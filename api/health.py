"""
CarbonLedger Health Check API

This module provides health check endpoints for monitoring and alerting.
Implements comprehensive health reporting for all system components.

Architectural Decision: Dedicated health endpoints
- Separate health checks from business logic
- Detailed component-level health reporting
- Support for different health check levels (basic vs comprehensive)
- Integration with monitoring and alerting systems
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse

from core.health import HealthChecker
from core.logging import get_logger


router = APIRouter()
logger = get_logger(__name__, component="HealthAPI")


@router.get("/", response_model=Dict[str, Any])
async def health_check_basic():
    """
    Basic health check endpoint.
    
    Returns a simple status indicating if the API is responsive.
    This endpoint is designed for load balancers and basic monitoring.
    """
    return {
        "status": "healthy",
        "message": "CarbonLedger API is operational"
    }


@router.get("/detailed", response_model=Dict[str, Any])
async def health_check_detailed(
    health_checker: HealthChecker = Depends()
):
    """
    Detailed health check endpoint.
    
    Performs comprehensive health checks on all system components
    and returns detailed status information.
    
    Returns:
        Detailed health status including component-level information
    """
    try:
        health_status = await health_checker.check_all()
        
        # Determine HTTP status code based on overall health
        if health_status["status"] == "healthy":
            status_code = status.HTTP_200_OK
        elif health_status["status"] == "degraded":
            status_code = status.HTTP_200_OK  # Still operational
        else:
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        
        return JSONResponse(
            status_code=status_code,
            content=health_status
        )
        
    except Exception as e:
        logger.error(
            "Health check failed",
            error=str(e),
            error_type=type(e).__name__
        )
        
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "message": "Health check system failure",
                "error": "Internal health check error"
            }
        )


@router.get("/ready", response_model=Dict[str, Any])
async def readiness_check(
    health_checker: HealthChecker = Depends()
):
    """
    Kubernetes-style readiness check.
    
    Indicates whether the application is ready to serve traffic.
    This endpoint should return 200 only when all critical components
    are operational.
    """
    try:
        health_status = await health_checker.check_all()
        
        # Consider the service ready only if healthy or degraded
        # (degraded means functional but with warnings)
        if health_status["status"] in ["healthy", "degraded"]:
            return {
                "status": "ready",
                "message": "Service is ready to accept traffic"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service is not ready"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Readiness check failed",
            error=str(e),
            error_type=type(e).__name__
        )
        
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Readiness check system failure"
        )


@router.get("/live", response_model=Dict[str, Any])
async def liveness_check():
    """
    Kubernetes-style liveness check.
    
    Indicates whether the application is alive and should not be restarted.
    This is a minimal check that only verifies the application is responsive.
    """
    return {
        "status": "alive",
        "message": "Service is alive and responsive"
    }
