"""
CarbonLedger Verification API

This module provides the core satellite verification endpoints with enhanced
security, validation, and comprehensive error handling.

Architectural Decision: Enhanced verification with comprehensive validation
- Input validation and sanitization
- Rate limiting per endpoint
- Comprehensive error handling and logging
- Detailed verification results with confidence scoring
- Database persistence for audit trails
"""

import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel, Field, validator
from slowapi import Limiter
from slowapi.util import get_remote_address

import ee
from sqlmodel import Session

from core.db import DatabaseManager
from core.earth_engine import EarthEngineManager, EarthEngineError, DataQualityError
from core.logging import get_logger, log_performance
from core.exceptions import ValidationError, ExternalServiceError
from models import Project, Verification


router = APIRouter()
logger = get_logger(__name__, component="VerificationAPI")

# Rate limiting will be handled by global middleware


class VerificationRequest(BaseModel):
    """
    Enhanced verification request model with comprehensive validation.
    
    Validates geographic coordinates and ensures reasonable bounding box sizes
    for satellite analysis.
    """
    
    project_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="Name of the project to verify"
    )
    
    project_description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Optional description of the project"
    )
    
    lon_min: float = Field(
        ...,
        ge=-180.0,
        le=180.0,
        description="Minimum longitude (western boundary)"
    )
    
    lat_min: float = Field(
        ...,
        ge=-90.0,
        le=90.0,
        description="Minimum latitude (southern boundary)"
    )
    
    lon_max: float = Field(
        ...,
        ge=-180.0,
        le=180.0,
        description="Maximum longitude (eastern boundary)"
    )
    
    lat_max: float = Field(
        ...,
        ge=-90.0,
        le=90.0,
        description="Maximum latitude (northern boundary)"
    )
    
    @validator('lon_max')
    def validate_longitude_order(cls, v, values):
        """Ensure longitude max is greater than min."""
        if 'lon_min' in values and v <= values['lon_min']:
            raise ValueError('lon_max must be greater than lon_min')
        return v
    
    @validator('lat_max')
    def validate_latitude_order(cls, v, values):
        """Ensure latitude max is greater than min."""
        if 'lat_min' in values and v <= values['lat_min']:
            raise ValueError('lat_max must be greater than lat_min')
        return v
    
    @validator('lon_max')
    def validate_area_size(cls, v, values):
        """Ensure the area is not too large for processing."""
        if all(key in values for key in ['lon_min', 'lat_min', 'lat_max']):
            # Calculate approximate area in square degrees
            width = v - values['lon_min']
            height = values['lat_max'] - values['lat_min']
            area = width * height
            
            # Limit to reasonable size (roughly 100km x 100km at equator)
            max_area = 1.0  # 1 square degree
            if area > max_area:
                raise ValueError(
                    f'Area too large for processing. '
                    f'Maximum area: {max_area} square degrees, '
                    f'requested: {area:.4f} square degrees'
                )
        return v


class VerificationResponse(BaseModel):
    """Enhanced verification response with comprehensive analysis results."""

    verification_id: str
    project_id: int
    project_name: str
    verification_complete: bool
    deforestation_detected: bool
    deforested_area_km2: float
    confidence_score: Optional[float] = None
    data_quality_score: Optional[float] = None
    verification_level: Optional[str] = None
    sources_used: Optional[int] = None
    vegetation_indices: Optional[Dict[str, Any]] = None
    temporal_analysis: Optional[Dict[str, Any]] = None
    area_checked: Dict[str, float]
    analysis_period: Dict[str, str]
    verified_at: datetime
    processing_time_seconds: float
    warnings: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


@router.post("/verify", response_model=VerificationResponse)
async def verify_project(
    request: Request,
    verification_request: VerificationRequest
):
    """
    Perform satellite-based deforestation verification for a project area.
    
    This endpoint analyzes satellite imagery to detect deforestation within
    the specified geographic boundaries using advanced NDVI analysis.
    
    Args:
        verification_request: Project details and geographic boundaries
    
    Returns:
        Detailed verification results with confidence scoring
    
    Raises:
        ValidationError: If input validation fails
        ExternalServiceError: If satellite data is unavailable
        HTTPException: For various error conditions
    """
    start_time = datetime.now(timezone.utc)
    verification_id = str(uuid.uuid4())

    logger.info(
        "Starting satellite verification",
        verification_id=verification_id,
        project_name=verification_request.project_name,
        area=f"({verification_request.lon_min}, {verification_request.lat_min}) to "
             f"({verification_request.lon_max}, {verification_request.lat_max})",
        operation="satellite_verification"
    )
    
    try:
        # Get managers from global instances
        from main import get_db_manager, get_ee_manager
        db_manager = get_db_manager()
        ee_manager = get_ee_manager()

        # Create project record in database
        with db_manager.get_session() as session:
            project = Project(
                name=verification_request.project_name,
                description=verification_request.project_description,
                lon_min=verification_request.lon_min,
                lat_min=verification_request.lat_min,
                lon_max=verification_request.lon_max,
                lat_max=verification_request.lat_max
            )
            session.add(project)
            session.commit()
            session.refresh(project)
            project_id = project.id
        
        logger.info(
            "Project created in database",
            verification_id=verification_id,
            project_id=project_id
        )
        
        # Perform enhanced satellite analysis
        deforestation_detected, deforested_area_km2, analysis_details = await _perform_enhanced_satellite_analysis(
            verification_request, ee_manager, verification_id
        )
        
        # Calculate processing time
        processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()
        
        # Create verification record
        with db_manager.get_session() as session:
            verification = Verification(
                project_id=project_id,
                deforestation_detected=deforestation_detected,
                deforested_area_km2=deforested_area_km2,
                verified_at=start_time
            )
            session.add(verification)
            session.commit()
            session.refresh(verification)
        
        logger.info(
            "Verification completed successfully",
            verification_id=verification_id,
            project_id=project_id,
            deforestation_detected=deforestation_detected,
            deforested_area_km2=deforested_area_km2,
            processing_time_seconds=processing_time
        )
        
        # Prepare enhanced response
        return VerificationResponse(
            verification_id=verification_id,
            project_id=project_id,
            project_name=verification_request.project_name,
            verification_complete=True,
            deforestation_detected=deforestation_detected,
            deforested_area_km2=deforested_area_km2,
            confidence_score=analysis_details.get("confidence_score"),
            data_quality_score=analysis_details.get("data_quality_score"),
            verification_level=analysis_details.get("verification_level"),
            sources_used=analysis_details.get("sources_used"),
            vegetation_indices=analysis_details.get("vegetation_indices"),
            temporal_analysis=analysis_details.get("temporal_analysis"),
            area_checked={
                "lon_min": verification_request.lon_min,
                "lat_min": verification_request.lat_min,
                "lon_max": verification_request.lon_max,
                "lat_max": verification_request.lat_max
            },
            analysis_period=analysis_details.get("analysis_period", {}),
            verified_at=start_time,
            processing_time_seconds=processing_time,
            warnings=analysis_details.get("warnings"),
            metadata=analysis_details.get("metadata")
        )
        
    except ValidationError:
        raise
    except EarthEngineError as e:
        logger.error(
            "Earth Engine error during verification",
            verification_id=verification_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise ExternalServiceError("Google Earth Engine", str(e))
    
    except Exception as e:
        logger.error(
            "Unexpected error during verification",
            verification_id=verification_id,
            error=str(e),
            error_type=type(e).__name__
        )
        
        # Try to save error to database
        try:
            if 'project_id' in locals():
                with db_manager.get_session() as session:
                    verification = Verification(
                        project_id=project_id,
                        deforestation_detected=False,
                        deforested_area_km2=0.0,
                        error_message=str(e),
                        verified_at=start_time
                    )
                    session.add(verification)
                    session.commit()
        except Exception as db_error:
            logger.error(
                "Failed to save error to database",
                verification_id=verification_id,
                db_error=str(db_error)
            )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Verification processing failed"
        )


async def _perform_enhanced_satellite_analysis(
    request: VerificationRequest,
    ee_manager: EarthEngineManager,
    verification_id: str
) -> tuple[bool, float, Dict[str, Any]]:
    """
    Perform enhanced satellite analysis using the new verification engine.

    Args:
        request: Verification request with coordinates
        ee_manager: Earth Engine manager
        verification_id: Unique verification ID for logging

    Returns:
        Tuple of (deforestation_detected, area_km2, analysis_details)
    """
    try:
        # Import the enhanced verification engine
        from core.ndvi import EnhancedVerificationEngine, VerificationLevel

        # Create geometry for analysis
        aoi = ee.Geometry.Rectangle([
            request.lon_min, request.lat_min,
            request.lon_max, request.lat_max
        ])

        logger.debug(
            "Created analysis geometry for enhanced verification",
            verification_id=verification_id,
            geometry_type="Rectangle"
        )

        # Create enhanced verification engine
        verification_engine = EnhancedVerificationEngine(ee_manager)

        # Determine verification level based on area size
        area_size = (request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)

        if area_size > 0.1:  # Large area
            verification_level = VerificationLevel.BASIC
        elif area_size > 0.01:  # Medium area
            verification_level = VerificationLevel.STANDARD
        else:  # Small area - can afford comprehensive analysis
            verification_level = VerificationLevel.COMPREHENSIVE

        logger.info(
            "Selected verification level",
            verification_id=verification_id,
            verification_level=verification_level.value,
            area_size=area_size
        )

        # Perform enhanced verification
        result = verification_engine.verify_deforestation(
            geometry=aoi,
            start_date="2023-01-01",
            end_date="2024-12-31",
            verification_level=verification_level
        )

        # Extract results
        deforestation_detected = result["deforestation_detected"]
        deforestation_area_km2 = result["deforestation_area_km2"]

        # Prepare analysis details for API response
        analysis_details = {
            "verification_level": verification_level.value,
            "confidence_score": result.get("confidence_score", 0.5),
            "data_quality_score": result.get("data_quality_score", 0.5),
            "sources_used": result.get("sources_used", 1),
            "vegetation_indices": result.get("vegetation_indices", {}),
            "analysis_period": {
                "before": "2023-01-01 to 2023-12-31",
                "after": "2024-01-01 to 2024-12-31"
            },
            "warnings": []
        }

        # Add temporal analysis if available
        if "temporal_analysis" in result:
            analysis_details["temporal_analysis"] = result["temporal_analysis"]

        # Generate warnings based on results
        if analysis_details["confidence_score"] < 0.7:
            analysis_details["warnings"].append(
                "Low confidence due to limited data availability or quality"
            )

        if analysis_details["data_quality_score"] < 0.8:
            analysis_details["warnings"].append(
                "Data quality concerns detected - results should be interpreted carefully"
            )

        if analysis_details["sources_used"] < 2 and verification_level != VerificationLevel.BASIC:
            analysis_details["warnings"].append(
                "Limited satellite data sources available for cross-validation"
            )

        # Add metadata about the analysis
        analysis_details["metadata"] = result.get("analysis_metadata", {})

        logger.info(
            "Enhanced satellite analysis completed",
            verification_id=verification_id,
            deforestation_detected=deforestation_detected,
            area_km2=deforestation_area_km2,
            confidence_score=analysis_details["confidence_score"],
            data_quality_score=analysis_details["data_quality_score"],
            verification_level=verification_level.value,
            sources_used=analysis_details["sources_used"]
        )

        return deforestation_detected, deforestation_area_km2, analysis_details

    except DataQualityError as e:
        logger.warning(
            "Data quality insufficient for enhanced analysis",
            verification_id=verification_id,
            error=str(e)
        )
        raise

    except Exception as e:
        logger.error(
            "Enhanced satellite analysis failed",
            verification_id=verification_id,
            error=str(e),
            error_type=type(e).__name__
        )
        raise EarthEngineError(f"Enhanced satellite analysis failed: {e}")


# Legacy helper functions removed - now handled by EnhancedVerificationEngine
