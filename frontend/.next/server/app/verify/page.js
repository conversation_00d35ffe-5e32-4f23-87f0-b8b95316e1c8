/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/verify/page";
exports.ids = ["app/verify/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fverify%2Fpage&page=%2Fverify%2Fpage&appPaths=%2Fverify%2Fpage&pagePath=private-next-app-dir%2Fverify%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fverify%2Fpage&page=%2Fverify%2Fpage&appPaths=%2Fverify%2Fpage&pagePath=private-next-app-dir%2Fverify%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'verify',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/verify/page.tsx */ \"(rsc)/./src/app/verify/page.tsx\")), \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/cl/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/verify/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/verify/page\",\n        pathname: \"/verify\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fverify%2Fpage&page=%2Fverify%2Fpage&appPaths=%2Fverify%2Fpage&pagePath=private-next-app-dir%2Fverify%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnMuY2h5JTJGY2wlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRnJlYWN0LWhvdC10b2FzdCUyRmRpc3QlMkZpbmRleC5tanMmbW9kdWxlcz0lMkZVc2VycyUyRnMuY2h5JTJGY2wlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NhcmJvbmxlZGdlci1mcm9udGVuZC8/NzRjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zLmNoeS9jbC9mcm9udGVuZC9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fverify%2Fpage.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fverify%2Fpage.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/verify/page.tsx */ \"(ssr)/./src/app/verify/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZ2ZXJpZnklMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvPzRhYzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcy5jaHkvY2wvZnJvbnRlbmQvc3JjL2FwcC92ZXJpZnkvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fverify%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/verify/page.tsx":
/*!*********************************!*\
  !*** ./src/app/verify/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerifyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _components_Verification_VerificationForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Verification/VerificationForm */ \"(ssr)/./src/components/Verification/VerificationForm.tsx\");\n/* harmony import */ var _components_Verification_VerificationResults__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Verification/VerificationResults */ \"(ssr)/./src/components/Verification/VerificationResults.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction VerifyPage() {\n    const [verificationResult, setVerificationResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVerifying, setIsVerifying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleVerificationComplete = (result)=>{\n        setVerificationResult(result);\n        setIsVerifying(false);\n    };\n    const handleVerificationStart = ()=>{\n        setIsVerifying(true);\n        setVerificationResult(null);\n    };\n    const handleNewVerification = ()=>{\n        setVerificationResult(null);\n        setIsVerifying(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Project Verification\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-gray-600\",\n                                children: \"Verify carbon credit projects using advanced satellite imagery analysis\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Verification_VerificationForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onVerificationStart: handleVerificationStart,\n                                    onVerificationComplete: handleVerificationComplete,\n                                    isVerifying: isVerifying,\n                                    disabled: isVerifying\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.4\n                                },\n                                children: [\n                                    isVerifying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"Analyzing Satellite Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Processing satellite imagery and detecting deforestation patterns...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 bg-gray-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary-600 h-2 rounded-full animate-pulse\",\n                                                        style: {\n                                                            width: \"60%\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-2\",\n                                                    children: \"This may take up to 2 minutes\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this),\n                                    verificationResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Verification_VerificationResults__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        result: verificationResult,\n                                        onNewVerification: handleNewVerification\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isVerifying && !verificationResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    strokeWidth: 1.5,\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0.621 0 1.125-.504 1.125-1.125V9.375c0-.621.504-1.125 1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"Ready for Verification\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Fill out the form to start analyzing your carbon credit project\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.6\n                        },\n                        className: \"mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"How Satellite Verification Works\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100 mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-6 w-6 text-primary-600\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        strokeWidth: 1.5,\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            d: \"M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"Satellite Data Collection\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"We analyze high-resolution satellite imagery from multiple sources including Landsat and Sentinel\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100 mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-6 w-6 text-primary-600\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        strokeWidth: 1.5,\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            d: \"M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.611L5 14.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"AI-Powered Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Advanced machine learning algorithms detect deforestation patterns and vegetation changes\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100 mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-6 w-6 text-primary-600\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        strokeWidth: 1.5,\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            d: \"M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"Verification Report\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Comprehensive reports with confidence scores and detailed analysis for carbon credit validation\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/verify/page.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3ZlcmlmeS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQ3VCO0FBQ2tCO0FBQ007QUFDekM7QUFHeEIsU0FBU0s7SUFDdEIsTUFBTSxDQUFDQyxvQkFBb0JDLHNCQUFzQixHQUFHUCwrQ0FBUUEsQ0FBOEI7SUFDMUYsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUFDO0lBRS9DLE1BQU1VLDZCQUE2QixDQUFDQztRQUNsQ0osc0JBQXNCSTtRQUN0QkYsZUFBZTtJQUNqQjtJQUVBLE1BQU1HLDBCQUEwQjtRQUM5QkgsZUFBZTtRQUNmRixzQkFBc0I7SUFDeEI7SUFFQSxNQUFNTSx3QkFBd0I7UUFDNUJOLHNCQUFzQjtRQUN0QkUsZUFBZTtJQUNqQjtJQUVBLHFCQUNFLDhEQUFDUixxRUFBVUE7a0JBQ1QsNEVBQUNhO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ1gsaURBQU1BLENBQUNVLEdBQUc7d0JBQ1RFLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUc7d0JBQzdCQyxTQUFTOzRCQUFFRixTQUFTOzRCQUFHQyxHQUFHO3dCQUFFO3dCQUM1QkUsWUFBWTs0QkFBRUMsVUFBVTt3QkFBSTt3QkFDNUJOLFdBQVU7OzBDQUVWLDhEQUFDTztnQ0FBR1AsV0FBVTswQ0FBbUM7Ozs7OzswQ0FDakQsOERBQUNRO2dDQUFFUixXQUFVOzBDQUFxQjs7Ozs7Ozs7Ozs7O2tDQU1wQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDWCxpREFBTUEsQ0FBQ1UsR0FBRztnQ0FDVEUsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR08sR0FBRyxDQUFDO2dDQUFHO2dDQUM5QkwsU0FBUztvQ0FBRUYsU0FBUztvQ0FBR08sR0FBRztnQ0FBRTtnQ0FDNUJKLFlBQVk7b0NBQUVDLFVBQVU7b0NBQUtJLE9BQU87Z0NBQUk7MENBRXhDLDRFQUFDdkIsaUZBQWdCQTtvQ0FDZndCLHFCQUFxQmQ7b0NBQ3JCZSx3QkFBd0JqQjtvQ0FDeEJGLGFBQWFBO29DQUNib0IsVUFBVXBCOzs7Ozs7Ozs7OzswQ0FLZCw4REFBQ0osaURBQU1BLENBQUNVLEdBQUc7Z0NBQ1RFLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdPLEdBQUc7Z0NBQUc7Z0NBQzdCTCxTQUFTO29DQUFFRixTQUFTO29DQUFHTyxHQUFHO2dDQUFFO2dDQUM1QkosWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBS0ksT0FBTztnQ0FBSTs7b0NBRXZDakIsNkJBQ0MsOERBQUNNO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUNjO29EQUFHZCxXQUFVOzhEQUF5Qzs7Ozs7OzhEQUd2RCw4REFBQ1E7b0RBQUVSLFdBQVU7OERBQWdCOzs7Ozs7OERBRzdCLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7d0RBQWdEZSxPQUFPOzREQUFFQyxPQUFPO3dEQUFNOzs7Ozs7Ozs7Ozs4REFFdkYsOERBQUNSO29EQUFFUixXQUFVOzhEQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBTy9DVCxvQ0FDQyw4REFBQ0gsb0ZBQW1CQTt3Q0FDbEJRLFFBQVFMO3dDQUNSMEIsbUJBQW1CbkI7Ozs7OztvQ0FJdEIsQ0FBQ0wsZUFBZSxDQUFDRixvQ0FDaEIsOERBQUNRO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNrQjtvREFDQ2xCLFdBQVU7b0RBQ1ZtQixNQUFLO29EQUNMQyxTQUFRO29EQUNSQyxhQUFhO29EQUNiQyxRQUFPOzhEQUVQLDRFQUFDQzt3REFDQ0MsZUFBYzt3REFDZEMsZ0JBQWU7d0RBQ2ZDLEdBQUU7Ozs7Ozs7Ozs7OzhEQUdOLDhEQUFDWjtvREFBR2QsV0FBVTs4REFBeUM7Ozs7Ozs4REFHdkQsOERBQUNRO29EQUFFUixXQUFVOzhEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBVXZDLDhEQUFDWCxpREFBTUEsQ0FBQ1UsR0FBRzt3QkFDVEUsU0FBUzs0QkFBRUMsU0FBUzs0QkFBR0MsR0FBRzt3QkFBRzt3QkFDN0JDLFNBQVM7NEJBQUVGLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUU7d0JBQzVCRSxZQUFZOzRCQUFFQyxVQUFVOzRCQUFLSSxPQUFPO3dCQUFJO3dCQUN4Q1YsV0FBVTtrQ0FFViw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDMkI7b0NBQUczQixXQUFVOzhDQUEyQzs7Ozs7OzhDQUd6RCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDa0I7d0RBQ0NsQixXQUFVO3dEQUNWbUIsTUFBSzt3REFDTEMsU0FBUTt3REFDUkMsYUFBYTt3REFDYkMsUUFBTztrRUFFUCw0RUFBQ0M7NERBQ0NDLGVBQWM7NERBQ2RDLGdCQUFlOzREQUNmQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUlSLDhEQUFDWjtvREFBR2QsV0FBVTs4REFBeUM7Ozs7Ozs4REFHdkQsOERBQUNRO29EQUFFUixXQUFVOzhEQUFnQjs7Ozs7Ozs7Ozs7O3NEQUkvQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ2tCO3dEQUNDbEIsV0FBVTt3REFDVm1CLE1BQUs7d0RBQ0xDLFNBQVE7d0RBQ1JDLGFBQWE7d0RBQ2JDLFFBQU87a0VBRVAsNEVBQUNDOzREQUNDQyxlQUFjOzREQUNkQyxnQkFBZTs0REFDZkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJUiw4REFBQ1o7b0RBQUdkLFdBQVU7OERBQXlDOzs7Ozs7OERBR3ZELDhEQUFDUTtvREFBRVIsV0FBVTs4REFBZ0I7Ozs7Ozs7Ozs7OztzREFJL0IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNrQjt3REFDQ2xCLFdBQVU7d0RBQ1ZtQixNQUFLO3dEQUNMQyxTQUFRO3dEQUNSQyxhQUFhO3dEQUNiQyxRQUFPO2tFQUVQLDRFQUFDQzs0REFDQ0MsZUFBYzs0REFDZEMsZ0JBQWU7NERBQ2ZDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSVIsOERBQUNaO29EQUFHZCxXQUFVOzhEQUF5Qzs7Ozs7OzhEQUd2RCw4REFBQ1E7b0RBQUVSLFdBQVU7OERBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXL0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvLi9zcmMvYXBwL3ZlcmlmeS9wYWdlLnRzeD82NTBkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTWFpbkxheW91dCBmcm9tICdAL2NvbXBvbmVudHMvTGF5b3V0L01haW5MYXlvdXQnO1xuaW1wb3J0IFZlcmlmaWNhdGlvbkZvcm0gZnJvbSAnQC9jb21wb25lbnRzL1ZlcmlmaWNhdGlvbi9WZXJpZmljYXRpb25Gb3JtJztcbmltcG9ydCBWZXJpZmljYXRpb25SZXN1bHRzIGZyb20gJ0AvY29tcG9uZW50cy9WZXJpZmljYXRpb24vVmVyaWZpY2F0aW9uUmVzdWx0cyc7XG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB0eXBlIHsgVmVyaWZpY2F0aW9uUmVzcG9uc2UgfSBmcm9tICdAL3R5cGVzL2FwaSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFZlcmlmeVBhZ2UoKSB7XG4gIGNvbnN0IFt2ZXJpZmljYXRpb25SZXN1bHQsIHNldFZlcmlmaWNhdGlvblJlc3VsdF0gPSB1c2VTdGF0ZTxWZXJpZmljYXRpb25SZXNwb25zZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNWZXJpZnlpbmcsIHNldElzVmVyaWZ5aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBoYW5kbGVWZXJpZmljYXRpb25Db21wbGV0ZSA9IChyZXN1bHQ6IFZlcmlmaWNhdGlvblJlc3BvbnNlKSA9PiB7XG4gICAgc2V0VmVyaWZpY2F0aW9uUmVzdWx0KHJlc3VsdCk7XG4gICAgc2V0SXNWZXJpZnlpbmcoZmFsc2UpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVZlcmlmaWNhdGlvblN0YXJ0ID0gKCkgPT4ge1xuICAgIHNldElzVmVyaWZ5aW5nKHRydWUpO1xuICAgIHNldFZlcmlmaWNhdGlvblJlc3VsdChudWxsKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVOZXdWZXJpZmljYXRpb24gPSAoKSA9PiB7XG4gICAgc2V0VmVyaWZpY2F0aW9uUmVzdWx0KG51bGwpO1xuICAgIHNldElzVmVyaWZ5aW5nKGZhbHNlKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxNYWluTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBtYXgtdy03eGwgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICB7LyogUGFnZSBoZWFkZXIgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41IH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtYi04XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5Qcm9qZWN0IFZlcmlmaWNhdGlvbjwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgVmVyaWZ5IGNhcmJvbiBjcmVkaXQgcHJvamVjdHMgdXNpbmcgYWR2YW5jZWQgc2F0ZWxsaXRlIGltYWdlcnkgYW5hbHlzaXNcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogTWFpbiBjb250ZW50ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtOFwiPlxuICAgICAgICAgICAgey8qIExlZnQgY29sdW1uIC0gRm9ybSAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjIgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFZlcmlmaWNhdGlvbkZvcm1cbiAgICAgICAgICAgICAgICBvblZlcmlmaWNhdGlvblN0YXJ0PXtoYW5kbGVWZXJpZmljYXRpb25TdGFydH1cbiAgICAgICAgICAgICAgICBvblZlcmlmaWNhdGlvbkNvbXBsZXRlPXtoYW5kbGVWZXJpZmljYXRpb25Db21wbGV0ZX1cbiAgICAgICAgICAgICAgICBpc1ZlcmlmeWluZz17aXNWZXJpZnlpbmd9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzVmVyaWZ5aW5nfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgICB7LyogUmlnaHQgY29sdW1uIC0gUmVzdWx0cyAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuNCB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNWZXJpZnlpbmcgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLXByaW1hcnktNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBBbmFseXppbmcgU2F0ZWxsaXRlIERhdGFcbiAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIFByb2Nlc3Npbmcgc2F0ZWxsaXRlIGltYWdlcnkgYW5kIGRldGVjdGluZyBkZWZvcmVzdGF0aW9uIHBhdHRlcm5zLi4uXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXByaW1hcnktNjAwIGgtMiByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiIHN0eWxlPXt7IHdpZHRoOiAnNjAlJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgVGhpcyBtYXkgdGFrZSB1cCB0byAyIG1pbnV0ZXNcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAge3ZlcmlmaWNhdGlvblJlc3VsdCAmJiAoXG4gICAgICAgICAgICAgICAgPFZlcmlmaWNhdGlvblJlc3VsdHNcbiAgICAgICAgICAgICAgICAgIHJlc3VsdD17dmVyaWZpY2F0aW9uUmVzdWx0fVxuICAgICAgICAgICAgICAgICAgb25OZXdWZXJpZmljYXRpb249e2hhbmRsZU5ld1ZlcmlmaWNhdGlvbn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHshaXNWZXJpZnlpbmcgJiYgIXZlcmlmaWNhdGlvblJlc3VsdCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIlxuICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17MS41fVxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk05IDEyaDMuNzVNOSAxNWgzLjc1TTkgMThoMy43NW0zIC43NUgxOGEyLjI1IDIuMjUgMCAwMDIuMjUtMi4yNVY2LjEwOGMwLTEuMTM1LS44NDUtMi4wOTgtMS45NzYtMi4xOTJhNDguNDI0IDQ4LjQyNCAwIDAwLTEuMTIzLS4wOG0tNS44MDEgMGMtLjA2NS4yMS0uMS40MzMtLjEuNjY0IDAgLjQxNC4zMzYuNzUuNzUuNzVoNC41YS43NS43NSAwIDAwLjc1LS43NSAyLjI1IDIuMjUgMCAwMC0uMS0uNjY0bS01LjggMEEyLjI1MSAyLjI1MSAwIDAxMTMuNSAyLjI1SDE1YzEuMDEyIDAgMS44NjcuNjY4IDIuMTUgMS41ODZtLTUuOCAwYy0uMzc2LjAyMy0uNzUuMDUtMS4xMjQuMDhDOS4wOTUgNC4wMSA4LjI1IDQuOTczIDguMjUgNi4xMDhWOC4yNW0wIDBINC44NzVjLS42MjEgMC0xLjEyNS41MDQtMS4xMjUgMS4xMjV2MTEuMjVjMC42MjEgMCAxLjEyNS0uNTA0IDEuMTI1LTEuMTI1VjkuMzc1YzAtLjYyMS41MDQtMS4xMjUgMS4xMjUtMS4xMjVIOC4yNXpNNi43NSAxMmguMDA4di4wMDhINi43NVYxMnptMCAzaC4wMDh2LjAwOEg2Ljc1VjE1em0wIDNoLjAwOHYuMDA4SDYuNzVWMTh6XCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgUmVhZHkgZm9yIFZlcmlmaWNhdGlvblxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgRmlsbCBvdXQgdGhlIGZvcm0gdG8gc3RhcnQgYW5hbHl6aW5nIHlvdXIgY2FyYm9uIGNyZWRpdCBwcm9qZWN0XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEluZm9ybWF0aW9uIHNlY3Rpb24gKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC42IH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xMlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkXCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgSG93IFNhdGVsbGl0ZSBWZXJpZmljYXRpb24gV29ya3NcbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtMTIgdy0xMiBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1sZyBiZy1wcmltYXJ5LTEwMCBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wcmltYXJ5LTYwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsxLjV9XG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTEyIDIxYTkuMDA0IDkuMDA0IDAgMDA4LjcxNi02Ljc0N00xMiAyMWE5LjAwNCA5LjAwNCAwIDAxLTguNzE2LTYuNzQ3TTEyIDIxYzIuNDg1IDAgNC41LTQuMDMgNC41LTlTMTQuNDg1IDMgMTIgM3MtNC41IDQuMDMtNC41IDkgMi4wMTUgOSA0LjUgOXpcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgU2F0ZWxsaXRlIERhdGEgQ29sbGVjdGlvblxuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgV2UgYW5hbHl6ZSBoaWdoLXJlc29sdXRpb24gc2F0ZWxsaXRlIGltYWdlcnkgZnJvbSBtdWx0aXBsZSBzb3VyY2VzIGluY2x1ZGluZyBMYW5kc2F0IGFuZCBTZW50aW5lbFxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLTEyIHctMTIgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbGcgYmctcHJpbWFyeS0xMDAgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtcHJpbWFyeS02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17MS41fVxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk05Ljc1IDMuMTA0djUuNzE0YTIuMjUgMi4yNSAwIDAxLS42NTkgMS41OTFMNSAxNC41TTkuNzUgMy4xMDRjLS4yNTEuMDIzLS41MDEuMDUtLjc1LjA4Mm0uNzUtLjA4MmEyNC4zMDEgMjQuMzAxIDAgMDE0LjUgMG0wIDB2NS43MTRjMCAuNTk3LjIzNyAxLjE3LjY1OSAxLjU5MUwxOS44IDE1LjNNMTQuMjUgMy4xMDRjLjI1MS4wMjMuNTAxLjA1Ljc1LjA4Mk0xOS44IDE1LjNsLTEuNTcuMzkzQTkuMDY1IDkuMDY1IDAgMDExMiAxNWE5LjA2NSA5LjA2NSAwIDAwLTYuMjMtLjY5M0w1IDE0LjVtMTQuOC44bDEuNDAyIDEuNDAyYzEuMjMyIDEuMjMyLjY1IDMuMzE4LTEuMDY3IDMuNjExQTQ4LjMwOSA0OC4zMDkgMCAwMTEyIDIxYy0yLjc3MyAwLTUuNDkxLS4yMzUtOC4xMzUtLjY4Ny0xLjcxOC0uMjkzLTIuMy0yLjM3OS0xLjA2Ny0zLjYxMUw1IDE0LjVcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgQUktUG93ZXJlZCBBbmFseXNpc1xuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgQWR2YW5jZWQgbWFjaGluZSBsZWFybmluZyBhbGdvcml0aG1zIGRldGVjdCBkZWZvcmVzdGF0aW9uIHBhdHRlcm5zIGFuZCB2ZWdldGF0aW9uIGNoYW5nZXNcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC0xMiB3LTEyIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWxnIGJnLXByaW1hcnktMTAwIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXByaW1hcnktNjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezEuNX1cbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNOSAxMi43NUwxMS4yNSAxNSAxNSA5Ljc1TTIxIDEyYzAgMS4yNjgtLjYzIDIuMzktMS41OTMgMy4wNjhhMy43NDUgMy43NDUgMCAwMS0xLjA0MyAzLjI5NiAzLjc0NSAzLjc0NSAwIDAxLTMuMjk2IDEuMDQzQTMuNzQ1IDMuNzQ1IDAgMDExMiAyMWMtMS4yNjggMC0yLjM5LS42My0zLjA2OC0xLjU5M2EzLjc0NiAzLjc0NiAwIDAxLTMuMjk2LTEuMDQzIDMuNzQ1IDMuNzQ1IDAgMDEtMS4wNDMtMy4yOTZBMy43NDUgMy43NDUgMCAwMTMgMTJjMC0xLjI2OC42My0yLjM5IDEuNTkzLTMuMDY4YTMuNzQ1IDMuNzQ1IDAgMDExLjA0My0zLjI5NiAzLjc0NiAzLjc0NiAwIDAxMy4yOTYtMS4wNDNBMy43NDYgMy43NDYgMCAwMTEyIDNjMS4yNjggMCAyLjM5LjYzIDMuMDY4IDEuNTkzYTMuNzQ2IDMuNzQ2IDAgMDEzLjI5NiAxLjA0MyAzLjc0NiAzLjc0NiAwIDAxMS4wNDMgMy4yOTZBMy43NDUgMy43NDUgMCAwMTIxIDEyelwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICBWZXJpZmljYXRpb24gUmVwb3J0XG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICBDb21wcmVoZW5zaXZlIHJlcG9ydHMgd2l0aCBjb25maWRlbmNlIHNjb3JlcyBhbmQgZGV0YWlsZWQgYW5hbHlzaXMgZm9yIGNhcmJvbiBjcmVkaXQgdmFsaWRhdGlvblxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L01haW5MYXlvdXQ+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJNYWluTGF5b3V0IiwiVmVyaWZpY2F0aW9uRm9ybSIsIlZlcmlmaWNhdGlvblJlc3VsdHMiLCJtb3Rpb24iLCJWZXJpZnlQYWdlIiwidmVyaWZpY2F0aW9uUmVzdWx0Iiwic2V0VmVyaWZpY2F0aW9uUmVzdWx0IiwiaXNWZXJpZnlpbmciLCJzZXRJc1ZlcmlmeWluZyIsImhhbmRsZVZlcmlmaWNhdGlvbkNvbXBsZXRlIiwicmVzdWx0IiwiaGFuZGxlVmVyaWZpY2F0aW9uU3RhcnQiLCJoYW5kbGVOZXdWZXJpZmljYXRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJpbml0aWFsIiwib3BhY2l0eSIsInkiLCJhbmltYXRlIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiaDEiLCJwIiwieCIsImRlbGF5Iiwib25WZXJpZmljYXRpb25TdGFydCIsIm9uVmVyaWZpY2F0aW9uQ29tcGxldGUiLCJkaXNhYmxlZCIsImgzIiwic3R5bGUiLCJ3aWR0aCIsIm9uTmV3VmVyaWZpY2F0aW9uIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJkIiwiaDIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/verify/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst footerLinks = {\n    product: [\n        {\n            name: \"Features\",\n            href: \"#\"\n        },\n        {\n            name: \"Pricing\",\n            href: \"#\"\n        },\n        {\n            name: \"API Documentation\",\n            href: \"/docs\"\n        },\n        {\n            name: \"System Status\",\n            href: \"/health\"\n        }\n    ],\n    company: [\n        {\n            name: \"About\",\n            href: \"#\"\n        },\n        {\n            name: \"Blog\",\n            href: \"#\"\n        },\n        {\n            name: \"Careers\",\n            href: \"#\"\n        },\n        {\n            name: \"Contact\",\n            href: \"#\"\n        }\n    ],\n    resources: [\n        {\n            name: \"Help Center\",\n            href: \"#\"\n        },\n        {\n            name: \"Privacy Policy\",\n            href: \"#\"\n        },\n        {\n            name: \"Terms of Service\",\n            href: \"#\"\n        },\n        {\n            name: \"Security\",\n            href: \"#\"\n        }\n    ],\n    social: [\n        {\n            name: \"GitHub\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"Twitter\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-8 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.product.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Company\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.company.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.resources.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Connect\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex space-x-4\",\n                                    children: footerLinks.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: \"text-gray-400 hover:text-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"h-6 w-6\",\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-6 md:flex md:items-center md:justify-between lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-6 w-6 items-center justify-center rounded bg-primary-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 text-white\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: 1.5,\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            d: \"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"CarbonLedger\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 md:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"\\xa9 2024 CarbonLedger. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Made with\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-3 w-3 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"for the planet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRTZCO0FBQ3lCO0FBRXRELE1BQU1FLGNBQWM7SUFDbEJDLFNBQVM7UUFDUDtZQUFFQyxNQUFNO1lBQVlDLE1BQU07UUFBSTtRQUM5QjtZQUFFRCxNQUFNO1lBQVdDLE1BQU07UUFBSTtRQUM3QjtZQUFFRCxNQUFNO1lBQXFCQyxNQUFNO1FBQVE7UUFDM0M7WUFBRUQsTUFBTTtZQUFpQkMsTUFBTTtRQUFVO0tBQzFDO0lBQ0RDLFNBQVM7UUFDUDtZQUFFRixNQUFNO1lBQVNDLE1BQU07UUFBSTtRQUMzQjtZQUFFRCxNQUFNO1lBQVFDLE1BQU07UUFBSTtRQUMxQjtZQUFFRCxNQUFNO1lBQVdDLE1BQU07UUFBSTtRQUM3QjtZQUFFRCxNQUFNO1lBQVdDLE1BQU07UUFBSTtLQUM5QjtJQUNERSxXQUFXO1FBQ1Q7WUFBRUgsTUFBTTtZQUFlQyxNQUFNO1FBQUk7UUFDakM7WUFBRUQsTUFBTTtZQUFrQkMsTUFBTTtRQUFJO1FBQ3BDO1lBQUVELE1BQU07WUFBb0JDLE1BQU07UUFBSTtRQUN0QztZQUFFRCxNQUFNO1lBQVlDLE1BQU07UUFBSTtLQUMvQjtJQUNERyxRQUFRO1FBQ047WUFDRUosTUFBTTtZQUNOQyxNQUFNO1lBQ05JLE1BQU0sQ0FBQ0Msc0JBQ0wsOERBQUNDO29CQUFJQyxNQUFLO29CQUFlQyxTQUFRO29CQUFhLEdBQUdILEtBQUs7OEJBQ3BELDRFQUFDSTt3QkFDQ0MsVUFBUzt3QkFDVEMsR0FBRTt3QkFDRkMsVUFBUzs7Ozs7Ozs7Ozs7UUFJakI7UUFDQTtZQUNFYixNQUFNO1lBQ05DLE1BQU07WUFDTkksTUFBTSxDQUFDQyxzQkFDTCw4REFBQ0M7b0JBQUlDLE1BQUs7b0JBQWVDLFNBQVE7b0JBQWEsR0FBR0gsS0FBSzs4QkFDcEQsNEVBQUNJO3dCQUFLRSxHQUFFOzs7Ozs7Ozs7OztRQUdkO1FBQ0E7WUFDRVosTUFBTTtZQUNOQyxNQUFNO1lBQ05JLE1BQU0sQ0FBQ0Msc0JBQ0wsOERBQUNDO29CQUFJQyxNQUFLO29CQUFlQyxTQUFRO29CQUFhLEdBQUdILEtBQUs7OEJBQ3BELDRFQUFDSTt3QkFBS0UsR0FBRTs7Ozs7Ozs7Ozs7UUFHZDtLQUNEO0FBQ0g7QUFFZSxTQUFTRTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTs7MEJBQ2hCLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDQzs7OENBQ0MsOERBQUNDO29DQUFHRixXQUFVOzhDQUFnRDs7Ozs7OzhDQUM5RCw4REFBQ0c7b0NBQUdDLE1BQUs7b0NBQU9KLFdBQVU7OENBQ3ZCbEIsWUFBWUMsT0FBTyxDQUFDc0IsR0FBRyxDQUFDLENBQUNDLHFCQUN4Qiw4REFBQ0M7c0RBQ0MsNEVBQUMzQixrREFBSUE7Z0RBQ0hLLE1BQU1xQixLQUFLckIsSUFBSTtnREFDZmUsV0FBVTswREFFVE0sS0FBS3RCLElBQUk7Ozs7OzsyQ0FMTHNCLEtBQUt0QixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7O3NDQVd4Qiw4REFBQ2lCOzs4Q0FDQyw4REFBQ0M7b0NBQUdGLFdBQVU7OENBQWdEOzs7Ozs7OENBQzlELDhEQUFDRztvQ0FBR0MsTUFBSztvQ0FBT0osV0FBVTs4Q0FDdkJsQixZQUFZSSxPQUFPLENBQUNtQixHQUFHLENBQUMsQ0FBQ0MscUJBQ3hCLDhEQUFDQztzREFDQyw0RUFBQzNCLGtEQUFJQTtnREFDSEssTUFBTXFCLEtBQUtyQixJQUFJO2dEQUNmZSxXQUFVOzBEQUVUTSxLQUFLdEIsSUFBSTs7Ozs7OzJDQUxMc0IsS0FBS3RCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBV3hCLDhEQUFDaUI7OzhDQUNDLDhEQUFDQztvQ0FBR0YsV0FBVTs4Q0FBZ0Q7Ozs7Ozs4Q0FDOUQsOERBQUNHO29DQUFHQyxNQUFLO29DQUFPSixXQUFVOzhDQUN2QmxCLFlBQVlLLFNBQVMsQ0FBQ2tCLEdBQUcsQ0FBQyxDQUFDQyxxQkFDMUIsOERBQUNDO3NEQUNDLDRFQUFDM0Isa0RBQUlBO2dEQUNISyxNQUFNcUIsS0FBS3JCLElBQUk7Z0RBQ2ZlLFdBQVU7MERBRVRNLEtBQUt0QixJQUFJOzs7Ozs7MkNBTExzQixLQUFLdEIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FXeEIsOERBQUNpQjs7OENBQ0MsOERBQUNDO29DQUFHRixXQUFVOzhDQUFnRDs7Ozs7OzhDQUM5RCw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ1psQixZQUFZTSxNQUFNLENBQUNpQixHQUFHLENBQUMsQ0FBQ0MscUJBQ3ZCLDhEQUFDMUIsa0RBQUlBOzRDQUVISyxNQUFNcUIsS0FBS3JCLElBQUk7NENBQ2ZlLFdBQVU7OzhEQUVWLDhEQUFDUTtvREFBS1IsV0FBVTs4REFBV00sS0FBS3RCLElBQUk7Ozs7Ozs4REFDcEMsOERBQUNzQixLQUFLakIsSUFBSTtvREFBQ1csV0FBVTtvREFBVVMsZUFBWTs7Ozs7OzsyQ0FMdENILEtBQUt0QixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBYTFCLDhEQUFDaUI7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ1Q7d0NBQ0NTLFdBQVU7d0NBQ1ZSLE1BQUs7d0NBQ0xDLFNBQVE7d0NBQ1JpQixhQUFhO3dDQUNiQyxRQUFPO2tEQUVQLDRFQUFDakI7NENBQ0NrQixlQUFjOzRDQUNkQyxnQkFBZTs0Q0FDZmpCLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSVIsOERBQUNZO29DQUFLUixXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7O3NDQUcxQyw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDYztvQ0FBRWQsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FHckMsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ1E7c0RBQUs7Ozs7OztzREFDTiw4REFBQzNCLGlHQUFTQTs0Q0FBQ21CLFdBQVU7Ozs7OztzREFDckIsOERBQUNRO3NEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9wQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NhcmJvbmxlZGdlci1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL0xheW91dC9Gb290ZXIudHN4P2Q4NjAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgSGVhcnRJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9zb2xpZCc7XG5cbmNvbnN0IGZvb3RlckxpbmtzID0ge1xuICBwcm9kdWN0OiBbXG4gICAgeyBuYW1lOiAnRmVhdHVyZXMnLCBocmVmOiAnIycgfSxcbiAgICB7IG5hbWU6ICdQcmljaW5nJywgaHJlZjogJyMnIH0sXG4gICAgeyBuYW1lOiAnQVBJIERvY3VtZW50YXRpb24nLCBocmVmOiAnL2RvY3MnIH0sXG4gICAgeyBuYW1lOiAnU3lzdGVtIFN0YXR1cycsIGhyZWY6ICcvaGVhbHRoJyB9LFxuICBdLFxuICBjb21wYW55OiBbXG4gICAgeyBuYW1lOiAnQWJvdXQnLCBocmVmOiAnIycgfSxcbiAgICB7IG5hbWU6ICdCbG9nJywgaHJlZjogJyMnIH0sXG4gICAgeyBuYW1lOiAnQ2FyZWVycycsIGhyZWY6ICcjJyB9LFxuICAgIHsgbmFtZTogJ0NvbnRhY3QnLCBocmVmOiAnIycgfSxcbiAgXSxcbiAgcmVzb3VyY2VzOiBbXG4gICAgeyBuYW1lOiAnSGVscCBDZW50ZXInLCBocmVmOiAnIycgfSxcbiAgICB7IG5hbWU6ICdQcml2YWN5IFBvbGljeScsIGhyZWY6ICcjJyB9LFxuICAgIHsgbmFtZTogJ1Rlcm1zIG9mIFNlcnZpY2UnLCBocmVmOiAnIycgfSxcbiAgICB7IG5hbWU6ICdTZWN1cml0eScsIGhyZWY6ICcjJyB9LFxuICBdLFxuICBzb2NpYWw6IFtcbiAgICB7XG4gICAgICBuYW1lOiAnR2l0SHViJyxcbiAgICAgIGhyZWY6ICcjJyxcbiAgICAgIGljb246IChwcm9wczogYW55KSA9PiAoXG4gICAgICAgIDxzdmcgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgZD1cIk0xMiAyQzYuNDc3IDIgMiA2LjQ4NCAyIDEyLjAxN2MwIDQuNDI1IDIuODY1IDguMTggNi44MzkgOS41MDQuNS4wOTIuNjgyLS4yMTcuNjgyLS40ODMgMC0uMjM3LS4wMDgtLjg2OC0uMDEzLTEuNzAzLTIuNzgyLjYwNS0zLjM2OS0xLjM0My0zLjM2OS0xLjM0My0uNDU0LTEuMTU4LTEuMTEtMS40NjYtMS4xMS0xLjQ2Ni0uOTA4LS42Mi4wNjktLjYwOC4wNjktLjYwOCAxLjAwMy4wNyAxLjUzMSAxLjAzMiAxLjUzMSAxLjAzMi44OTIgMS41MyAyLjM0MSAxLjA4OCAyLjkxLjgzMi4wOTItLjY0Ny4zNS0xLjA4OC42MzYtMS4zMzgtMi4yMi0uMjUzLTQuNTU1LTEuMTEzLTQuNTU1LTQuOTUxIDAtMS4wOTMuMzktMS45ODggMS4wMjktMi42ODgtLjEwMy0uMjUzLS40NDYtMS4yNzIuMDk4LTIuNjUgMCAwIC44NC0uMjcgMi43NSAxLjAyNkE5LjU2NCA5LjU2NCAwIDAxMTIgNi44NDRjLjg1LjAwNCAxLjcwNS4xMTUgMi41MDQuMzM3IDEuOTA5LTEuMjk2IDIuNzQ3LTEuMDI3IDIuNzQ3LTEuMDI3LjU0NiAxLjM3OS4yMDIgMi4zOTguMSAyLjY1MS42NC43IDEuMDI4IDEuNTk1IDEuMDI4IDIuNjg4IDAgMy44NDgtMi4zMzkgNC42OTUtNC41NjYgNC45NDMuMzU5LjMwOS42NzguOTIuNjc4IDEuODU1IDAgMS4zMzgtLjAxMiAyLjQxOS0uMDEyIDIuNzQ3IDAgLjI2OC4xOC41OC42ODguNDgyQTEwLjAxOSAxMC4wMTkgMCAwMDIyIDEyLjAxN0MyMiA2LjQ4NCAxNy41MjIgMiAxMiAyelwiXG4gICAgICAgICAgICBjbGlwUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICdUd2l0dGVyJyxcbiAgICAgIGhyZWY6ICcjJyxcbiAgICAgIGljb246IChwcm9wczogYW55KSA9PiAoXG4gICAgICAgIDxzdmcgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgIDxwYXRoIGQ9XCJNOC4yOSAyMC4yNTFjNy41NDcgMCAxMS42NzUtNi4yNTMgMTEuNjc1LTExLjY3NSAwLS4xNzggMC0uMzU1LS4wMTItLjUzQTguMzQ4IDguMzQ4IDAgMDAyMiA1LjkyYTguMTkgOC4xOSAwIDAxLTIuMzU3LjY0NiA0LjExOCA0LjExOCAwIDAwMS44MDQtMi4yNyA4LjIyNCA4LjIyNCAwIDAxLTIuNjA1Ljk5NiA0LjEwNyA0LjEwNyAwIDAwLTYuOTkzIDMuNzQzIDExLjY1IDExLjY1IDAgMDEtOC40NTctNC4yODcgNC4xMDYgNC4xMDYgMCAwMDEuMjcgNS40NzdBNC4wNzIgNC4wNzIgMCAwMTIuOCA5LjcxM3YuMDUyYTQuMTA1IDQuMTA1IDAgMDAzLjI5MiA0LjAyMiA0LjA5NSA0LjA5NSAwIDAxLTEuODUzLjA3IDQuMTA4IDQuMTA4IDAgMDAzLjgzNCAyLjg1QTguMjMzIDguMjMzIDAgMDEyIDE4LjQwN2ExMS42MTYgMTEuNjE2IDAgMDA2LjI5IDEuODRcIiAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICksXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnTGlua2VkSW4nLFxuICAgICAgaHJlZjogJyMnLFxuICAgICAgaWNvbjogKHByb3BzOiBhbnkpID0+IChcbiAgICAgICAgPHN2ZyBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgPHBhdGggZD1cIk0yMC40NDcgMjAuNDUyaC0zLjU1NHYtNS41NjljMC0xLjMyOC0uMDI3LTMuMDM3LTEuODUyLTMuMDM3LTEuODUzIDAtMi4xMzYgMS40NDUtMi4xMzYgMi45Mzl2NS42NjdIOS4zNTFWOWgzLjQxNHYxLjU2MWguMDQ2Yy40NzctLjkgMS42MzctMS44NSAzLjM3LTEuODUgMy42MDEgMCA0LjI2NyAyLjM3IDQuMjY3IDUuNDU1djYuMjg2ek01LjMzNyA3LjQzM2MtMS4xNDQgMC0yLjA2My0uOTI2LTIuMDYzLTIuMDY1IDAtMS4xMzguOTItMi4wNjMgMi4wNjMtMi4wNjMgMS4xNCAwIDIuMDY0LjkyNSAyLjA2NCAyLjA2MyAwIDEuMTM5LS45MjUgMi4wNjUtMi4wNjQgMi4wNjV6bTEuNzgyIDEzLjAxOUgzLjU1NVY5aDMuNTY0djExLjQ1MnpNMjIuMjI1IDBIMS43NzFDLjc5MiAwIDAgLjc3NCAwIDEuNzI5djIwLjU0MkMwIDIzLjIyNy43OTIgMjQgMS43NzEgMjRoMjAuNDUxQzIzLjIgMjQgMjQgMjMuMjI3IDI0IDIyLjI3MVYxLjcyOUMyNCAuNzc0IDIzLjIgMCAyMi4yMjIgMGguMDAzelwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICB9LFxuICBdLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRm9vdGVyKCkge1xuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctN3hsIHB4LTYgcHktMTIgbWQ6ZmxleCBtZDppdGVtcy1jZW50ZXIgbWQ6anVzdGlmeS1iZXR3ZWVuIGxnOnB4LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC04IG1kOmdyaWQtY29scy00XCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgbGVhZGluZy02IHRleHQtZ3JheS05MDBcIj5Qcm9kdWN0PC9oMz5cbiAgICAgICAgICAgIDx1bCByb2xlPVwibGlzdFwiIGNsYXNzTmFtZT1cIm10LTYgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5wcm9kdWN0Lm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgIDxsaSBrZXk9e2l0ZW0ubmFtZX0+XG4gICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gbGVhZGluZy02IHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCBsZWFkaW5nLTYgdGV4dC1ncmF5LTkwMFwiPkNvbXBhbnk8L2gzPlxuICAgICAgICAgICAgPHVsIHJvbGU9XCJsaXN0XCIgY2xhc3NOYW1lPVwibXQtNiBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAge2Zvb3RlckxpbmtzLmNvbXBhbnkubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBsZWFkaW5nLTYgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIGxlYWRpbmctNiB0ZXh0LWdyYXktOTAwXCI+UmVzb3VyY2VzPC9oMz5cbiAgICAgICAgICAgIDx1bCByb2xlPVwibGlzdFwiIGNsYXNzTmFtZT1cIm10LTYgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5yZXNvdXJjZXMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBsZWFkaW5nLTYgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIGxlYWRpbmctNiB0ZXh0LWdyYXktOTAwXCI+Q29ubmVjdDwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgZmxleCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAge2Zvb3RlckxpbmtzLnNvY2lhbC5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS01MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8aXRlbS5pY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICAgIFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIG1heC13LTd4bCBweC02IHB5LTYgbWQ6ZmxleCBtZDppdGVtcy1jZW50ZXIgbWQ6anVzdGlmeS1iZXR3ZWVuIGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtNiB3LTYgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQgYmctcHJpbWFyeS02MDBcIj5cbiAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsxLjV9XG4gICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICBkPVwiTTEyIDN2Mi4yNW02LjM2NC4zODZsLTEuNTkxIDEuNTkxTTIxIDEyaC0yLjI1bS0uMzg2IDYuMzY0bC0xLjU5MS0xLjU5MU0xMiAxOC43NVYyMW0tNC43NzMtNC4yMjdsLTEuNTkxIDEuNTkxTTUuMjUgMTJIM200LjIyNy00Ljc3M0w1LjYzNiA1LjYzNk0xNS43NSAxMmEzLjc1IDMuNzUgMCAxMS03LjUgMCAzLjc1IDMuNzUgMCAwMTcuNSAwelwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkNhcmJvbkxlZGdlcjwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgbWQ6bXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICDCqSAyMDI0IENhcmJvbkxlZGdlci4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICA8c3Bhbj5NYWRlIHdpdGg8L3NwYW4+XG4gICAgICAgICAgICAgIDxIZWFydEljb24gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LXJlZC01MDBcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj5mb3IgdGhlIHBsYW5ldDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJIZWFydEljb24iLCJmb290ZXJMaW5rcyIsInByb2R1Y3QiLCJuYW1lIiwiaHJlZiIsImNvbXBhbnkiLCJyZXNvdXJjZXMiLCJzb2NpYWwiLCJpY29uIiwicHJvcHMiLCJzdmciLCJmaWxsIiwidmlld0JveCIsInBhdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsIkZvb3RlciIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsImgzIiwidWwiLCJyb2xlIiwibWFwIiwiaXRlbSIsImxpIiwic3BhbiIsImFyaWEtaGlkZGVuIiwic3Ryb2tlV2lkdGgiLCJzdHJva2UiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Verify Project\",\n        href: \"/verify\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"System Health\",\n        href: \"/health\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-white\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            strokeWidth: 1.5,\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                d: \"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"CarbonLedger\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 -mt-1\",\n                                                children: \"Satellite Verification\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${isActive ? \"text-primary-600 bg-primary-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"System Online\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\",\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"block h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"block h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"md:hidden bg-white border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 pt-2 pb-3 space-y-1\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `flex items-center px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${isActive ? \"text-primary-600 bg-primary-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 21\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 px-4 py-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"System Online\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/Layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MainLayout({ children, showFooter = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 19,\n                columnNumber: 22\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzhCO0FBQ0E7QUFPZixTQUFTRSxXQUFXLEVBQUVDLFFBQVEsRUFBRUMsYUFBYSxJQUFJLEVBQW1CO0lBQ2pGLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ04sK0NBQU1BOzs7OzswQkFDUCw4REFBQ087Z0JBQUtELFdBQVU7MEJBQ2JIOzs7Ozs7WUFFRkMsNEJBQWMsOERBQUNILCtDQUFNQTs7Ozs7Ozs7Ozs7QUFHNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dC50c3g/ZTNjNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBIZWFkZXIgZnJvbSAnLi9IZWFkZXInO1xuaW1wb3J0IEZvb3RlciBmcm9tICcuL0Zvb3Rlcic7XG5cbmludGVyZmFjZSBNYWluTGF5b3V0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xuICBzaG93Rm9vdGVyPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWFpbkxheW91dCh7IGNoaWxkcmVuLCBzaG93Rm9vdGVyID0gdHJ1ZSB9OiBNYWluTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICA8SGVhZGVyIC8+XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JheS01MFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L21haW4+XG4gICAgICB7c2hvd0Zvb3RlciAmJiA8Rm9vdGVyIC8+fVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWRlciIsIkZvb3RlciIsIk1haW5MYXlvdXQiLCJjaGlsZHJlbiIsInNob3dGb290ZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Verification/VerificationForm.tsx":
/*!**********************************************************!*\
  !*** ./src/components/Verification/VerificationForm.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerificationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_InformationCircleIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,InformationCircleIcon,MapPinIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_InformationCircleIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,InformationCircleIcon,MapPinIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentTextIcon_InformationCircleIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentTextIcon,InformationCircleIcon,MapPinIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Validation schema\nconst verificationSchema = zod__WEBPACK_IMPORTED_MODULE_5__.object({\n    projectName: zod__WEBPACK_IMPORTED_MODULE_5__.string().min(1, \"Project name is required\").max(200, \"Project name too long\"),\n    projectDescription: zod__WEBPACK_IMPORTED_MODULE_5__.string().max(1000, \"Description too long\").optional(),\n    coordinates: zod__WEBPACK_IMPORTED_MODULE_5__.object({\n        lonMin: zod__WEBPACK_IMPORTED_MODULE_5__.number().min(-180, \"Invalid longitude\").max(180, \"Invalid longitude\"),\n        latMin: zod__WEBPACK_IMPORTED_MODULE_5__.number().min(-90, \"Invalid latitude\").max(90, \"Invalid latitude\"),\n        lonMax: zod__WEBPACK_IMPORTED_MODULE_5__.number().min(-180, \"Invalid longitude\").max(180, \"Invalid longitude\"),\n        latMax: zod__WEBPACK_IMPORTED_MODULE_5__.number().min(-90, \"Invalid latitude\").max(90, \"Invalid latitude\")\n    }).refine((data)=>data.lonMin < data.lonMax && data.latMin < data.latMax, {\n        message: \"Invalid coordinate bounds\",\n        path: [\n            \"coordinates\"\n        ]\n    })\n});\nfunction VerificationForm({ onVerificationStart, onVerificationComplete, isVerifying, disabled = false }) {\n    const [usePreset, setUsePreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, setValue, watch, formState: { errors, isValid } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(verificationSchema),\n        mode: \"onChange\",\n        defaultValues: {\n            projectName: \"\",\n            projectDescription: \"\",\n            coordinates: {\n                lonMin: -74.0,\n                latMin: -2.0,\n                lonMax: -73.9,\n                latMax: -1.9\n            }\n        }\n    });\n    const coordinates = watch(\"coordinates\");\n    const presetLocations = [\n        {\n            name: \"Amazon Rainforest (Brazil)\",\n            coordinates: {\n                lonMin: -74.0,\n                latMin: -2.0,\n                lonMax: -73.9,\n                latMax: -1.9\n            }\n        },\n        {\n            name: \"Congo Basin (DRC)\",\n            coordinates: {\n                lonMin: 18.0,\n                latMin: -4.0,\n                lonMax: 18.1,\n                latMax: -3.9\n            }\n        },\n        {\n            name: \"Borneo (Indonesia)\",\n            coordinates: {\n                lonMin: 113.0,\n                latMin: -2.0,\n                lonMax: 113.1,\n                latMax: -1.9\n            }\n        },\n        {\n            name: \"Madagascar Forest\",\n            coordinates: {\n                lonMin: 46.0,\n                latMin: -19.0,\n                lonMax: 46.1,\n                latMax: -18.9\n            }\n        }\n    ];\n    const handlePresetSelect = (preset)=>{\n        setValue(\"coordinates\", preset.coordinates, {\n            shouldValidate: true\n        });\n        setUsePreset(false);\n    };\n    const onSubmit = async (data)=>{\n        try {\n            onVerificationStart();\n            const request = {\n                project_name: data.projectName,\n                project_description: data.projectDescription || undefined,\n                lon_min: data.coordinates.lonMin,\n                lat_min: data.coordinates.latMin,\n                lon_max: data.coordinates.lonMax,\n                lat_max: data.coordinates.latMax\n            };\n            // Try real API first, fall back to mock\n            let result;\n            try {\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].submitVerification(request);\n            } catch (error) {\n                // Use mock data if API is not available\n                result = await _lib_api__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getMockVerification(request);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Using demo data - API not available\");\n            }\n            onVerificationComplete(result);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Verification completed successfully!\");\n        } catch (error) {\n            console.error(\"Verification failed:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Verification failed. Please try again.\");\n            onVerificationComplete({}); // Reset state\n        }\n    };\n    const calculateArea = ()=>{\n        const { lonMin, latMin, lonMax, latMax } = coordinates;\n        if (lonMin && latMin && lonMax && latMax) {\n            // Rough calculation in km²\n            const area = Math.abs((lonMax - lonMin) * (latMax - latMin)) * 111 * 111;\n            return area.toFixed(2);\n        }\n        return \"0\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Project Information\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"Enter the details of your carbon credit project for satellite verification\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"projectName\",\n                                className: \"label\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_InformationCircleIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Project Name *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ...register(\"projectName\"),\n                                type: \"text\",\n                                id: \"projectName\",\n                                className: \"input\",\n                                placeholder: \"e.g., Amazon Forest Conservation Project\",\n                                disabled: disabled\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            errors.projectName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.projectName.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"projectDescription\",\n                                className: \"label\",\n                                children: \"Project Description\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ...register(\"projectDescription\"),\n                                id: \"projectDescription\",\n                                rows: 3,\n                                className: \"input\",\n                                placeholder: \"Describe your carbon credit project, conservation efforts, and goals...\",\n                                disabled: disabled\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            errors.projectDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.projectDescription.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label mb-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_InformationCircleIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Geographic Coordinates *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setUsePreset(!usePreset),\n                                        className: \"text-sm text-primary-600 hover:text-primary-500\",\n                                        disabled: disabled,\n                                        children: \"Use preset location\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            usePreset && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Select a preset location:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-2\",\n                                        children: presetLocations.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handlePresetSelect(preset),\n                                                className: \"text-left p-2 text-sm text-gray-600 hover:bg-white hover:shadow-sm rounded border border-transparent hover:border-gray-200 transition-all duration-200\",\n                                                disabled: disabled,\n                                                children: preset.name\n                                            }, preset.name, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"lonMin\",\n                                                className: \"block text-sm text-gray-600 mb-1\",\n                                                children: \"Min Longitude (West)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...register(\"coordinates.lonMin\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                type: \"number\",\n                                                step: \"0.000001\",\n                                                id: \"lonMin\",\n                                                className: \"input\",\n                                                placeholder: \"-74.0\",\n                                                disabled: disabled\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"latMin\",\n                                                className: \"block text-sm text-gray-600 mb-1\",\n                                                children: \"Min Latitude (South)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...register(\"coordinates.latMin\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                type: \"number\",\n                                                step: \"0.000001\",\n                                                id: \"latMin\",\n                                                className: \"input\",\n                                                placeholder: \"-2.0\",\n                                                disabled: disabled\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"lonMax\",\n                                                className: \"block text-sm text-gray-600 mb-1\",\n                                                children: \"Max Longitude (East)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...register(\"coordinates.lonMax\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                type: \"number\",\n                                                step: \"0.000001\",\n                                                id: \"lonMax\",\n                                                className: \"input\",\n                                                placeholder: \"-73.9\",\n                                                disabled: disabled\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"latMax\",\n                                                className: \"block text-sm text-gray-600 mb-1\",\n                                                children: \"Max Latitude (North)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...register(\"coordinates.latMax\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                type: \"number\",\n                                                step: \"0.000001\",\n                                                id: \"latMax\",\n                                                className: \"input\",\n                                                placeholder: \"-1.9\",\n                                                disabled: disabled\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            errors.coordinates && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.coordinates.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-blue-50 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentTextIcon_InformationCircleIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-500 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: [\n                                                \"Estimated area: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: [\n                                                        calculateArea(),\n                                                        \" km\\xb2\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: !isValid || disabled || isVerifying,\n                            className: \"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isVerifying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Analyzing Satellite Data...\"\n                                ]\n                            }, void 0, true) : \"Start Verification\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationForm.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Verification/VerificationForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Verification/VerificationResults.tsx":
/*!*************************************************************!*\
  !*** ./src/components/Verification/VerificationResults.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerificationResults)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,DocumentArrowDownIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,DocumentArrowDownIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,DocumentArrowDownIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,DocumentArrowDownIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,DocumentArrowDownIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction VerificationResults({ result, onNewVerification }) {\n    const getStatusIcon = ()=>{\n        if (!result.deforestation_detected) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"h-8 w-8 text-green-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                lineNumber: 24,\n                columnNumber: 14\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-8 w-8 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                lineNumber: 26,\n                columnNumber: 14\n            }, this);\n        }\n    };\n    const getStatusColor = ()=>{\n        if (!result.deforestation_detected) {\n            return \"text-green-600 bg-green-50 border-green-200\";\n        } else {\n            return \"text-red-600 bg-red-50 border-red-200\";\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatPercentage = (value)=>{\n        return `${(value * 100).toFixed(1)}%`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Verification Results\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onNewVerification,\n                                className: \"btn-outline text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"New Verification\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-4 rounded-lg border ${getStatusColor()} mb-6`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                getStatusIcon(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium\",\n                                            children: result.deforestation_detected ? \"Deforestation Detected\" : \"No Deforestation Detected\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm opacity-90\",\n                                            children: result.deforestation_detected ? `${result.deforested_area_km2.toFixed(2)} km² of deforestation found` : \"Forest area appears to be preserved\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: result.confidence_score ? formatPercentage(result.confidence_score) : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Confidence Score\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: [\n                                            result.area_checked.total_area_km2.toFixed(1),\n                                            \" km\\xb2\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Area Analyzed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Project Details\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"grid grid-cols-1 gap-4 sm:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Project Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: result.project_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Verification ID\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900 font-mono\",\n                                                children: result.verification_id\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Verified At\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: formatDate(result.verified_at)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"Processing Time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: [\n                                                    result.processing_time_seconds.toFixed(1),\n                                                    \" seconds\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Detailed Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Data Quality Score\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: result.data_quality_score ? formatPercentage(result.data_quality_score) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-600 h-2 rounded-full\",\n                                            style: {\n                                                width: result.data_quality_score ? `${result.data_quality_score * 100}%` : \"0%\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Confidence Score\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: result.confidence_score ? formatPercentage(result.confidence_score) : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-600 h-2 rounded-full\",\n                                            style: {\n                                                width: result.confidence_score ? `${result.confidence_score * 100}%` : \"0%\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                        children: \"Analysis Period\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            result.analysis_period.start_date,\n                                            \" to \",\n                                            result.analysis_period.end_date\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                        children: \"Sources Used\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            result.sources_used || \"Multiple\",\n                                            \" satellite data sources\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                        children: \"Verification Level\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 capitalize\",\n                                        children: result.verification_level || \"Standard\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                        children: \"Coverage\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            (result.area_checked.analyzed_area_km2 / result.area_checked.total_area_km2 * 100).toFixed(1),\n                                            \"% analyzed\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    result.vegetation_indices && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 mb-2\",\n                                children: \"Vegetation Indices\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-2 sm:grid-cols-3\",\n                                children: Object.entries(result.vegetation_indices).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-3 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-900 uppercase\",\n                                                children: key.replace(\"_\", \" \")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-gray-700\",\n                                                children: typeof value === \"number\" ? value.toFixed(3) : value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    result.warnings && result.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600 mt-0.5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"Warnings\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"mt-1 text-sm text-yellow-700 list-disc list-inside\",\n                                            children: result.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: warning\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Next Steps\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_DocumentArrowDownIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Download Verification Report\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full btn-outline\",\n                                children: \"View on Map\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full btn-outline\",\n                                children: \"Share Results\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Verification/VerificationResults.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9WZXJpZmljYXRpb24vVmVyaWZpY2F0aW9uUmVzdWx0cy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUV1QztBQU9GO0FBUXRCLFNBQVNNLG9CQUFvQixFQUMxQ0MsTUFBTSxFQUNOQyxpQkFBaUIsRUFDUTtJQUN6QixNQUFNQyxnQkFBZ0I7UUFDcEIsSUFBSSxDQUFDRixPQUFPRyxzQkFBc0IsRUFBRTtZQUNsQyxxQkFBTyw4REFBQ1QsMkxBQWVBO2dCQUFDVSxXQUFVOzs7Ozs7UUFDcEMsT0FBTztZQUNMLHFCQUFPLDhEQUFDVCwyTEFBdUJBO2dCQUFDUyxXQUFVOzs7Ozs7UUFDNUM7SUFDRjtJQUVBLE1BQU1DLGlCQUFpQjtRQUNyQixJQUFJLENBQUNMLE9BQU9HLHNCQUFzQixFQUFFO1lBQ2xDLE9BQU87UUFDVCxPQUFPO1lBQ0wsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNRyxhQUFhLENBQUNDO1FBQ2xCLE9BQU8sSUFBSUMsS0FBS0QsWUFBWUUsa0JBQWtCLENBQUMsU0FBUztZQUN0REMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1FBQ1Y7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixPQUFPLENBQUMsRUFBRSxDQUFDQSxRQUFRLEdBQUUsRUFBR0MsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQ3ZDO0lBRUEscUJBQ0UsOERBQUN4QixpREFBTUEsQ0FBQ3lCLEdBQUc7UUFDVEMsU0FBUztZQUFFQyxTQUFTO1lBQUdDLEdBQUc7UUFBRztRQUM3QkMsU0FBUztZQUFFRixTQUFTO1lBQUdDLEdBQUc7UUFBRTtRQUM1QkUsWUFBWTtZQUFFQyxVQUFVO1FBQUk7UUFDNUJwQixXQUFVOzswQkFHViw4REFBQ2M7Z0JBQUlkLFdBQVU7O2tDQUNiLDhEQUFDYzt3QkFBSWQsV0FBVTs7MENBQ2IsOERBQUNxQjtnQ0FBR3JCLFdBQVU7MENBQXNDOzs7Ozs7MENBR3BELDhEQUFDc0I7Z0NBQ0NDLFNBQVMxQjtnQ0FDVEcsV0FBVTs7a0RBRVYsOERBQUNOLDJMQUFhQTt3Q0FBQ00sV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7OztrQ0FNOUMsOERBQUNjO3dCQUFJZCxXQUFXLENBQUMsc0JBQXNCLEVBQUVDLGlCQUFpQixLQUFLLENBQUM7a0NBQzlELDRFQUFDYTs0QkFBSWQsV0FBVTs7Z0NBQ1pGOzhDQUNELDhEQUFDZ0I7b0NBQUlkLFdBQVU7O3NEQUNiLDhEQUFDd0I7NENBQUd4QixXQUFVO3NEQUNYSixPQUFPRyxzQkFBc0IsR0FDMUIsMkJBQ0E7Ozs7OztzREFFTiw4REFBQzBCOzRDQUFFekIsV0FBVTtzREFDVkosT0FBT0csc0JBQXNCLEdBQzFCLENBQUMsRUFBRUgsT0FBTzhCLG1CQUFtQixDQUFDYixPQUFPLENBQUMsR0FBRywyQkFBMkIsQ0FBQyxHQUNyRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT1osOERBQUNDO3dCQUFJZCxXQUFVOzswQ0FDYiw4REFBQ2M7Z0NBQUlkLFdBQVU7O2tEQUNiLDhEQUFDYzt3Q0FBSWQsV0FBVTtrREFDWkosT0FBTytCLGdCQUFnQixHQUFHaEIsaUJBQWlCZixPQUFPK0IsZ0JBQWdCLElBQUk7Ozs7OztrREFFekUsOERBQUNiO3dDQUFJZCxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV6Qyw4REFBQ2M7Z0NBQUlkLFdBQVU7O2tEQUNiLDhEQUFDYzt3Q0FBSWQsV0FBVTs7NENBQ1pKLE9BQU9nQyxZQUFZLENBQUNDLGNBQWMsQ0FBQ2hCLE9BQU8sQ0FBQzs0Q0FBRzs7Ozs7OztrREFFakQsOERBQUNDO3dDQUFJZCxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUszQyw4REFBQ2M7d0JBQUlkLFdBQVU7OzBDQUNiLDhEQUFDOEI7Z0NBQUc5QixXQUFVOzBDQUF5Qzs7Ozs7OzBDQUN2RCw4REFBQytCO2dDQUFHL0IsV0FBVTs7a0RBQ1osOERBQUNjOzswREFDQyw4REFBQ2tCO2dEQUFHaEMsV0FBVTswREFBb0M7Ozs7OzswREFDbEQsOERBQUNpQztnREFBR2pDLFdBQVU7MERBQThCSixPQUFPc0MsWUFBWTs7Ozs7Ozs7Ozs7O2tEQUVqRSw4REFBQ3BCOzswREFDQyw4REFBQ2tCO2dEQUFHaEMsV0FBVTswREFBb0M7Ozs7OzswREFDbEQsOERBQUNpQztnREFBR2pDLFdBQVU7MERBQXdDSixPQUFPdUMsZUFBZTs7Ozs7Ozs7Ozs7O2tEQUU5RSw4REFBQ3JCOzswREFDQyw4REFBQ2tCO2dEQUFHaEMsV0FBVTswREFBb0M7Ozs7OzswREFDbEQsOERBQUNpQztnREFBR2pDLFdBQVU7MERBQThCRSxXQUFXTixPQUFPd0MsV0FBVzs7Ozs7Ozs7Ozs7O2tEQUUzRSw4REFBQ3RCOzswREFDQyw4REFBQ2tCO2dEQUFHaEMsV0FBVTswREFBb0M7Ozs7OzswREFDbEQsOERBQUNpQztnREFBR2pDLFdBQVU7O29EQUNYSixPQUFPeUMsdUJBQXVCLENBQUN4QixPQUFPLENBQUM7b0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXJELDhEQUFDQztnQkFBSWQsV0FBVTs7a0NBQ2IsOERBQUN3Qjt3QkFBR3hCLFdBQVU7a0NBQXlDOzs7Ozs7a0NBR3ZELDhEQUFDYzt3QkFBSWQsV0FBVTs7MENBQ2IsOERBQUNjOztrREFDQyw4REFBQ0E7d0NBQUlkLFdBQVU7OzBEQUNiLDhEQUFDc0M7Z0RBQUt0QyxXQUFVOzBEQUFnQjs7Ozs7OzBEQUNoQyw4REFBQ3NDO2dEQUFLdEMsV0FBVTswREFDYkosT0FBTzJDLGtCQUFrQixHQUFHNUIsaUJBQWlCZixPQUFPMkMsa0JBQWtCLElBQUk7Ozs7Ozs7Ozs7OztrREFHL0UsOERBQUN6Qjt3Q0FBSWQsV0FBVTtrREFDYiw0RUFBQ2M7NENBQ0NkLFdBQVU7NENBQ1Z3QyxPQUFPO2dEQUNMQyxPQUFPN0MsT0FBTzJDLGtCQUFrQixHQUM1QixDQUFDLEVBQUUzQyxPQUFPMkMsa0JBQWtCLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FDckM7NENBQ047Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtOLDhEQUFDekI7O2tEQUNDLDhEQUFDQTt3Q0FBSWQsV0FBVTs7MERBQ2IsOERBQUNzQztnREFBS3RDLFdBQVU7MERBQWdCOzs7Ozs7MERBQ2hDLDhEQUFDc0M7Z0RBQUt0QyxXQUFVOzBEQUNiSixPQUFPK0IsZ0JBQWdCLEdBQUdoQixpQkFBaUJmLE9BQU8rQixnQkFBZ0IsSUFBSTs7Ozs7Ozs7Ozs7O2tEQUczRSw4REFBQ2I7d0NBQUlkLFdBQVU7a0RBQ2IsNEVBQUNjOzRDQUNDZCxXQUFVOzRDQUNWd0MsT0FBTztnREFDTEMsT0FBTzdDLE9BQU8rQixnQkFBZ0IsR0FDMUIsQ0FBQyxFQUFFL0IsT0FBTytCLGdCQUFnQixHQUFHLElBQUksQ0FBQyxDQUFDLEdBQ25DOzRDQUNOOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPUiw4REFBQ2I7d0JBQUlkLFdBQVU7OzBDQUNiLDhEQUFDYzs7a0RBQ0MsOERBQUNnQjt3Q0FBRzlCLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDeUI7d0NBQUV6QixXQUFVOzs0Q0FDVkosT0FBTzhDLGVBQWUsQ0FBQ0MsVUFBVTs0Q0FBQzs0Q0FBSy9DLE9BQU84QyxlQUFlLENBQUNFLFFBQVE7Ozs7Ozs7Ozs7Ozs7MENBRzNFLDhEQUFDOUI7O2tEQUNDLDhEQUFDZ0I7d0NBQUc5QixXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ3lCO3dDQUFFekIsV0FBVTs7NENBQ1ZKLE9BQU9pRCxZQUFZLElBQUk7NENBQVc7Ozs7Ozs7Ozs7Ozs7MENBR3ZDLDhEQUFDL0I7O2tEQUNDLDhEQUFDZ0I7d0NBQUc5QixXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ3lCO3dDQUFFekIsV0FBVTtrREFDVkosT0FBT2tELGtCQUFrQixJQUFJOzs7Ozs7Ozs7Ozs7MENBR2xDLDhEQUFDaEM7O2tEQUNDLDhEQUFDZ0I7d0NBQUc5QixXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ3lCO3dDQUFFekIsV0FBVTs7NENBQ1QsUUFBUTRCLFlBQVksQ0FBQ21CLGlCQUFpQixHQUFHbkQsT0FBT2dDLFlBQVksQ0FBQ0MsY0FBYyxHQUFJLEdBQUUsRUFBR2hCLE9BQU8sQ0FBQzs0Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFNdEdqQixPQUFPb0Qsa0JBQWtCLGtCQUN4Qiw4REFBQ2xDO3dCQUFJZCxXQUFVOzswQ0FDYiw4REFBQzhCO2dDQUFHOUIsV0FBVTswQ0FBeUM7Ozs7OzswQ0FDdkQsOERBQUNjO2dDQUFJZCxXQUFVOzBDQUNaaUQsT0FBT0MsT0FBTyxDQUFDdEQsT0FBT29ELGtCQUFrQixFQUFFRyxHQUFHLENBQUMsQ0FBQyxDQUFDQyxLQUFLeEMsTUFBTSxpQkFDMUQsOERBQUNFO3dDQUFjZCxXQUFVOzswREFDdkIsOERBQUNjO2dEQUFJZCxXQUFVOzBEQUNab0QsSUFBSUMsT0FBTyxDQUFDLEtBQUs7Ozs7OzswREFFcEIsOERBQUN2QztnREFBSWQsV0FBVTswREFDWixPQUFPWSxVQUFVLFdBQVdBLE1BQU1DLE9BQU8sQ0FBQyxLQUFLRDs7Ozs7Ozt1Q0FMMUN3Qzs7Ozs7Ozs7Ozs7Ozs7OztvQkFjakJ4RCxPQUFPMEQsUUFBUSxJQUFJMUQsT0FBTzBELFFBQVEsQ0FBQ0MsTUFBTSxHQUFHLG1CQUMzQyw4REFBQ3pDO3dCQUFJZCxXQUFVO2tDQUNiLDRFQUFDYzs0QkFBSWQsV0FBVTs7OENBQ2IsOERBQUNSLDJMQUFxQkE7b0NBQUNRLFdBQVU7Ozs7Ozs4Q0FDakMsOERBQUNjOztzREFDQyw4REFBQ2dCOzRDQUFHOUIsV0FBVTtzREFBc0M7Ozs7OztzREFDcEQsOERBQUN3RDs0Q0FBR3hELFdBQVU7c0RBQ1hKLE9BQU8wRCxRQUFRLENBQUNILEdBQUcsQ0FBQyxDQUFDTSxTQUFTQyxzQkFDN0IsOERBQUNDOzhEQUFnQkY7bURBQVJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVXZCLDhEQUFDNUM7Z0JBQUlkLFdBQVU7O2tDQUNiLDhEQUFDd0I7d0JBQUd4QixXQUFVO2tDQUF5Qzs7Ozs7O2tDQUN2RCw4REFBQ2M7d0JBQUlkLFdBQVU7OzBDQUNiLDhEQUFDc0I7Z0NBQU90QixXQUFVOztrREFDaEIsOERBQUNQLDJMQUFxQkE7d0NBQUNPLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7MENBR3BELDhEQUFDc0I7Z0NBQU90QixXQUFVOzBDQUFxQjs7Ozs7OzBDQUd2Qyw4REFBQ3NCO2dDQUFPdEIsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9qRCIsInNvdXJjZXMiOlsid2VicGFjazovL2NhcmJvbmxlZGdlci1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL1ZlcmlmaWNhdGlvbi9WZXJpZmljYXRpb25SZXN1bHRzLnRzeD8yNjA4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQge1xuICBDaGVja0NpcmNsZUljb24sXG4gIEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uLFxuICBJbmZvcm1hdGlvbkNpcmNsZUljb24sXG4gIERvY3VtZW50QXJyb3dEb3duSWNvbixcbiAgQXJyb3dQYXRoSWNvbixcbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB0eXBlIHsgVmVyaWZpY2F0aW9uUmVzcG9uc2UgfSBmcm9tICdAL3R5cGVzL2FwaSc7XG5cbmludGVyZmFjZSBWZXJpZmljYXRpb25SZXN1bHRzUHJvcHMge1xuICByZXN1bHQ6IFZlcmlmaWNhdGlvblJlc3BvbnNlO1xuICBvbk5ld1ZlcmlmaWNhdGlvbjogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVmVyaWZpY2F0aW9uUmVzdWx0cyh7XG4gIHJlc3VsdCxcbiAgb25OZXdWZXJpZmljYXRpb24sXG59OiBWZXJpZmljYXRpb25SZXN1bHRzUHJvcHMpIHtcbiAgY29uc3QgZ2V0U3RhdHVzSWNvbiA9ICgpID0+IHtcbiAgICBpZiAoIXJlc3VsdC5kZWZvcmVzdGF0aW9uX2RldGVjdGVkKSB7XG4gICAgICByZXR1cm4gPENoZWNrQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JlZW4tNTAwXCIgLz47XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiA8RXhjbGFtYXRpb25UcmlhbmdsZUljb24gY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LXJlZC01MDBcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoKSA9PiB7XG4gICAgaWYgKCFyZXN1bHQuZGVmb3Jlc3RhdGlvbl9kZXRlY3RlZCkge1xuICAgICAgcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwJztcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuICd0ZXh0LXJlZC02MDAgYmctcmVkLTUwIGJvcmRlci1yZWQtMjAwJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFBlcmNlbnRhZ2UgPSAodmFsdWU6IG51bWJlcikgPT4ge1xuICAgIHJldHVybiBgJHsodmFsdWUgKiAxMDApLnRvRml4ZWQoMSl9JWA7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8bW90aW9uLmRpdlxuICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUgfX1cbiAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktNlwiXG4gICAgPlxuICAgICAgey8qIE1haW4gUmVzdWx0IENhcmQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgVmVyaWZpY2F0aW9uIFJlc3VsdHNcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uTmV3VmVyaWZpY2F0aW9ufVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLW91dGxpbmUgdGV4dC1zbVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEFycm93UGF0aEljb24gY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgIE5ldyBWZXJpZmljYXRpb25cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFN0YXR1cyBCYW5uZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgYm9yZGVyICR7Z2V0U3RhdHVzQ29sb3IoKX0gbWItNmB9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKCl9XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICB7cmVzdWx0LmRlZm9yZXN0YXRpb25fZGV0ZWN0ZWRcbiAgICAgICAgICAgICAgICAgID8gJ0RlZm9yZXN0YXRpb24gRGV0ZWN0ZWQnXG4gICAgICAgICAgICAgICAgICA6ICdObyBEZWZvcmVzdGF0aW9uIERldGVjdGVkJ31cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBvcGFjaXR5LTkwXCI+XG4gICAgICAgICAgICAgICAge3Jlc3VsdC5kZWZvcmVzdGF0aW9uX2RldGVjdGVkXG4gICAgICAgICAgICAgICAgICA/IGAke3Jlc3VsdC5kZWZvcmVzdGVkX2FyZWFfa20yLnRvRml4ZWQoMil9IGttwrIgb2YgZGVmb3Jlc3RhdGlvbiBmb3VuZGBcbiAgICAgICAgICAgICAgICAgIDogJ0ZvcmVzdCBhcmVhIGFwcGVhcnMgdG8gYmUgcHJlc2VydmVkJ31cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBLZXkgTWV0cmljcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00IG1iLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAge3Jlc3VsdC5jb25maWRlbmNlX3Njb3JlID8gZm9ybWF0UGVyY2VudGFnZShyZXN1bHQuY29uZmlkZW5jZV9zY29yZSkgOiAnTi9BJ31cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5Db25maWRlbmNlIFNjb3JlPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtNCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgIHtyZXN1bHQuYXJlYV9jaGVja2VkLnRvdGFsX2FyZWFfa20yLnRvRml4ZWQoMSl9IGttwrJcbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5BcmVhIEFuYWx5emVkPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9qZWN0IEluZm9ybWF0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBwdC02XCI+XG4gICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+UHJvamVjdCBEZXRhaWxzPC9oND5cbiAgICAgICAgICA8ZGwgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtNCBzbTpncmlkLWNvbHMtMlwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGR0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMFwiPlByb2plY3QgTmFtZTwvZHQ+XG4gICAgICAgICAgICAgIDxkZCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ncmF5LTkwMFwiPntyZXN1bHQucHJvamVjdF9uYW1lfTwvZGQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5WZXJpZmljYXRpb24gSUQ8L2R0PlxuICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtZ3JheS05MDAgZm9udC1tb25vXCI+e3Jlc3VsdC52ZXJpZmljYXRpb25faWR9PC9kZD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGR0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMFwiPlZlcmlmaWVkIEF0PC9kdD5cbiAgICAgICAgICAgICAgPGRkIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+e2Zvcm1hdERhdGUocmVzdWx0LnZlcmlmaWVkX2F0KX08L2RkPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZHQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwXCI+UHJvY2Vzc2luZyBUaW1lPC9kdD5cbiAgICAgICAgICAgICAgPGRkIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAge3Jlc3VsdC5wcm9jZXNzaW5nX3RpbWVfc2Vjb25kcy50b0ZpeGVkKDEpfSBzZWNvbmRzXG4gICAgICAgICAgICAgIDwvZGQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2RsPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRGV0YWlsZWQgQW5hbHlzaXMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+RGV0YWlsZWQgQW5hbHlzaXM8L2gzPlxuICAgICAgICBcbiAgICAgICAgey8qIFF1YWxpdHkgTWV0cmljcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIG1iLTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkRhdGEgUXVhbGl0eSBTY29yZTwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICB7cmVzdWx0LmRhdGFfcXVhbGl0eV9zY29yZSA/IGZvcm1hdFBlcmNlbnRhZ2UocmVzdWx0LmRhdGFfcXVhbGl0eV9zY29yZSkgOiAnTi9BJ31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBoLTIgcm91bmRlZC1mdWxsXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6IHJlc3VsdC5kYXRhX3F1YWxpdHlfc2NvcmVcbiAgICAgICAgICAgICAgICAgICAgPyBgJHtyZXN1bHQuZGF0YV9xdWFsaXR5X3Njb3JlICogMTAwfSVgXG4gICAgICAgICAgICAgICAgICAgIDogJzAlJyxcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQtc20gbWItMVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+Q29uZmlkZW5jZSBTY29yZTwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICB7cmVzdWx0LmNvbmZpZGVuY2Vfc2NvcmUgPyBmb3JtYXRQZXJjZW50YWdlKHJlc3VsdC5jb25maWRlbmNlX3Njb3JlKSA6ICdOL0EnfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBoLTIgcm91bmRlZC1mdWxsXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6IHJlc3VsdC5jb25maWRlbmNlX3Njb3JlXG4gICAgICAgICAgICAgICAgICAgID8gYCR7cmVzdWx0LmNvbmZpZGVuY2Vfc2NvcmUgKiAxMDB9JWBcbiAgICAgICAgICAgICAgICAgICAgOiAnMCUnLFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQW5hbHlzaXMgRGV0YWlscyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IGdyaWQgZ3JpZC1jb2xzLTEgZ2FwLTQgc206Z3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+QW5hbHlzaXMgUGVyaW9kPC9oND5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICB7cmVzdWx0LmFuYWx5c2lzX3BlcmlvZC5zdGFydF9kYXRlfSB0byB7cmVzdWx0LmFuYWx5c2lzX3BlcmlvZC5lbmRfZGF0ZX1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+U291cmNlcyBVc2VkPC9oND5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICB7cmVzdWx0LnNvdXJjZXNfdXNlZCB8fCAnTXVsdGlwbGUnfSBzYXRlbGxpdGUgZGF0YSBzb3VyY2VzXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlZlcmlmaWNhdGlvbiBMZXZlbDwvaDQ+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgY2FwaXRhbGl6ZVwiPlxuICAgICAgICAgICAgICB7cmVzdWx0LnZlcmlmaWNhdGlvbl9sZXZlbCB8fCAnU3RhbmRhcmQnfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5Db3ZlcmFnZTwvaDQ+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgeygocmVzdWx0LmFyZWFfY2hlY2tlZC5hbmFseXplZF9hcmVhX2ttMiAvIHJlc3VsdC5hcmVhX2NoZWNrZWQudG90YWxfYXJlYV9rbTIpICogMTAwKS50b0ZpeGVkKDEpfSUgYW5hbHl6ZWRcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFZlZ2V0YXRpb24gSW5kaWNlcyAqL31cbiAgICAgICAge3Jlc3VsdC52ZWdldGF0aW9uX2luZGljZXMgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+VmVnZXRhdGlvbiBJbmRpY2VzPC9oND5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtMiBzbTpncmlkLWNvbHMtM1wiPlxuICAgICAgICAgICAgICB7T2JqZWN0LmVudHJpZXMocmVzdWx0LnZlZ2V0YXRpb25faW5kaWNlcykubWFwKChba2V5LCB2YWx1ZV0pID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17a2V5fSBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtMyByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCB1cHBlcmNhc2VcIj5cbiAgICAgICAgICAgICAgICAgICAge2tleS5yZXBsYWNlKCdfJywgJyAnKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHt0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInID8gdmFsdWUudG9GaXhlZCgzKSA6IHZhbHVlfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFdhcm5pbmdzICovfVxuICAgICAgICB7cmVzdWx0Lndhcm5pbmdzICYmIHJlc3VsdC53YXJuaW5ncy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcC00IGJnLXllbGxvdy01MCBib3JkZXIgYm9yZGVyLXllbGxvdy0yMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgIDxJbmZvcm1hdGlvbkNpcmNsZUljb24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXllbGxvdy02MDAgbXQtMC41IG1yLTJcIiAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQteWVsbG93LTgwMFwiPldhcm5pbmdzPC9oND5cbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQteWVsbG93LTcwMCBsaXN0LWRpc2MgbGlzdC1pbnNpZGVcIj5cbiAgICAgICAgICAgICAgICAgIHtyZXN1bHQud2FybmluZ3MubWFwKCh3YXJuaW5nLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpbmRleH0+e3dhcm5pbmd9PC9saT5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEFjdGlvbnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+TmV4dCBTdGVwczwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgYnRuLXByaW1hcnlcIj5cbiAgICAgICAgICAgIDxEb2N1bWVudEFycm93RG93bkljb24gY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgIERvd25sb2FkIFZlcmlmaWNhdGlvbiBSZXBvcnRcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCBidG4tb3V0bGluZVwiPlxuICAgICAgICAgICAgVmlldyBvbiBNYXBcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCBidG4tb3V0bGluZVwiPlxuICAgICAgICAgICAgU2hhcmUgUmVzdWx0c1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvbW90aW9uLmRpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJtb3Rpb24iLCJDaGVja0NpcmNsZUljb24iLCJFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiIsIkluZm9ybWF0aW9uQ2lyY2xlSWNvbiIsIkRvY3VtZW50QXJyb3dEb3duSWNvbiIsIkFycm93UGF0aEljb24iLCJWZXJpZmljYXRpb25SZXN1bHRzIiwicmVzdWx0Iiwib25OZXdWZXJpZmljYXRpb24iLCJnZXRTdGF0dXNJY29uIiwiZGVmb3Jlc3RhdGlvbl9kZXRlY3RlZCIsImNsYXNzTmFtZSIsImdldFN0YXR1c0NvbG9yIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiaG91ciIsIm1pbnV0ZSIsImZvcm1hdFBlcmNlbnRhZ2UiLCJ2YWx1ZSIsInRvRml4ZWQiLCJkaXYiLCJpbml0aWFsIiwib3BhY2l0eSIsInkiLCJhbmltYXRlIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiaDIiLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJwIiwiZGVmb3Jlc3RlZF9hcmVhX2ttMiIsImNvbmZpZGVuY2Vfc2NvcmUiLCJhcmVhX2NoZWNrZWQiLCJ0b3RhbF9hcmVhX2ttMiIsImg0IiwiZGwiLCJkdCIsImRkIiwicHJvamVjdF9uYW1lIiwidmVyaWZpY2F0aW9uX2lkIiwidmVyaWZpZWRfYXQiLCJwcm9jZXNzaW5nX3RpbWVfc2Vjb25kcyIsInNwYW4iLCJkYXRhX3F1YWxpdHlfc2NvcmUiLCJzdHlsZSIsIndpZHRoIiwiYW5hbHlzaXNfcGVyaW9kIiwic3RhcnRfZGF0ZSIsImVuZF9kYXRlIiwic291cmNlc191c2VkIiwidmVyaWZpY2F0aW9uX2xldmVsIiwiYW5hbHl6ZWRfYXJlYV9rbTIiLCJ2ZWdldGF0aW9uX2luZGljZXMiLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwia2V5IiwicmVwbGFjZSIsIndhcm5pbmdzIiwibGVuZ3RoIiwidWwiLCJ3YXJuaW5nIiwiaW5kZXgiLCJsaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Verification/VerificationResults.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkApiStatus: () => (/* binding */ checkApiStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getDetailedHealth: () => (/* binding */ getDetailedHealth),\n/* harmony export */   getHealth: () => (/* binding */ getHealth),\n/* harmony export */   getMockHealthStatus: () => (/* binding */ getMockHealthStatus),\n/* harmony export */   getMockVerification: () => (/* binding */ getMockVerification),\n/* harmony export */   submitVerification: () => (/* binding */ submitVerification)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n// API Client for CarbonLedger Frontend\n\n\nclass ApiClient {\n    constructor(){\n        this.baseURL = \"http://127.0.0.1:8001\" || 0;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: this.baseURL,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n    setupInterceptors() {\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            // Add request timestamp for debugging\n            config.metadata = {\n                startTime: new Date()\n            };\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>{\n            // Calculate request duration\n            const endTime = new Date();\n            const duration = endTime.getTime() - response.config.metadata?.startTime?.getTime();\n            if (true) {\n                console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);\n            }\n            return response;\n        }, (error)=>{\n            this.handleApiError(error);\n            return Promise.reject(error);\n        });\n    }\n    handleApiError(error) {\n        if (error.response) {\n            // Server responded with error status\n            const status = error.response.status;\n            const data = error.response.data;\n            let message = \"An error occurred\";\n            if (data?.user_message) {\n                message = data.user_message;\n            } else if (data?.detail) {\n                message = data.detail;\n            } else if (status === 429) {\n                message = \"Too many requests. Please try again later.\";\n            } else if (status === 500) {\n                message = \"Server error. Please try again later.\";\n            } else if (status === 503) {\n                message = \"Service temporarily unavailable.\";\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(message);\n        } else if (error.request) {\n            // Network error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n        } else {\n            // Other error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"An unexpected error occurred.\");\n        }\n    }\n    // Health endpoints\n    async getHealth() {\n        const response = await this.client.get(\"/health\");\n        return response.data;\n    }\n    async getDetailedHealth() {\n        const response = await this.client.get(\"/health/detailed\");\n        return response.data;\n    }\n    // Verification endpoints\n    async submitVerification(request) {\n        const response = await this.client.post(\"/api/v1/verify\", request);\n        return response.data;\n    }\n    // Blockchain endpoints (when enabled)\n    async mintTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/mint\", request);\n        return response.data;\n    }\n    async retireTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/retire\", request);\n        return response.data;\n    }\n    async getTokenBalance(address, tokenId) {\n        const response = await this.client.get(`/api/v1/blockchain/balance/${address}/${tokenId}`);\n        return response.data;\n    }\n    async getTokenMetadata(tokenId) {\n        const response = await this.client.get(`/api/v1/blockchain/metadata/${tokenId}`);\n        return response.data;\n    }\n    async getGasEstimate(operation) {\n        const response = await this.client.get(`/api/v1/blockchain/gas-estimate/${operation}`);\n        return response.data;\n    }\n    async getBlockchainHealth() {\n        const response = await this.client.get(\"/api/v1/blockchain/health\");\n        return response.data;\n    }\n    // Utility methods\n    async checkApiStatus() {\n        try {\n            await this.getHealth();\n            return true;\n        } catch  {\n            return false;\n        }\n    }\n    getBaseURL() {\n        return this.baseURL;\n    }\n    // Mock data methods for development (when backend is not available)\n    async getMockVerification(request) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const mockResponse = {\n            verification_id: `mock-${Date.now()}`,\n            project_id: Math.floor(Math.random() * 1000),\n            project_name: request.project_name,\n            verification_complete: true,\n            deforestation_detected: Math.random() > 0.7,\n            deforested_area_km2: Math.random() * 10,\n            confidence_score: 0.85 + Math.random() * 0.15,\n            data_quality_score: 0.9 + Math.random() * 0.1,\n            verification_level: \"enhanced\",\n            sources_used: 3,\n            vegetation_indices: {\n                ndvi_mean: 0.7 + Math.random() * 0.2,\n                ndvi_std: 0.1 + Math.random() * 0.05,\n                evi_mean: 0.6 + Math.random() * 0.2\n            },\n            temporal_analysis: {\n                trend: Math.random() > 0.5 ? \"stable\" : \"declining\",\n                change_rate: -0.02 + Math.random() * 0.04\n            },\n            area_checked: {\n                total_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111,\n                analyzed_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111 * 0.95\n            },\n            analysis_period: {\n                start_date: \"2023-01-01\",\n                end_date: \"2023-12-31\"\n            },\n            verified_at: new Date().toISOString(),\n            processing_time_seconds: 45.2,\n            warnings: Math.random() > 0.8 ? [\n                \"Cloud cover detected in some areas\"\n            ] : undefined,\n            metadata: {\n                satellite_sources: [\n                    \"Landsat-8\",\n                    \"Sentinel-2\"\n                ],\n                processing_version: \"2.1.0\"\n            }\n        };\n        return mockResponse;\n    }\n    async getMockHealthStatus() {\n        const mockHealth = {\n            status: \"healthy\",\n            timestamp: new Date().toISOString(),\n            response_time_ms: 150,\n            version: \"1.0.0\",\n            environment: \"development\",\n            components: [\n                {\n                    name: \"database\",\n                    status: \"healthy\",\n                    message: \"Database connection healthy\",\n                    response_time_ms: 50\n                },\n                {\n                    name: \"earth_engine\",\n                    status: \"degraded\",\n                    message: \"Earth Engine not configured (development mode)\",\n                    response_time_ms: 0\n                },\n                {\n                    name: \"blockchain\",\n                    status: \"healthy\",\n                    message: \"Blockchain configuration loaded\",\n                    response_time_ms: 25\n                }\n            ]\n        };\n        return mockHealth;\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n// Export individual methods for convenience\nconst { getHealth, getDetailedHealth, submitVerification, checkApiStatus, getMockVerification, getMockHealthStatus } = apiClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8c2030a095e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FyYm9ubGVkZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9lYjUyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOGMyMDMwYTA5NWU5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n    description: \"Advanced satellite verification platform for carbon credit projects using AI-powered deforestation detection\",\n    keywords: \"carbon credits, satellite verification, deforestation, blockchain, environmental monitoring\",\n    authors: [\n        {\n            name: \"CarbonLedger Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#22c55e\",\n    openGraph: {\n        title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n        description: \"Advanced satellite verification platform for carbon credit projects\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n        description: \"Advanced satellite verification platform for carbon credit projects\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: \"#363636\",\n                            color: \"#fff\"\n                        },\n                        success: {\n                            duration: 3000,\n                            iconTheme: {\n                                primary: \"#22c55e\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            iconTheme: {\n                                primary: \"#ef4444\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/verify/page.tsx":
/*!*********************************!*\
  !*** ./src/app/verify/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/cl/frontend/src/app/verify/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/framer-motion","vendor-chunks/@heroicons","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fverify%2Fpage&page=%2Fverify%2Fpage&appPaths=%2Fverify%2Fpage&pagePath=private-next-app-dir%2Fverify%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();