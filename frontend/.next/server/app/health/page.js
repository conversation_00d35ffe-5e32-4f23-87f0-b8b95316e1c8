/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/health/page";
exports.ids = ["app/health/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fhealth%2Fpage&page=%2Fhealth%2Fpage&appPaths=%2Fhealth%2Fpage&pagePath=private-next-app-dir%2Fhealth%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fhealth%2Fpage&page=%2Fhealth%2Fpage&appPaths=%2Fhealth%2Fpage&pagePath=private-next-app-dir%2Fhealth%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'health',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/health/page.tsx */ \"(rsc)/./src/app/health/page.tsx\")), \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/cl/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/cl/frontend/src/app/health/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/health/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/health/page\",\n        pathname: \"/health\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fhealth%2Fpage&page=%2Fhealth%2Fpage&appPaths=%2Fhealth%2Fpage&pagePath=private-next-app-dir%2Fhealth%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnMuY2h5JTJGY2wlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRnJlYWN0LWhvdC10b2FzdCUyRmRpc3QlMkZpbmRleC5tanMmbW9kdWxlcz0lMkZVc2VycyUyRnMuY2h5JTJGY2wlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NhcmJvbmxlZGdlci1mcm9udGVuZC8/NzRjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zLmNoeS9jbC9mcm9udGVuZC9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fhealth%2Fpage.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fhealth%2Fpage.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/health/page.tsx */ \"(ssr)/./src/app/health/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZoZWFsdGglMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvP2U2MDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcy5jaHkvY2wvZnJvbnRlbmQvc3JjL2FwcC9oZWFsdGgvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fhealth%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/health/page.tsx":
/*!*********************************!*\
  !*** ./src/app/health/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HealthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/LinkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,CloudIcon,CpuChipIcon,ExclamationTriangleIcon,LinkIcon,ServerIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction HealthPage() {\n    const [healthStatus, setHealthStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchHealthStatus();\n        let interval;\n        if (autoRefresh) {\n            interval = setInterval(fetchHealthStatus, 30000); // Every 30 seconds\n        }\n        return ()=>{\n            if (interval) clearInterval(interval);\n        };\n    }, [\n        autoRefresh\n    ]);\n    const fetchHealthStatus = async ()=>{\n        try {\n            const status = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDetailedHealth();\n            setHealthStatus(status);\n            setLastUpdated(new Date());\n        } catch (error) {\n            // If API is not available, use mock data\n            try {\n                const mockStatus = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getMockHealthStatus();\n                setHealthStatus(mockStatus);\n                setLastUpdated(new Date());\n            } catch (mockError) {\n                console.error(\"Failed to fetch health status:\", mockError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getStatusIcon = (status, size = \"h-6 w-6\")=>{\n        const iconClass = `${size}`;\n        switch(status){\n            case \"healthy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: `${iconClass} text-green-500`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, this);\n            case \"degraded\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: `${iconClass} text-yellow-500`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, this);\n            case \"unhealthy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: `${iconClass} text-red-500`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: `${iconClass} text-gray-500`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"healthy\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            case \"degraded\":\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n            case \"unhealthy\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            default:\n                return \"text-gray-600 bg-gray-50 border-gray-200\";\n        }\n    };\n    const getComponentIcon = (name)=>{\n        switch(name.toLowerCase()){\n            case \"database\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n            case \"earth_engine\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 16\n                }, this);\n            case \"blockchain\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 16\n                }, this);\n            case \"system_resources\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"System Health\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Monitor the health and performance of all system components\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: autoRefresh,\n                                                    onChange: (e)=>setAutoRefresh(e.target.checked),\n                                                    className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-sm text-gray-600\",\n                                                    children: \"Auto-refresh\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: fetchHealthStatus,\n                                            disabled: isLoading,\n                                            className: \"btn-outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: `h-4 w-4 mr-2 ${isLoading ? \"animate-spin\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && !healthStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded w-1/2 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, this) : healthStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.1\n                                },\n                                className: `p-6 rounded-lg border-2 mb-8 ${getStatusColor(healthStatus.status)}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                getStatusIcon(healthStatus.status, \"h-8 w-8\"),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-bold capitalize\",\n                                                            children: [\n                                                                \"System \",\n                                                                healthStatus.status\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm opacity-90\",\n                                                            children: [\n                                                                \"Overall response time: \",\n                                                                healthStatus.response_time_ms,\n                                                                \"ms\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        \"Version \",\n                                                        healthStatus.version\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs opacity-75 capitalize\",\n                                                    children: healthStatus.environment\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n                                children: healthStatus.components.map((component, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2 + index * 0.1\n                                        },\n                                        className: \"card hover:shadow-md transition-shadow duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-600\",\n                                                                children: getComponentIcon(component.name)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-medium text-gray-900 capitalize\",\n                                                                        children: component.name.replace(\"_\", \" \")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: component.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    getStatusIcon(component.status)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `font-medium capitalize ${component.status === \"healthy\" ? \"text-green-600\" : component.status === \"degraded\" ? \"text-yellow-600\" : \"text-red-600\"}`,\n                                                                children: component.status\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    component.response_time_ms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Response Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    component.response_time_ms,\n                                                                    \"ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, component.name, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.6\n                                },\n                                className: \"card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"System Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Environment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-900 capitalize\",\n                                                        children: healthStatus.environment\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Version\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: healthStatus.version\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: lastUpdated.toLocaleTimeString()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CheckCircleIcon_ClockIcon_CloudIcon_CpuChipIcon_ExclamationTriangleIcon_LinkIcon_ServerIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"Unable to fetch system status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"There was an error connecting to the health monitoring service.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchHealthStatus,\n                                className: \"btn-primary\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/health/page.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/health/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst footerLinks = {\n    product: [\n        {\n            name: \"Features\",\n            href: \"#\"\n        },\n        {\n            name: \"Pricing\",\n            href: \"#\"\n        },\n        {\n            name: \"API Documentation\",\n            href: \"/docs\"\n        },\n        {\n            name: \"System Status\",\n            href: \"/health\"\n        }\n    ],\n    company: [\n        {\n            name: \"About\",\n            href: \"#\"\n        },\n        {\n            name: \"Blog\",\n            href: \"#\"\n        },\n        {\n            name: \"Careers\",\n            href: \"#\"\n        },\n        {\n            name: \"Contact\",\n            href: \"#\"\n        }\n    ],\n    resources: [\n        {\n            name: \"Help Center\",\n            href: \"#\"\n        },\n        {\n            name: \"Privacy Policy\",\n            href: \"#\"\n        },\n        {\n            name: \"Terms of Service\",\n            href: \"#\"\n        },\n        {\n            name: \"Security\",\n            href: \"#\"\n        }\n    ],\n    social: [\n        {\n            name: \"GitHub\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"Twitter\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-8 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.product.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Company\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.company.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.resources.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Connect\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex space-x-4\",\n                                    children: footerLinks.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: \"text-gray-400 hover:text-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"h-6 w-6\",\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-6 md:flex md:items-center md:justify-between lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-6 w-6 items-center justify-center rounded bg-primary-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 text-white\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: 1.5,\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            d: \"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"CarbonLedger\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 md:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"\\xa9 2024 CarbonLedger. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Made with\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-3 w-3 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"for the planet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Verify Project\",\n        href: \"/verify\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"System Health\",\n        href: \"/health\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-white\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            strokeWidth: 1.5,\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                d: \"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"CarbonLedger\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 -mt-1\",\n                                                children: \"Satellite Verification\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${isActive ? \"text-primary-600 bg-primary-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"System Online\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\",\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"block h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"block h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"md:hidden bg-white border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 pt-2 pb-3 space-y-1\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `flex items-center px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${isActive ? \"text-primary-600 bg-primary-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 21\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 px-4 py-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"System Online\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/Layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MainLayout({ children, showFooter = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 19,\n                columnNumber: 22\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzhCO0FBQ0E7QUFPZixTQUFTRSxXQUFXLEVBQUVDLFFBQVEsRUFBRUMsYUFBYSxJQUFJLEVBQW1CO0lBQ2pGLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ04sK0NBQU1BOzs7OzswQkFDUCw4REFBQ087Z0JBQUtELFdBQVU7MEJBQ2JIOzs7Ozs7WUFFRkMsNEJBQWMsOERBQUNILCtDQUFNQTs7Ozs7Ozs7Ozs7QUFHNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dC50c3g/ZTNjNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBIZWFkZXIgZnJvbSAnLi9IZWFkZXInO1xuaW1wb3J0IEZvb3RlciBmcm9tICcuL0Zvb3Rlcic7XG5cbmludGVyZmFjZSBNYWluTGF5b3V0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xuICBzaG93Rm9vdGVyPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWFpbkxheW91dCh7IGNoaWxkcmVuLCBzaG93Rm9vdGVyID0gdHJ1ZSB9OiBNYWluTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICA8SGVhZGVyIC8+XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JheS01MFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L21haW4+XG4gICAgICB7c2hvd0Zvb3RlciAmJiA8Rm9vdGVyIC8+fVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWRlciIsIkZvb3RlciIsIk1haW5MYXlvdXQiLCJjaGlsZHJlbiIsInNob3dGb290ZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkApiStatus: () => (/* binding */ checkApiStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getDetailedHealth: () => (/* binding */ getDetailedHealth),\n/* harmony export */   getHealth: () => (/* binding */ getHealth),\n/* harmony export */   getMockHealthStatus: () => (/* binding */ getMockHealthStatus),\n/* harmony export */   getMockVerification: () => (/* binding */ getMockVerification),\n/* harmony export */   submitVerification: () => (/* binding */ submitVerification)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n// API Client for CarbonLedger Frontend\n\n\nclass ApiClient {\n    constructor(){\n        this.baseURL = \"http://127.0.0.1:8001\" || 0;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: this.baseURL,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n    setupInterceptors() {\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            // Add request timestamp for debugging\n            config.metadata = {\n                startTime: new Date()\n            };\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>{\n            // Calculate request duration\n            const endTime = new Date();\n            const duration = endTime.getTime() - response.config.metadata?.startTime?.getTime();\n            if (true) {\n                console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);\n            }\n            return response;\n        }, (error)=>{\n            this.handleApiError(error);\n            return Promise.reject(error);\n        });\n    }\n    handleApiError(error) {\n        if (error.response) {\n            // Server responded with error status\n            const status = error.response.status;\n            const data = error.response.data;\n            let message = \"An error occurred\";\n            if (data?.user_message) {\n                message = data.user_message;\n            } else if (data?.detail) {\n                message = data.detail;\n            } else if (status === 429) {\n                message = \"Too many requests. Please try again later.\";\n            } else if (status === 500) {\n                message = \"Server error. Please try again later.\";\n            } else if (status === 503) {\n                message = \"Service temporarily unavailable.\";\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(message);\n        } else if (error.request) {\n            // Network error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n        } else {\n            // Other error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"An unexpected error occurred.\");\n        }\n    }\n    // Health endpoints\n    async getHealth() {\n        const response = await this.client.get(\"/health\");\n        return response.data;\n    }\n    async getDetailedHealth() {\n        const response = await this.client.get(\"/health/detailed\");\n        return response.data;\n    }\n    // Verification endpoints\n    async submitVerification(request) {\n        const response = await this.client.post(\"/api/v1/verify\", request);\n        return response.data;\n    }\n    // Blockchain endpoints (when enabled)\n    async mintTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/mint\", request);\n        return response.data;\n    }\n    async retireTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/retire\", request);\n        return response.data;\n    }\n    async getTokenBalance(address, tokenId) {\n        const response = await this.client.get(`/api/v1/blockchain/balance/${address}/${tokenId}`);\n        return response.data;\n    }\n    async getTokenMetadata(tokenId) {\n        const response = await this.client.get(`/api/v1/blockchain/metadata/${tokenId}`);\n        return response.data;\n    }\n    async getGasEstimate(operation) {\n        const response = await this.client.get(`/api/v1/blockchain/gas-estimate/${operation}`);\n        return response.data;\n    }\n    async getBlockchainHealth() {\n        const response = await this.client.get(\"/api/v1/blockchain/health\");\n        return response.data;\n    }\n    // Utility methods\n    async checkApiStatus() {\n        try {\n            await this.getHealth();\n            return true;\n        } catch  {\n            return false;\n        }\n    }\n    getBaseURL() {\n        return this.baseURL;\n    }\n    // Mock data methods for development (when backend is not available)\n    async getMockVerification(request) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const mockResponse = {\n            verification_id: `mock-${Date.now()}`,\n            project_id: Math.floor(Math.random() * 1000),\n            project_name: request.project_name,\n            verification_complete: true,\n            deforestation_detected: Math.random() > 0.7,\n            deforested_area_km2: Math.random() * 10,\n            confidence_score: 0.85 + Math.random() * 0.15,\n            data_quality_score: 0.9 + Math.random() * 0.1,\n            verification_level: \"enhanced\",\n            sources_used: 3,\n            vegetation_indices: {\n                ndvi_mean: 0.7 + Math.random() * 0.2,\n                ndvi_std: 0.1 + Math.random() * 0.05,\n                evi_mean: 0.6 + Math.random() * 0.2\n            },\n            temporal_analysis: {\n                trend: Math.random() > 0.5 ? \"stable\" : \"declining\",\n                change_rate: -0.02 + Math.random() * 0.04\n            },\n            area_checked: {\n                total_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111,\n                analyzed_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111 * 0.95\n            },\n            analysis_period: {\n                start_date: \"2023-01-01\",\n                end_date: \"2023-12-31\"\n            },\n            verified_at: new Date().toISOString(),\n            processing_time_seconds: 45.2,\n            warnings: Math.random() > 0.8 ? [\n                \"Cloud cover detected in some areas\"\n            ] : undefined,\n            metadata: {\n                satellite_sources: [\n                    \"Landsat-8\",\n                    \"Sentinel-2\"\n                ],\n                processing_version: \"2.1.0\"\n            }\n        };\n        return mockResponse;\n    }\n    async getMockHealthStatus() {\n        const mockHealth = {\n            status: \"healthy\",\n            timestamp: new Date().toISOString(),\n            response_time_ms: 150,\n            version: \"1.0.0\",\n            environment: \"development\",\n            components: [\n                {\n                    name: \"database\",\n                    status: \"healthy\",\n                    message: \"Database connection healthy\",\n                    response_time_ms: 50\n                },\n                {\n                    name: \"earth_engine\",\n                    status: \"degraded\",\n                    message: \"Earth Engine not configured (development mode)\",\n                    response_time_ms: 0\n                },\n                {\n                    name: \"blockchain\",\n                    status: \"healthy\",\n                    message: \"Blockchain configuration loaded\",\n                    response_time_ms: 25\n                }\n            ]\n        };\n        return mockHealth;\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n// Export individual methods for convenience\nconst { getHealth, getDetailedHealth, submitVerification, checkApiStatus, getMockVerification, getMockHealthStatus } = apiClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8c2030a095e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FyYm9ubGVkZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9lYjUyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOGMyMDMwYTA5NWU5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/health/page.tsx":
/*!*********************************!*\
  !*** ./src/app/health/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/cl/frontend/src/app/health/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n    description: \"Advanced satellite verification platform for carbon credit projects using AI-powered deforestation detection\",\n    keywords: \"carbon credits, satellite verification, deforestation, blockchain, environmental monitoring\",\n    authors: [\n        {\n            name: \"CarbonLedger Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#22c55e\",\n    openGraph: {\n        title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n        description: \"Advanced satellite verification platform for carbon credit projects\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n        description: \"Advanced satellite verification platform for carbon credit projects\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: \"#363636\",\n                            color: \"#fff\"\n                        },\n                        success: {\n                            duration: 3000,\n                            iconTheme: {\n                                primary: \"#22c55e\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            iconTheme: {\n                                primary: \"#ef4444\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/@heroicons","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fhealth%2Fpage&page=%2Fhealth%2Fpage&appPaths=%2Fhealth%2Fpage&pagePath=private-next-app-dir%2Fhealth%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();