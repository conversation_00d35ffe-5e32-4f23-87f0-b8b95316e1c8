/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/cl/frontend/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/cl/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/cl/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZhcHAtcm91dGVyLmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcy5jaHklMkZjbCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGcy5jaHklMkZjbCUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQW9IO0FBQ3BILDBPQUF3SDtBQUN4SCx3T0FBdUg7QUFDdkgsa1BBQTRIO0FBQzVILHNRQUFzSTtBQUN0SSIsInNvdXJjZXMiOlsid2VicGFjazovL2NhcmJvbmxlZGdlci1mcm9udGVuZC8/ODQ3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zLmNoeS9jbC9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zLmNoeS9jbC9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcy5jaHkvY2wvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcy5jaHkvY2wvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zLmNoeS9jbC9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zLmNoeS9jbC9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLXNlYXJjaHBhcmFtcy1iYWlsb3V0LXByb3ZpZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnMuY2h5JTJGY2wlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRnJlYWN0LWhvdC10b2FzdCUyRmRpc3QlMkZpbmRleC5tanMmbW9kdWxlcz0lMkZVc2VycyUyRnMuY2h5JTJGY2wlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NhcmJvbmxlZGdlci1mcm9udGVuZC8/NzRjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9zLmNoeS9jbC9mcm9udGVuZC9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZzLmNoeSUyRmNsJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvP2Y3ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcy5jaHkvY2wvZnJvbnRlbmQvc3JjL2FwcC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _components_Dashboard_DashboardStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Dashboard/DashboardStats */ \"(ssr)/./src/components/Dashboard/DashboardStats.tsx\");\n/* harmony import */ var _components_Dashboard_RecentVerifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Dashboard/RecentVerifications */ \"(ssr)/./src/components/Dashboard/RecentVerifications.tsx\");\n/* harmony import */ var _components_Dashboard_SystemStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Dashboard/SystemStatus */ \"(ssr)/./src/components/Dashboard/SystemStatus.tsx\");\n/* harmony import */ var _components_Dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Dashboard/QuickActions */ \"(ssr)/./src/components/Dashboard/QuickActions.tsx\");\n/* harmony import */ var _components_Dashboard_VerificationTrends__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Dashboard/VerificationTrends */ \"(ssr)/./src/components/Dashboard/VerificationTrends.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate initial data loading\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 1000);\n        return ()=>clearTimeout(timer);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"Loading dashboard...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-gray-600\",\n                                children: \"Monitor your carbon credit verification projects and system performance\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.1\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_QuickActions__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_DashboardStats__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_VerificationTrends__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_RecentVerifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.5\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_SystemStatus__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/DashboardStats.tsx":
/*!*****************************************************!*\
  !*** ./src/components/Dashboard/DashboardStats.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardStats() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchStats = async ()=>{\n            try {\n                setIsLoading(true);\n                const dashboardData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getDashboardStats();\n                // Format the data for display\n                const formattedStats = [\n                    {\n                        name: \"Total Verifications\",\n                        value: dashboardData.total_verifications.toLocaleString(),\n                        change: \"+12%\",\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                        description: \"Projects verified to date\"\n                    },\n                    {\n                        name: \"Area Verified\",\n                        value: `${Math.round(dashboardData.total_area_verified_km2).toLocaleString()} km²`,\n                        change: \"+8%\",\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        description: \"Total forest area analyzed\"\n                    },\n                    {\n                        name: \"Deforestation Detected\",\n                        value: dashboardData.deforestation_detected_count.toLocaleString(),\n                        change: \"-5%\",\n                        changeType: \"decrease\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                        description: \"Projects with deforestation alerts\"\n                    },\n                    {\n                        name: \"Average Confidence\",\n                        value: `${Math.round(dashboardData.average_confidence_score * 100)}%`,\n                        change: \"+2%\",\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        description: \"Average verification confidence\"\n                    }\n                ];\n                setStats(formattedStats);\n            } catch (error) {\n                console.error(\"Failed to fetch dashboard stats:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to load dashboard statistics\");\n                // Fallback to mock data if API fails\n                const fallbackStats = [\n                    {\n                        name: \"Total Verifications\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                        description: \"Unable to load data\"\n                    },\n                    {\n                        name: \"Area Verified\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        description: \"Unable to load data\"\n                    },\n                    {\n                        name: \"Deforestation Detected\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                        description: \"Unable to load data\"\n                    },\n                    {\n                        name: \"Average Confidence\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        description: \"Unable to load data\"\n                    }\n                ];\n                setStats(fallbackStats);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchStats();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n            children: [\n                ...Array(4)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gray-200 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-5 w-0 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded w-1/2 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this)\n                }, i, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n        children: stats.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: index * 0.1\n                },\n                className: \"card hover:shadow-md transition-shadow duration-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-8 w-8 items-center justify-center rounded-md bg-primary-500 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-5 w-5\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-5 w-0 flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                        className: \"flex items-baseline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: item.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `ml-2 flex items-baseline text-sm font-semibold ${item.changeType === \"increase\" ? \"text-green-600\" : item.changeType === \"decrease\" ? \"text-red-600\" : \"text-gray-500\"}`,\n                                                children: [\n                                                    item.changeType === \"increase\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-3 w-3 flex-shrink-0 self-center text-green-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.changeType === \"decrease\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-3 w-3 flex-shrink-0 self-center text-red-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: [\n                                                            item.changeType === \"increase\" ? \"Increased\" : \"Decreased\",\n                                                            \" by\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.change\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this)\n            }, item.name, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/DashboardStats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/QuickActions.tsx":
/*!***************************************************!*\
  !*** ./src/components/Dashboard/QuickActions.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuickActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst actions = [\n    {\n        name: \"New Verification\",\n        description: \"Start verifying a new carbon credit project\",\n        href: \"/verify\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"bg-primary-500 hover:bg-primary-600\"\n    },\n    {\n        name: \"View Analytics\",\n        description: \"Analyze verification trends and patterns\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"bg-blue-500 hover:bg-blue-600\"\n    },\n    {\n        name: \"Browse Projects\",\n        description: \"View all verified projects and their status\",\n        href: \"/projects\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"bg-purple-500 hover:bg-purple-600\"\n    },\n    {\n        name: \"System Settings\",\n        description: \"Configure system preferences and settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"bg-gray-500 hover:bg-gray-600\"\n    }\n];\nfunction QuickActions() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Common tasks and shortcuts to get you started\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n                children: actions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.3,\n                            delay: index * 0.1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: action.href,\n                            className: \"group relative block p-6 bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex h-10 w-10 items-center justify-center rounded-lg ${action.color} text-white transition-colors duration-200`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                className: \"h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors duration-200\",\n                                                    children: action.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: action.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-lg bg-gradient-to-r from-primary-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this)\n                    }, action.name, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/QuickActions.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/QuickActions.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/RecentVerifications.tsx":
/*!**********************************************************!*\
  !*** ./src/components/Dashboard/RecentVerifications.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecentVerifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction RecentVerifications() {\n    const [verifications, setVerifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchVerifications = async ()=>{\n            try {\n                setIsLoading(true);\n                const dashboardData = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDashboardStats();\n                // Transform API data to match component format\n                const formattedVerifications = dashboardData.recent_verifications.map((verification)=>({\n                        id: verification.verification_id,\n                        project_name: verification.project_name,\n                        status: verification.verification_complete ? \"completed\" : \"processing\",\n                        deforestation_detected: verification.deforestation_detected,\n                        confidence_score: verification.confidence_score || 0.85,\n                        area_km2: verification.deforested_area_km2,\n                        verified_at: verification.verified_at\n                    }));\n                setVerifications(formattedVerifications);\n            } catch (error) {\n                console.error(\"Failed to fetch recent verifications:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load recent verifications\");\n                // Fallback to empty array if API fails\n                setVerifications([]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchVerifications();\n    }, []);\n    const getStatusIcon = (status, deforestation)=>{\n        switch(status){\n            case \"completed\":\n                return deforestation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status, deforestation)=>{\n        switch(status){\n            case \"completed\":\n                return deforestation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-error\",\n                    children: \"Deforestation Detected\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-success\",\n                    children: \"Verified Clean\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-warning\",\n                    children: \"Processing\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-error\",\n                    children: \"Failed\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-info\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 w-5 bg-gray-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-16 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Recent Verifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/verifications\",\n                        className: \"text-sm text-primary-600 hover:text-primary-500\",\n                        children: \"View all\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: verifications.map((verification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.3,\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    getStatusIcon(verification.status, verification.deforestation_detected),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: verification.project_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: verification.area_km2 > 0 ? `${verification.area_km2.toFixed(1)} km²` : \"Area pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    verification.confidence_score > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            (verification.confidence_score * 100).toFixed(0),\n                                                            \"% confidence\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: formatDate(verification.verified_at)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    getStatusBadge(verification.status, verification.deforestation_detected),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, verification.id, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            verifications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: 1.5,\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0.621 0 1.125-.504 1.125-1.125V9.375c0-.621.504-1.125 1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"No verifications yet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/verify\",\n                        className: \"mt-2 btn-primary inline-flex\",\n                        children: \"Start First Verification\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9EYXNoYm9hcmQvUmVjZW50VmVyaWZpY2F0aW9ucy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2Y7QUFDVTtBQU1GO0FBQ0g7QUFDTTtBQVl6QixTQUFTVTtJQUN0QixNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHWiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUNhLFdBQVdDLGFBQWEsR0FBR2QsK0NBQVFBLENBQUM7SUFFM0NDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWMscUJBQXFCO1lBQ3pCLElBQUk7Z0JBQ0ZELGFBQWE7Z0JBQ2IsTUFBTUUsZ0JBQWdCLE1BQU1SLGtFQUEyQjtnQkFFdkQsK0NBQStDO2dCQUMvQyxNQUFNVSx5QkFBeUNGLGNBQWNHLG9CQUFvQixDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsZUFBa0I7d0JBQ3ZHQyxJQUFJRCxhQUFhRSxlQUFlO3dCQUNoQ0MsY0FBY0gsYUFBYUcsWUFBWTt3QkFDdkNDLFFBQVFKLGFBQWFLLHFCQUFxQixHQUFHLGNBQWM7d0JBQzNEQyx3QkFBd0JOLGFBQWFNLHNCQUFzQjt3QkFDM0RDLGtCQUFrQlAsYUFBYU8sZ0JBQWdCLElBQUk7d0JBQ25EQyxVQUFVUixhQUFhUyxtQkFBbUI7d0JBQzFDQyxhQUFhVixhQUFhVSxXQUFXO29CQUN2QztnQkFFQW5CLGlCQUFpQk07WUFDbkIsRUFBRSxPQUFPYyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMseUNBQXlDQTtnQkFDdkR2QixrREFBS0EsQ0FBQ3VCLEtBQUssQ0FBQztnQkFFWix1Q0FBdUM7Z0JBQ3ZDcEIsaUJBQWlCLEVBQUU7WUFDckIsU0FBVTtnQkFDUkUsYUFBYTtZQUNmO1FBQ0Y7UUFFQUM7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNbUIsZ0JBQWdCLENBQUNULFFBQWdCVTtRQUNyQyxPQUFRVjtZQUNOLEtBQUs7Z0JBQ0gsT0FBT1UsOEJBQ0wsOERBQUM5QixtSkFBdUJBO29CQUFDK0IsV0FBVTs7Ozs7eUNBRW5DLDhEQUFDaEMsbUpBQWVBO29CQUFDZ0MsV0FBVTs7Ozs7O1lBRS9CLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUM5QixtSkFBU0E7b0JBQUM4QixXQUFVOzs7Ozs7WUFDOUIsS0FBSztnQkFDSCxxQkFBTyw4REFBQy9CLG1KQUF1QkE7b0JBQUMrQixXQUFVOzs7Ozs7WUFDNUM7Z0JBQ0UscUJBQU8sOERBQUM5QixtSkFBU0E7b0JBQUM4QixXQUFVOzs7Ozs7UUFDaEM7SUFDRjtJQUVBLE1BQU1DLGlCQUFpQixDQUFDWixRQUFnQlU7UUFDdEMsT0FBUVY7WUFDTixLQUFLO2dCQUNILE9BQU9VLDhCQUNMLDhEQUFDRztvQkFBS0YsV0FBVTs4QkFBYzs7Ozs7eUNBRTlCLDhEQUFDRTtvQkFBS0YsV0FBVTs4QkFBZ0I7Ozs7OztZQUVwQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDRTtvQkFBS0YsV0FBVTs4QkFBZ0I7Ozs7OztZQUN6QyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDRTtvQkFBS0YsV0FBVTs4QkFBYzs7Ozs7O1lBQ3ZDO2dCQUNFLHFCQUFPLDhEQUFDRTtvQkFBS0YsV0FBVTs4QkFBYTs7Ozs7O1FBQ3hDO0lBQ0Y7SUFFQSxNQUFNRyxhQUFhLENBQUNDO1FBQ2xCLE9BQU8sSUFBSUMsS0FBS0QsWUFBWUUsa0JBQWtCLENBQUMsU0FBUztZQUN0REMsT0FBTztZQUNQQyxLQUFLO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxJQUFJakMsV0FBVztRQUNiLHFCQUNFLDhEQUFDa0M7WUFBSVgsV0FBVTtzQkFDYiw0RUFBQ1c7Z0JBQUlYLFdBQVU7O2tDQUNiLDhEQUFDVzt3QkFBSVgsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDVzt3QkFBSVgsV0FBVTtrQ0FDWjsrQkFBSVksTUFBTTt5QkFBRyxDQUFDNUIsR0FBRyxDQUFDLENBQUM2QixHQUFHQyxrQkFDckIsOERBQUNIO2dDQUFZWCxXQUFVOztrREFDckIsOERBQUNXO3dDQUFJWCxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNXO3dDQUFJWCxXQUFVOzswREFDYiw4REFBQ1c7Z0RBQUlYLFdBQVU7Ozs7OzswREFDZiw4REFBQ1c7Z0RBQUlYLFdBQVU7Ozs7Ozs7Ozs7OztrREFFakIsOERBQUNXO3dDQUFJWCxXQUFVOzs7Ozs7OytCQU5QYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBYXRCO0lBRUEscUJBQ0UsOERBQUNIO1FBQUlYLFdBQVU7OzBCQUNiLDhEQUFDVztnQkFBSVgsV0FBVTs7a0NBQ2IsOERBQUNlO3dCQUFHZixXQUFVO2tDQUFvQzs7Ozs7O2tDQUNsRCw4REFBQ2xDLGtEQUFJQTt3QkFDSGtELE1BQUs7d0JBQ0xoQixXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7MEJBS0gsOERBQUNXO2dCQUFJWCxXQUFVOzBCQUNaekIsY0FBY1MsR0FBRyxDQUFDLENBQUNDLGNBQWNnQyxzQkFDaEMsOERBQUNsRCxpREFBTUEsQ0FBQzRDLEdBQUc7d0JBRVRPLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUc7d0JBQzdCQyxTQUFTOzRCQUFFRixTQUFTOzRCQUFHQyxHQUFHO3dCQUFFO3dCQUM1QkUsWUFBWTs0QkFBRUMsVUFBVTs0QkFBS0MsT0FBT1AsUUFBUTt3QkFBSTt3QkFDaERqQixXQUFVOzswQ0FFViw4REFBQ1c7Z0NBQUlYLFdBQVU7O29DQUNaRixjQUFjYixhQUFhSSxNQUFNLEVBQUVKLGFBQWFNLHNCQUFzQjtrREFDdkUsOERBQUNvQjt3Q0FBSVgsV0FBVTs7MERBQ2IsOERBQUN5QjtnREFBRXpCLFdBQVU7MERBQ1ZmLGFBQWFHLFlBQVk7Ozs7OzswREFFNUIsOERBQUN1QjtnREFBSVgsV0FBVTs7a0VBQ2IsOERBQUN5Qjt3REFBRXpCLFdBQVU7a0VBQ1ZmLGFBQWFRLFFBQVEsR0FBRyxJQUFJLENBQUMsRUFBRVIsYUFBYVEsUUFBUSxDQUFDaUMsT0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUc7Ozs7OztvREFFMUV6QyxhQUFhTyxnQkFBZ0IsR0FBRyxtQkFDL0IsOERBQUNpQzt3REFBRXpCLFdBQVU7OzREQUNUZixDQUFBQSxhQUFhTyxnQkFBZ0IsR0FBRyxHQUFFLEVBQUdrQyxPQUFPLENBQUM7NERBQUc7Ozs7Ozs7a0VBR3RELDhEQUFDRDt3REFBRXpCLFdBQVU7a0VBQ1ZHLFdBQVdsQixhQUFhVSxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSzVDLDhEQUFDZ0I7Z0NBQUlYLFdBQVU7O29DQUNaQyxlQUFlaEIsYUFBYUksTUFBTSxFQUFFSixhQUFhTSxzQkFBc0I7a0RBQ3hFLDhEQUFDb0M7d0NBQU8zQixXQUFVO2tEQUNoQiw0RUFBQzdCLG1KQUFPQTs0Q0FBQzZCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1QkE5QmxCZixhQUFhQyxFQUFFOzs7Ozs7Ozs7O1lBcUN6QlgsY0FBY3FELE1BQU0sS0FBSyxtQkFDeEIsOERBQUNqQjtnQkFBSVgsV0FBVTs7a0NBQ2IsOERBQUM2Qjt3QkFDQzdCLFdBQVU7d0JBQ1Y4QixNQUFLO3dCQUNMQyxTQUFRO3dCQUNSQyxhQUFhO3dCQUNiQyxRQUFPO2tDQUVQLDRFQUFDQzs0QkFDQ0MsZUFBYzs0QkFDZEMsZ0JBQWU7NEJBQ2ZDLEdBQUU7Ozs7Ozs7Ozs7O2tDQUdOLDhEQUFDWjt3QkFBRXpCLFdBQVU7a0NBQWdCOzs7Ozs7a0NBQzdCLDhEQUFDbEMsa0RBQUlBO3dCQUFDa0QsTUFBSzt3QkFBVWhCLFdBQVU7a0NBQStCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9EYXNoYm9hcmQvUmVjZW50VmVyaWZpY2F0aW9ucy50c3g/Mjc2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQge1xuICBDaGVja0NpcmNsZUljb24sXG4gIEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uLFxuICBDbG9ja0ljb24sXG4gIEV5ZUljb24sXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgYXBpQ2xpZW50IGZyb20gJ0AvbGliL2FwaSc7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3JlYWN0LWhvdC10b2FzdCc7XG5cbmludGVyZmFjZSBWZXJpZmljYXRpb24ge1xuICBpZDogc3RyaW5nO1xuICBwcm9qZWN0X25hbWU6IHN0cmluZztcbiAgc3RhdHVzOiAnY29tcGxldGVkJyB8ICdwcm9jZXNzaW5nJyB8ICdmYWlsZWQnO1xuICBkZWZvcmVzdGF0aW9uX2RldGVjdGVkOiBib29sZWFuO1xuICBjb25maWRlbmNlX3Njb3JlOiBudW1iZXI7XG4gIGFyZWFfa20yOiBudW1iZXI7XG4gIHZlcmlmaWVkX2F0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJlY2VudFZlcmlmaWNhdGlvbnMoKSB7XG4gIGNvbnN0IFt2ZXJpZmljYXRpb25zLCBzZXRWZXJpZmljYXRpb25zXSA9IHVzZVN0YXRlPFZlcmlmaWNhdGlvbltdPihbXSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGZldGNoVmVyaWZpY2F0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICAgICAgY29uc3QgZGFzaGJvYXJkRGF0YSA9IGF3YWl0IGFwaUNsaWVudC5nZXREYXNoYm9hcmRTdGF0cygpO1xuXG4gICAgICAgIC8vIFRyYW5zZm9ybSBBUEkgZGF0YSB0byBtYXRjaCBjb21wb25lbnQgZm9ybWF0XG4gICAgICAgIGNvbnN0IGZvcm1hdHRlZFZlcmlmaWNhdGlvbnM6IFZlcmlmaWNhdGlvbltdID0gZGFzaGJvYXJkRGF0YS5yZWNlbnRfdmVyaWZpY2F0aW9ucy5tYXAoKHZlcmlmaWNhdGlvbikgPT4gKHtcbiAgICAgICAgICBpZDogdmVyaWZpY2F0aW9uLnZlcmlmaWNhdGlvbl9pZCxcbiAgICAgICAgICBwcm9qZWN0X25hbWU6IHZlcmlmaWNhdGlvbi5wcm9qZWN0X25hbWUsXG4gICAgICAgICAgc3RhdHVzOiB2ZXJpZmljYXRpb24udmVyaWZpY2F0aW9uX2NvbXBsZXRlID8gJ2NvbXBsZXRlZCcgOiAncHJvY2Vzc2luZycsXG4gICAgICAgICAgZGVmb3Jlc3RhdGlvbl9kZXRlY3RlZDogdmVyaWZpY2F0aW9uLmRlZm9yZXN0YXRpb25fZGV0ZWN0ZWQsXG4gICAgICAgICAgY29uZmlkZW5jZV9zY29yZTogdmVyaWZpY2F0aW9uLmNvbmZpZGVuY2Vfc2NvcmUgfHwgMC44NSxcbiAgICAgICAgICBhcmVhX2ttMjogdmVyaWZpY2F0aW9uLmRlZm9yZXN0ZWRfYXJlYV9rbTIsXG4gICAgICAgICAgdmVyaWZpZWRfYXQ6IHZlcmlmaWNhdGlvbi52ZXJpZmllZF9hdCxcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIHNldFZlcmlmaWNhdGlvbnMoZm9ybWF0dGVkVmVyaWZpY2F0aW9ucyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggcmVjZW50IHZlcmlmaWNhdGlvbnM6JywgZXJyb3IpO1xuICAgICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIGxvYWQgcmVjZW50IHZlcmlmaWNhdGlvbnMnKTtcblxuICAgICAgICAvLyBGYWxsYmFjayB0byBlbXB0eSBhcnJheSBpZiBBUEkgZmFpbHNcbiAgICAgICAgc2V0VmVyaWZpY2F0aW9ucyhbXSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBmZXRjaFZlcmlmaWNhdGlvbnMoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGdldFN0YXR1c0ljb24gPSAoc3RhdHVzOiBzdHJpbmcsIGRlZm9yZXN0YXRpb246IGJvb2xlYW4pID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnY29tcGxldGVkJzpcbiAgICAgICAgcmV0dXJuIGRlZm9yZXN0YXRpb24gPyAoXG4gICAgICAgICAgPEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICApO1xuICAgICAgY2FzZSAncHJvY2Vzc2luZyc6XG4gICAgICAgIHJldHVybiA8Q2xvY2tJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNTAwXCIgLz47XG4gICAgICBjYXNlICdmYWlsZWQnOlxuICAgICAgICByZXR1cm4gPEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1yZWQtNTAwXCIgLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPENsb2NrSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JheS01MDBcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0U3RhdHVzQmFkZ2UgPSAoc3RhdHVzOiBzdHJpbmcsIGRlZm9yZXN0YXRpb246IGJvb2xlYW4pID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnY29tcGxldGVkJzpcbiAgICAgICAgcmV0dXJuIGRlZm9yZXN0YXRpb24gPyAoXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmFkZ2UtZXJyb3JcIj5EZWZvcmVzdGF0aW9uIERldGVjdGVkPC9zcGFuPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJhZGdlLXN1Y2Nlc3NcIj5WZXJpZmllZCBDbGVhbjwvc3Bhbj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ3Byb2Nlc3NpbmcnOlxuICAgICAgICByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwiYmFkZ2Utd2FybmluZ1wiPlByb2Nlc3Npbmc8L3NwYW4+O1xuICAgICAgY2FzZSAnZmFpbGVkJzpcbiAgICAgICAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cImJhZGdlLWVycm9yXCI+RmFpbGVkPC9zcGFuPjtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiA8c3BhbiBjbGFzc05hbWU9XCJiYWRnZS1pbmZvXCI+VW5rbm93bjwvc3Bhbj47XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVTdHJpbmcpLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICBtb250aDogJ3Nob3J0JyxcbiAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgbWludXRlOiAnMi1kaWdpdCcsXG4gICAgfSk7XG4gIH07XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTYgYmctZ3JheS0yMDAgcm91bmRlZCB3LTEvMyBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgIHtbLi4uQXJyYXkoNSldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHAtNCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNSB3LTUgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0zLzQgbWItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctZ3JheS0yMDAgcm91bmRlZCB3LTEvMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IHctMTYgYmctZ3JheS0yMDAgcm91bmRlZFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+UmVjZW50IFZlcmlmaWNhdGlvbnM8L2gyPlxuICAgICAgICA8TGlua1xuICAgICAgICAgIGhyZWY9XCIvdmVyaWZpY2F0aW9uc1wiXG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXByaW1hcnktNjAwIGhvdmVyOnRleHQtcHJpbWFyeS01MDBcIlxuICAgICAgICA+XG4gICAgICAgICAgVmlldyBhbGxcbiAgICAgICAgPC9MaW5rPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgIHt2ZXJpZmljYXRpb25zLm1hcCgodmVyaWZpY2F0aW9uLCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBrZXk9e3ZlcmlmaWNhdGlvbi5pZH1cbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zLCBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIGhvdmVyOmJvcmRlci1ncmF5LTMwMCBob3ZlcjpzaGFkb3ctc20gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICB7Z2V0U3RhdHVzSWNvbih2ZXJpZmljYXRpb24uc3RhdHVzLCB2ZXJpZmljYXRpb24uZGVmb3Jlc3RhdGlvbl9kZXRlY3RlZCl9XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgIHt2ZXJpZmljYXRpb24ucHJvamVjdF9uYW1lfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3ZlcmlmaWNhdGlvbi5hcmVhX2ttMiA+IDAgPyBgJHt2ZXJpZmljYXRpb24uYXJlYV9rbTIudG9GaXhlZCgxKX0ga23CsmAgOiAnQXJlYSBwZW5kaW5nJ31cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIHt2ZXJpZmljYXRpb24uY29uZmlkZW5jZV9zY29yZSA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7KHZlcmlmaWNhdGlvbi5jb25maWRlbmNlX3Njb3JlICogMTAwKS50b0ZpeGVkKDApfSUgY29uZmlkZW5jZVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKHZlcmlmaWNhdGlvbi52ZXJpZmllZF9hdCl9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICB7Z2V0U3RhdHVzQmFkZ2UodmVyaWZpY2F0aW9uLnN0YXR1cywgdmVyaWZpY2F0aW9uLmRlZm9yZXN0YXRpb25fZGV0ZWN0ZWQpfVxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICA8RXllSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHt2ZXJpZmljYXRpb25zLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiXG4gICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsxLjV9XG4gICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICBkPVwiTTkgMTJoMy43NU05IDE1aDMuNzVNOSAxOGgzLjc1bTMgLjc1SDE4YTIuMjUgMi4yNSAwIDAwMi4yNS0yLjI1VjYuMTA4YzAtMS4xMzUtLjg0NS0yLjA5OC0xLjk3Ni0yLjE5MmE0OC40MjQgNDguNDI0IDAgMDAtMS4xMjMtLjA4bS01LjgwMSAwYy0uMDY1LjIxLS4xLjQzMy0uMS42NjQgMCAuNDE0LjMzNi43NS43NS43NWg0LjVhLjc1Ljc1IDAgMDAuNzUtLjc1IDIuMjUgMi4yNSAwIDAwLS4xLS42NjRtLTUuOCAwQTIuMjUxIDIuMjUxIDAgMDExMy41IDIuMjVIMTVjMS4wMTIgMCAxLjg2Ny42NjggMi4xNSAxLjU4Nm0tNS44IDBjLS4zNzYuMDIzLS43NS4wNS0xLjEyNC4wOEM5LjA5NSA0LjAxIDguMjUgNC45NzMgOC4yNSA2LjEwOFY4LjI1bTAgMEg0Ljg3NWMtLjYyMSAwLTEuMTI1LjUwNC0xLjEyNSAxLjEyNXYxMS4yNWMwLjYyMSAwIDEuMTI1LS41MDQgMS4xMjUtMS4xMjVWOS4zNzVjMC0uNjIxLjUwNC0xLjEyNSAxLjEyNS0xLjEyNUg4LjI1ek02Ljc1IDEyaC4wMDh2LjAwOEg2Ljc1VjEyem0wIDNoLjAwOHYuMDA4SDYuNzVWMTV6bTAgM2guMDA4di4wMDhINi43NVYxOHpcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Tm8gdmVyaWZpY2F0aW9ucyB5ZXQ8L3A+XG4gICAgICAgICAgPExpbmsgaHJlZj1cIi92ZXJpZnlcIiBjbGFzc05hbWU9XCJtdC0yIGJ0bi1wcmltYXJ5IGlubGluZS1mbGV4XCI+XG4gICAgICAgICAgICBTdGFydCBGaXJzdCBWZXJpZmljYXRpb25cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkxpbmsiLCJtb3Rpb24iLCJDaGVja0NpcmNsZUljb24iLCJFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiIsIkNsb2NrSWNvbiIsIkV5ZUljb24iLCJhcGlDbGllbnQiLCJ0b2FzdCIsIlJlY2VudFZlcmlmaWNhdGlvbnMiLCJ2ZXJpZmljYXRpb25zIiwic2V0VmVyaWZpY2F0aW9ucyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImZldGNoVmVyaWZpY2F0aW9ucyIsImRhc2hib2FyZERhdGEiLCJnZXREYXNoYm9hcmRTdGF0cyIsImZvcm1hdHRlZFZlcmlmaWNhdGlvbnMiLCJyZWNlbnRfdmVyaWZpY2F0aW9ucyIsIm1hcCIsInZlcmlmaWNhdGlvbiIsImlkIiwidmVyaWZpY2F0aW9uX2lkIiwicHJvamVjdF9uYW1lIiwic3RhdHVzIiwidmVyaWZpY2F0aW9uX2NvbXBsZXRlIiwiZGVmb3Jlc3RhdGlvbl9kZXRlY3RlZCIsImNvbmZpZGVuY2Vfc2NvcmUiLCJhcmVhX2ttMiIsImRlZm9yZXN0ZWRfYXJlYV9rbTIiLCJ2ZXJpZmllZF9hdCIsImVycm9yIiwiY29uc29sZSIsImdldFN0YXR1c0ljb24iLCJkZWZvcmVzdGF0aW9uIiwiY2xhc3NOYW1lIiwiZ2V0U3RhdHVzQmFkZ2UiLCJzcGFuIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwibW9udGgiLCJkYXkiLCJob3VyIiwibWludXRlIiwiZGl2IiwiQXJyYXkiLCJfIiwiaSIsImgyIiwiaHJlZiIsImluZGV4IiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImRlbGF5IiwicCIsInRvRml4ZWQiLCJidXR0b24iLCJsZW5ndGgiLCJzdmciLCJmaWxsIiwidmlld0JveCIsInN0cm9rZVdpZHRoIiwic3Ryb2tlIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/RecentVerifications.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/SystemStatus.tsx":
/*!***************************************************!*\
  !*** ./src/components/Dashboard/SystemStatus.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction SystemStatus() {\n    const [healthStatus, setHealthStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchHealthStatus();\n        // Set up polling for health status\n        const interval = setInterval(fetchHealthStatus, 30000); // Every 30 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    const fetchHealthStatus = async ()=>{\n        try {\n            const status = await _lib_api__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getDetailedHealth();\n            setHealthStatus(status);\n            setLastUpdated(new Date());\n        } catch (error) {\n            // If API is not available, use mock data\n            try {\n                const mockStatus = await _lib_api__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getMockHealthStatus();\n                setHealthStatus(mockStatus);\n                setLastUpdated(new Date());\n            } catch (mockError) {\n                console.error(\"Failed to fetch health status:\", mockError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"healthy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, this);\n            case \"degraded\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, this);\n            case \"unhealthy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"healthy\":\n                return \"text-green-600 bg-green-50\";\n            case \"degraded\":\n                return \"text-yellow-600 bg-yellow-50\";\n            case \"unhealthy\":\n                return \"text-red-600 bg-red-50\";\n            default:\n                return \"text-gray-600 bg-gray-50\";\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/2 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 w-5 bg-gray-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"System Status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            healthStatus && getStatusIcon(healthStatus.status),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(healthStatus?.status || \"unknown\")}`,\n                                children: healthStatus?.status || \"Unknown\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            healthStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: \"Overall Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"Response time: \",\n                                            healthStatus.response_time_ms,\n                                            \"ms\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: healthStatus.environment\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"v\",\n                                            healthStatus.version\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: \"Components\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            healthStatus.components.map((component, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.3,\n                                        delay: index * 0.1\n                                    },\n                                    className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                getStatusIcon(component.status),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 capitalize\",\n                                                            children: component.name.replace(\"_\", \" \")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: component.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        component.response_time_ms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    component.response_time_ms,\n                                                    \"ms\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, component.name, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                \"Last updated: \",\n                                lastUpdated.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this),\n            !healthStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Unable to fetch system status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchHealthStatus,\n                        className: \"mt-2 text-sm text-primary-600 hover:text-primary-500\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/SystemStatus.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/SystemStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/VerificationTrends.tsx":
/*!*********************************************************!*\
  !*** ./src/components/Dashboard/VerificationTrends.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerificationTrends)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction VerificationTrends() {\n    const [trendData, setTrendData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeChart, setActiveChart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"verifications\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTrendData = async ()=>{\n            try {\n                setIsLoading(true);\n                const trendsData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getVerificationTrends(30);\n                // Transform API data to match component format\n                const formattedData = trendsData.map((trend, index)=>{\n                    const date = new Date(trend.date);\n                    const monthNames = [\n                        \"Jan\",\n                        \"Feb\",\n                        \"Mar\",\n                        \"Apr\",\n                        \"May\",\n                        \"Jun\",\n                        \"Jul\",\n                        \"Aug\",\n                        \"Sep\",\n                        \"Oct\",\n                        \"Nov\",\n                        \"Dec\"\n                    ];\n                    return {\n                        month: `${monthNames[date.getMonth()]} ${date.getDate()}`,\n                        verifications: trend.count,\n                        deforestation_rate: trend.deforestation_rate,\n                        average_confidence: trend.average_confidence\n                    };\n                });\n                setTrendData(formattedData);\n            } catch (error) {\n                console.error(\"Failed to fetch trend data:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to load verification trends\");\n                // Fallback to mock data if API fails\n                const fallbackData = [\n                    {\n                        month: \"No Data\",\n                        verifications: 0,\n                        deforestation_rate: 0,\n                        average_confidence: 0\n                    }\n                ];\n                setTrendData(fallbackData);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchTrendData();\n    }, []);\n    const CustomTooltip = ({ active, payload, label })=>{\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium text-gray-900\",\n                        children: `${label} 2023`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    payload.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            style: {\n                                color: entry.color\n                            },\n                            children: `${entry.name}: ${entry.dataKey === \"deforestation_rate\" ? `${(entry.value * 100).toFixed(1)}%` : entry.dataKey === \"average_confidence\" ? `${(entry.value * 100).toFixed(1)}%` : entry.value}`\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Verification Trends\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveChart(\"verifications\"),\n                                className: `px-3 py-1 text-sm rounded-md transition-colors duration-200 ${activeChart === \"verifications\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                children: \"Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveChart(\"deforestation\"),\n                                className: `px-3 py-1 text-sm rounded-md transition-colors duration-200 ${activeChart === \"deforestation\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                children: \"Deforestation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: activeChart === \"verifications\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.BarChart, {\n                        data: trendData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"#f0f0f0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                dataKey: \"verifications\",\n                                fill: \"#22c55e\",\n                                radius: [\n                                    4,\n                                    4,\n                                    0,\n                                    0\n                                ],\n                                name: \"Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.LineChart, {\n                        data: trendData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"#f0f0f0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                },\n                                tickFormatter: (value)=>`${(value * 100).toFixed(0)}%`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"deforestation_rate\",\n                                stroke: \"#ef4444\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#ef4444\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6,\n                                    stroke: \"#ef4444\",\n                                    strokeWidth: 2\n                                },\n                                name: \"Deforestation Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"average_confidence\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#3b82f6\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6,\n                                    stroke: \"#3b82f6\",\n                                    strokeWidth: 2\n                                },\n                                name: \"Avg Confidence\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: trendData.reduce((sum, item)=>sum + item.verifications, 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-red-600\",\n                                children: [\n                                    (trendData.reduce((sum, item)=>sum + item.deforestation_rate, 0) / trendData.length * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg Deforestation Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: [\n                                    (trendData.reduce((sum, item)=>sum + item.average_confidence, 0) / trendData.length * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg Confidence\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/VerificationTrends.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst footerLinks = {\n    product: [\n        {\n            name: \"Features\",\n            href: \"#\"\n        },\n        {\n            name: \"Pricing\",\n            href: \"#\"\n        },\n        {\n            name: \"API Documentation\",\n            href: \"/docs\"\n        },\n        {\n            name: \"System Status\",\n            href: \"/health\"\n        }\n    ],\n    company: [\n        {\n            name: \"About\",\n            href: \"#\"\n        },\n        {\n            name: \"Blog\",\n            href: \"#\"\n        },\n        {\n            name: \"Careers\",\n            href: \"#\"\n        },\n        {\n            name: \"Contact\",\n            href: \"#\"\n        }\n    ],\n    resources: [\n        {\n            name: \"Help Center\",\n            href: \"#\"\n        },\n        {\n            name: \"Privacy Policy\",\n            href: \"#\"\n        },\n        {\n            name: \"Terms of Service\",\n            href: \"#\"\n        },\n        {\n            name: \"Security\",\n            href: \"#\"\n        }\n    ],\n    social: [\n        {\n            name: \"GitHub\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"Twitter\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-8 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.product.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Company\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.company.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"mt-6 space-y-4\",\n                                    children: footerLinks.resources.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: \"text-sm leading-6 text-gray-600 hover:text-gray-900 transition-colors duration-200\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold leading-6 text-gray-900\",\n                                    children: \"Connect\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex space-x-4\",\n                                    children: footerLinks.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: \"text-gray-400 hover:text-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"h-6 w-6\",\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 py-6 md:flex md:items-center md:justify-between lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-6 w-6 items-center justify-center rounded bg-primary-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-4 w-4 text-white\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: 1.5,\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            d: \"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"CarbonLedger\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 md:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"\\xa9 2024 CarbonLedger. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Made with\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-3 w-3 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"for the planet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Footer.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChartBarIcon,Cog6ToothIcon,DocumentMagnifyingGlassIcon,HeartIcon,HomeIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Verify Project\",\n        href: \"/verify\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"System Health\",\n        href: \"/health\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-white\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            strokeWidth: 1.5,\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                d: \"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"CarbonLedger\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 -mt-1\",\n                                                children: \"Satellite Verification\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${isActive ? \"text-primary-600 bg-primary-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"System Online\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\",\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"block h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChartBarIcon_Cog6ToothIcon_DocumentMagnifyingGlassIcon_HeartIcon_HomeIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"block h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"md:hidden bg-white border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 pt-2 pb-3 space-y-1\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `flex items-center px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${isActive ? \"text-primary-600 bg-primary-50\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 21\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 px-4 py-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 bg-green-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"System Online\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/Header.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/Layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MainLayout({ children, showFooter = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n                lineNumber: 19,\n                columnNumber: 22\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Layout/MainLayout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzhCO0FBQ0E7QUFPZixTQUFTRSxXQUFXLEVBQUVDLFFBQVEsRUFBRUMsYUFBYSxJQUFJLEVBQW1CO0lBQ2pGLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ04sK0NBQU1BOzs7OzswQkFDUCw4REFBQ087Z0JBQUtELFdBQVU7MEJBQ2JIOzs7Ozs7WUFFRkMsNEJBQWMsOERBQUNILCtDQUFNQTs7Ozs7Ozs7Ozs7QUFHNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYXJib25sZWRnZXItZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dC50c3g/ZTNjNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBIZWFkZXIgZnJvbSAnLi9IZWFkZXInO1xuaW1wb3J0IEZvb3RlciBmcm9tICcuL0Zvb3Rlcic7XG5cbmludGVyZmFjZSBNYWluTGF5b3V0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xuICBzaG93Rm9vdGVyPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWFpbkxheW91dCh7IGNoaWxkcmVuLCBzaG93Rm9vdGVyID0gdHJ1ZSB9OiBNYWluTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICA8SGVhZGVyIC8+XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JheS01MFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L21haW4+XG4gICAgICB7c2hvd0Zvb3RlciAmJiA8Rm9vdGVyIC8+fVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWRlciIsIkZvb3RlciIsIk1haW5MYXlvdXQiLCJjaGlsZHJlbiIsInNob3dGb290ZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkApiStatus: () => (/* binding */ checkApiStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getDetailedHealth: () => (/* binding */ getDetailedHealth),\n/* harmony export */   getHealth: () => (/* binding */ getHealth),\n/* harmony export */   getMockHealthStatus: () => (/* binding */ getMockHealthStatus),\n/* harmony export */   getMockVerification: () => (/* binding */ getMockVerification),\n/* harmony export */   getPerformanceMetrics: () => (/* binding */ getPerformanceMetrics),\n/* harmony export */   getRegionalAnalysis: () => (/* binding */ getRegionalAnalysis),\n/* harmony export */   getVerificationTrends: () => (/* binding */ getVerificationTrends),\n/* harmony export */   submitVerification: () => (/* binding */ submitVerification)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n// API Client for CarbonLedger Frontend\n\n\nclass ApiClient {\n    constructor(){\n        this.baseURL = \"http://127.0.0.1:8001\" || 0;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: this.baseURL,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n    setupInterceptors() {\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            // Add request timestamp for debugging\n            config.metadata = {\n                startTime: new Date()\n            };\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>{\n            // Calculate request duration\n            const endTime = new Date();\n            const duration = endTime.getTime() - response.config.metadata?.startTime?.getTime();\n            if (true) {\n                console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);\n            }\n            return response;\n        }, (error)=>{\n            this.handleApiError(error);\n            return Promise.reject(error);\n        });\n    }\n    handleApiError(error) {\n        if (error.response) {\n            // Server responded with error status\n            const status = error.response.status;\n            const data = error.response.data;\n            let message = \"An error occurred\";\n            if (data?.user_message) {\n                message = data.user_message;\n            } else if (data?.detail) {\n                message = data.detail;\n            } else if (status === 429) {\n                message = \"Too many requests. Please try again later.\";\n            } else if (status === 500) {\n                message = \"Server error. Please try again later.\";\n            } else if (status === 503) {\n                message = \"Service temporarily unavailable.\";\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(message);\n        } else if (error.request) {\n            // Network error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n        } else {\n            // Other error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"An unexpected error occurred.\");\n        }\n    }\n    // Health endpoints\n    async getHealth() {\n        const response = await this.client.get(\"/health\");\n        return response.data;\n    }\n    async getDetailedHealth() {\n        const response = await this.client.get(\"/health/detailed\");\n        return response.data;\n    }\n    // Verification endpoints\n    async submitVerification(request) {\n        const response = await this.client.post(\"/api/v1/verify\", request);\n        return response.data;\n    }\n    // Blockchain endpoints (when enabled)\n    async mintTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/mint\", request);\n        return response.data;\n    }\n    async retireTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/retire\", request);\n        return response.data;\n    }\n    async getTokenBalance(address, tokenId) {\n        const response = await this.client.get(`/api/v1/blockchain/balance/${address}/${tokenId}`);\n        return response.data;\n    }\n    async getTokenMetadata(tokenId) {\n        const response = await this.client.get(`/api/v1/blockchain/metadata/${tokenId}`);\n        return response.data;\n    }\n    async getGasEstimate(operation) {\n        const response = await this.client.get(`/api/v1/blockchain/gas-estimate/${operation}`);\n        return response.data;\n    }\n    async getBlockchainHealth() {\n        const response = await this.client.get(\"/api/v1/blockchain/health\");\n        return response.data;\n    }\n    // Utility methods\n    async checkApiStatus() {\n        try {\n            await this.getHealth();\n            return true;\n        } catch  {\n            return false;\n        }\n    }\n    getBaseURL() {\n        return this.baseURL;\n    }\n    // Analytics endpoints\n    async getDashboardStats() {\n        const response = await this.client.get(\"/api/v1/analytics/dashboard-stats\");\n        return response.data;\n    }\n    async getVerificationTrends(days = 30) {\n        const response = await this.client.get(`/api/v1/analytics/trends?days=${days}`);\n        return response.data;\n    }\n    async getRegionalAnalysis() {\n        const response = await this.client.get(\"/api/v1/analytics/regional-analysis\");\n        return response.data;\n    }\n    async getPerformanceMetrics() {\n        const response = await this.client.get(\"/api/v1/analytics/performance-metrics\");\n        return response.data;\n    }\n    // Mock data methods for development (when backend is not available)\n    async getMockVerification(request) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const mockResponse = {\n            verification_id: `mock-${Date.now()}`,\n            project_id: Math.floor(Math.random() * 1000),\n            project_name: request.project_name,\n            verification_complete: true,\n            deforestation_detected: Math.random() > 0.7,\n            deforested_area_km2: Math.random() * 10,\n            confidence_score: 0.85 + Math.random() * 0.15,\n            data_quality_score: 0.9 + Math.random() * 0.1,\n            verification_level: \"enhanced\",\n            sources_used: 3,\n            vegetation_indices: {\n                ndvi_mean: 0.7 + Math.random() * 0.2,\n                ndvi_std: 0.1 + Math.random() * 0.05,\n                evi_mean: 0.6 + Math.random() * 0.2\n            },\n            temporal_analysis: {\n                trend: Math.random() > 0.5 ? \"stable\" : \"declining\",\n                change_rate: -0.02 + Math.random() * 0.04\n            },\n            area_checked: {\n                total_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111,\n                analyzed_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111 * 0.95\n            },\n            analysis_period: {\n                start_date: \"2023-01-01\",\n                end_date: \"2023-12-31\"\n            },\n            verified_at: new Date().toISOString(),\n            processing_time_seconds: 45.2,\n            warnings: Math.random() > 0.8 ? [\n                \"Cloud cover detected in some areas\"\n            ] : undefined,\n            metadata: {\n                satellite_sources: [\n                    \"Landsat-8\",\n                    \"Sentinel-2\"\n                ],\n                processing_version: \"2.1.0\"\n            }\n        };\n        return mockResponse;\n    }\n    async getMockHealthStatus() {\n        const mockHealth = {\n            status: \"healthy\",\n            timestamp: new Date().toISOString(),\n            response_time_ms: 150,\n            version: \"1.0.0\",\n            environment: \"development\",\n            components: [\n                {\n                    name: \"database\",\n                    status: \"healthy\",\n                    message: \"Database connection healthy\",\n                    response_time_ms: 50\n                },\n                {\n                    name: \"earth_engine\",\n                    status: \"degraded\",\n                    message: \"Earth Engine not configured (development mode)\",\n                    response_time_ms: 0\n                },\n                {\n                    name: \"blockchain\",\n                    status: \"healthy\",\n                    message: \"Blockchain configuration loaded\",\n                    response_time_ms: 25\n                }\n            ]\n        };\n        return mockHealth;\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n// Export individual methods for convenience\nconst { getHealth, getDetailedHealth, submitVerification, checkApiStatus, getDashboardStats, getVerificationTrends, getRegionalAnalysis, getPerformanceMetrics, getMockVerification, getMockHealthStatus } = apiClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8c2030a095e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FyYm9ubGVkZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9lYjUyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOGMyMDMwYTA5NWU5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n    description: \"Advanced satellite verification platform for carbon credit projects using AI-powered deforestation detection\",\n    keywords: \"carbon credits, satellite verification, deforestation, blockchain, environmental monitoring\",\n    authors: [\n        {\n            name: \"CarbonLedger Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#22c55e\",\n    openGraph: {\n        title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n        description: \"Advanced satellite verification platform for carbon credit projects\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"CarbonLedger - Satellite-Verified Carbon Credits\",\n        description: \"Advanced satellite verification platform for carbon credit projects\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: \"#363636\",\n                            color: \"#fff\"\n                        },\n                        success: {\n                            duration: 3000,\n                            iconTheme: {\n                                primary: \"#22c55e\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            iconTheme: {\n                                primary: \"#ef4444\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/cl/frontend/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/@heroicons","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/react-hot-toast","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/goober","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/recharts","vendor-chunks/lodash","vendor-chunks/d3-shape","vendor-chunks/react-smooth","vendor-chunks/decimal.js-light","vendor-chunks/d3-scale","vendor-chunks/react-transition-group","vendor-chunks/prop-types","vendor-chunks/fast-equals","vendor-chunks/d3-time-format","vendor-chunks/recharts-scale","vendor-chunks/d3-time","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/d3-color","vendor-chunks/eventemitter3","vendor-chunks/react-is","vendor-chunks/d3-interpolate","vendor-chunks/d3-path","vendor-chunks/object-assign","vendor-chunks/internmap","vendor-chunks/@babel","vendor-chunks/clsx","vendor-chunks/victory-vendor","vendor-chunks/tiny-invariant"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fs.chy%2Fcl%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();