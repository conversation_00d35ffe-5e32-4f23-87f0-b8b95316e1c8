{"c": ["app/layout", "webpack"], "r": ["app/analytics/page"], "m": ["(app-pages-browser)/./node_modules/lodash/maxBy.js", "(app-pages-browser)/./node_modules/lodash/minBy.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fs.chy%2Fcl%2Ffrontend%2Fsrc%2Fapp%2Fanalytics%2Fpage.tsx&server=false!", "(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js", "(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js", "(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js", "(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js", "(app-pages-browser)/./node_modules/recharts/es6/polar/PolarAngleAxis.js", "(app-pages-browser)/./node_modules/recharts/es6/polar/PolarRadiusAxis.js", "(app-pages-browser)/./node_modules/recharts/es6/shape/Polygon.js", "(app-pages-browser)/./src/app/analytics/page.tsx", "(app-pages-browser)/__barrel_optimize__?names=ChartBarIcon,GlobeAltIcon,TrendingDownIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}