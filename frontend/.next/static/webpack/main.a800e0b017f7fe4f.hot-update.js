"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return Router;\n    },\n    matchesMiddleware: function() {\n        return matchesMiddleware;\n    },\n    createKey: function() {\n        return createKey;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"./node_modules/next/dist/shared/lib/router/utils/resolve-rewrites.js\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nfunction buildCancellationError() {\n    return Object.assign(new Error(\"Route Cancelled\"), {\n        cancelled: true\n    });\n}\nasync function matchesMiddleware(options) {\n    const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n    if (!matchers) return false;\n    const { pathname: asPathname } = (0, _parsepath.parsePath)(options.asPath);\n    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n    const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n    const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n    // Check only path match on client. Matching \"has\" should be done on server\n    // where we can access more info such as headers, HttpOnly cookie, etc.\n    return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n    const origin = (0, _utils.getLocationOrigin)();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === \"/404\" || cleanPathname === \"/_error\") {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get(\"x-nextjs-rewrite\");\n    let rewriteTarget = rewriteHeader || response.headers.get(\"x-nextjs-matched-path\");\n    const matchedPath = response.headers.get(\"x-matched-path\");\n    if (matchedPath && !rewriteTarget && !matchedPath.includes(\"__next_data_catchall\") && !matchedPath.includes(\"/_error\") && !matchedPath.includes(\"/404\")) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith(\"/\") || undefined) {\n            const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites }] = param;\n                let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  true ? undefined : 0,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (true) {\n                    const result = (0, _resolverewrites.default)(as, pages, rewrites, parsedRewriteTarget.query, (path)=>resolveDynamicRoute(path, pages), options.router.locales);\n                    if (result.matchedPage) {\n                        parsedRewriteTarget.pathname = result.parsedAs.pathname;\n                        as = parsedRewriteTarget.pathname;\n                        Object.assign(parsedRewriteTarget.query, result.parsedAs.query);\n                    }\n                } else {}\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: \"rewrite\",\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsepath.parsePath)(source);\n        const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n            ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                nextConfig,\n                parseData: true\n            }),\n            defaultLocale: options.router.defaultLocale,\n            buildId: \"\"\n        });\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    const redirectTarget = response.headers.get(\"x-nextjs-redirect\");\n    if (redirectTarget) {\n        if (redirectTarget.startsWith(\"/\")) {\n            const src = (0, _parsepath.parsePath)(redirectTarget);\n            const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n                ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                    nextConfig,\n                    parseData: true\n                }),\n                defaultLocale: options.router.defaultLocale,\n                buildId: \"\"\n            });\n            return Promise.resolve({\n                type: \"redirect-internal\",\n                newAs: \"\" + pathname + src.query + src.hash,\n                newUrl: \"\" + pathname + src.query + src.hash\n            });\n        }\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: \"next\"\n    });\n}\nasync function withMiddlewareEffects(options) {\n    const matches = await matchesMiddleware(options);\n    if (!matches || !options.fetchData) {\n        return null;\n    }\n    try {\n        const data = await options.fetchData();\n        const effect = await getMiddlewareData(data.dataHref, data.response, options);\n        return {\n            dataHref: data.dataHref,\n            json: data.json,\n            response: data.response,\n            text: data.text,\n            cacheKey: data.cacheKey,\n            effect\n        };\n    } catch (e) {\n        /**\n     * TODO: Revisit this in the future.\n     * For now we will not consider middleware data errors to be fatal.\n     * maybe we should revisit in the future.\n     */ return null;\n    }\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol(\"SSG_DATA_NOT_FOUND\");\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: \"same-origin\",\n        method: options.method || \"GET\",\n        headers: Object.assign({}, options.headers, {\n            \"x-nextjs-data\": \"1\"\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref, inflightCache, isPrefetch, hasMiddleware, isServerRender, parseJSON, persistCache, isBackground, unstable_skipClientCache } = param;\n    const { href: cacheKey } = new URL(dataHref, window.location.href);\n    var _params_method;\n    const getData = (params)=>fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: \"prefetch\"\n            } : {}, isPrefetch && hasMiddleware ? {\n                \"x-middleware-prefetch\": \"1\"\n            } : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : \"GET\"\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === \"HEAD\") {\n                return {\n                    dataHref,\n                    response,\n                    text: \"\",\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(\"Failed to load static props\");\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== \"production\" || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === \"Failed to fetch\" || // firefox\n            err.message === \"NetworkError when attempting to fetch resource.\" || // safari\n            err.message === \"Load failed\") {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            inflightCache[cacheKey] = Promise.resolve(data);\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: \"HEAD\"\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url, router } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href);\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route, router } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error('Abort fetching component for route: \"' + route + '\"');\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options) {\n        if (options === void 0) options = {};\n        if (false) {}\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"pushState\", url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options) {\n        if (options === void 0) options = {};\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"replaceState\", url, as, options);\n    }\n    async _bfl(as, resolvedAs, locale, skipNavigate) {\n        if (true) {\n            let matchesBflStatic = false;\n            let matchesBflDynamic = false;\n            for (const curAs of [\n                as,\n                resolvedAs\n            ]){\n                if (curAs) {\n                    const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, \"http://n\").pathname);\n                    const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n                    if (asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, \"http://n\").pathname)) {\n                        var _this__bfl_s, _this__bfl_s1;\n                        matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                        for (const normalizedAS of [\n                            asNoSlash,\n                            asNoSlashLocale\n                        ]){\n                            // if any sub-path of as matches a dynamic filter path\n                            // it should be hard navigated\n                            const curAsParts = normalizedAS.split(\"/\");\n                            for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                var _this__bfl_d;\n                                const currentPart = curAsParts.slice(0, i).join(\"/\");\n                                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                    matchesBflDynamic = true;\n                                    break;\n                                }\n                            }\n                        }\n                        // if the client router filter is matched then we trigger\n                        // a hard navigation\n                        if (matchesBflStatic || matchesBflDynamic) {\n                            if (skipNavigate) {\n                                return true;\n                            }\n                            handleHardNavigation({\n                                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                                router: this\n                            });\n                            return new Promise(()=>{});\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n    async change(method, url, as, options, forcedScroll) {\n        var _this_components_pathname;\n        if (!(0, _islocalurl.isLocalURL)(url)) {\n            handleHardNavigation({\n                url,\n                router: this\n            });\n            return false;\n        }\n        // WARNING: `_h` is an internal option for handing Next.js client-side\n        // hydration. Your app should _never_ use this property. It may change at\n        // any time without notice.\n        const isQueryUpdating = options._h === 1;\n        if (!isQueryUpdating && !options.shallow) {\n            await this._bfl(as, undefined, options.locale);\n        }\n        let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n        const nextState = {\n            ...this.state\n        };\n        // for static pages with query params in the URL we delay\n        // marking the router ready until after the query is updated\n        // or a navigation has occurred\n        const readyStateChange = this.isReady !== true;\n        this.isReady = true;\n        const isSsr = this.isSsr;\n        if (!isQueryUpdating) {\n            this.isSsr = false;\n        }\n        // if a route transition is already in progress before\n        // the query updating is triggered ignore query updating\n        if (isQueryUpdating && this.clc) {\n            return false;\n        }\n        const prevLocale = nextState.locale;\n        if (false) { var _this_locales; }\n        // marking route changes as a navigation start entry\n        if (_utils.ST) {\n            performance.mark(\"routeChange\");\n        }\n        const { shallow = false, scroll = true } = options;\n        const routeProps = {\n            shallow\n        };\n        if (this._inFlightRoute && this.clc) {\n            if (!isSsr) {\n                Router.events.emit(\"routeChangeError\", buildCancellationError(), this._inFlightRoute, routeProps);\n            }\n            this.clc();\n            this.clc = null;\n        }\n        as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n        const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n        this._inFlightRoute = as;\n        const localeChange = prevLocale !== nextState.locale;\n        // If the url change is only related to a hash change\n        // We should not proceed. We should only change the state.\n        if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n            nextState.asPath = cleanedAs;\n            Router.events.emit(\"hashChangeStart\", as, routeProps);\n            // TODO: do we need the resolved href when only a hash change?\n            this.changeState(method, url, as, {\n                ...options,\n                scroll: false\n            });\n            if (scroll) {\n                this.scrollToHash(cleanedAs);\n            }\n            try {\n                await this.set(nextState, this.components[nextState.route], null);\n            } catch (err) {\n                if ((0, _iserror.default)(err) && err.cancelled) {\n                    Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                }\n                throw err;\n            }\n            Router.events.emit(\"hashChangeComplete\", as, routeProps);\n            return true;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        let { pathname, query } = parsed;\n        // if we detected the path as app route during prefetching\n        // trigger hard navigation\n        if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return new Promise(()=>{});\n        }\n        // The build manifest needs to be loaded before auto-static dynamic pages\n        // get their query parameters to allow ensuring they can be parsed properly\n        // when rewritten to\n        let pages, rewrites;\n        try {\n            [pages, { __rewrites: rewrites }] = await Promise.all([\n                this.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)(),\n                this.pageLoader.getMiddleware()\n            ]);\n        } catch (err) {\n            // If we fail to resolve the page list or client-build manifest, we must\n            // do a server-side transition:\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        // If asked to change the current URL we should reload the current page\n        // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n        // We also need to set the method = replaceState always\n        // as this should not go into the history (That's how browsers work)\n        // We should compare the new asPath to the current asPath, not the url\n        if (!this.urlIsNew(cleanedAs) && !localeChange) {\n            method = \"replaceState\";\n        }\n        // we need to resolve the as value using rewrites for dynamic SSG\n        // pages to allow building the data URL correctly\n        let resolvedAs = as;\n        // url and as should always be prefixed with basePath by this\n        // point by either next/link or router.push/replace so strip the\n        // basePath from the pathname to match the pages dir 1-to-1\n        pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n        let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        const parsedAsPathname = as.startsWith(\"/\") && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n        const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n        // we don't attempt resolve asPath when we need to execute\n        // middleware as the resolving will occur server-side\n        const isMiddlewareMatch = !options.shallow && await matchesMiddleware({\n            asPath: as,\n            locale: nextState.locale,\n            router: this\n        });\n        if (isQueryUpdating && isMiddlewareMatch) {\n            shouldResolveHref = false;\n        }\n        if (shouldResolveHref && pathname !== \"/_error\") {\n            options._shouldResolveHref = true;\n            if ( true && as.startsWith(\"/\")) {\n                const rewritesResult = (0, _resolverewrites.default)((0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, nextState.locale), true), pages, rewrites, query, (p)=>resolveDynamicRoute(p, pages), this.locales);\n                if (rewritesResult.externalDest) {\n                    handleHardNavigation({\n                        url: as,\n                        router: this\n                    });\n                    return true;\n                }\n                if (!isMiddlewareMatch) {\n                    resolvedAs = rewritesResult.asPath;\n                }\n                if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n                    // if this directly matches a page we need to update the href to\n                    // allow the correct page chunk to be loaded\n                    pathname = rewritesResult.resolvedHref;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            } else {\n                parsed.pathname = resolveDynamicRoute(pathname, pages);\n                if (parsed.pathname !== pathname) {\n                    pathname = parsed.pathname;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            }\n        }\n        if (!(0, _islocalurl.isLocalURL)(as)) {\n            if (true) {\n                throw new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\");\n            }\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n        route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        let routeMatch = false;\n        if ((0, _isdynamic.isDynamicRoute)(route)) {\n            const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n            const asPathname = parsedAs.pathname;\n            const routeRegex = (0, _routeregex.getRouteRegex)(route);\n            routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n            const shouldInterpolate = route === asPathname;\n            const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n            if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                if (missingParams.length > 0 && !isMiddlewareMatch) {\n                    if (true) {\n                        console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(\", \") + \" in the `href`'s `query`\"));\n                    }\n                    throw new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(\", \") + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? \"href-interpolation-failed\" : \"incompatible-href-as\")));\n                }\n            } else if (shouldInterpolate) {\n                as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n                    pathname: interpolatedAs.result,\n                    query: (0, _omit.omit)(query, interpolatedAs.params)\n                }));\n            } else {\n                // Merge params into `query`, overwriting any specified in search\n                Object.assign(query, routeMatch);\n            }\n        }\n        if (!isQueryUpdating) {\n            Router.events.emit(\"routeChangeStart\", as, routeProps);\n        }\n        const isErrorRoute = this.pathname === \"/404\" || this.pathname === \"/_error\";\n        try {\n            var _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props;\n            let routeInfo = await this.getRouteInfo({\n                route,\n                pathname,\n                query,\n                as,\n                resolvedAs,\n                routeProps,\n                locale: nextState.locale,\n                isPreview: nextState.isPreview,\n                hasMiddleware: isMiddlewareMatch,\n                unstable_skipClientCache: options.unstable_skipClientCache,\n                isQueryUpdating: isQueryUpdating && !this.isFallback,\n                isMiddlewareRewrite\n            });\n            if (!isQueryUpdating && !options.shallow) {\n                await this._bfl(as, \"resolvedAs\" in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n            }\n            if (\"route\" in routeInfo && isMiddlewareMatch) {\n                pathname = routeInfo.route || route;\n                route = pathname;\n                if (!routeProps.shallow) {\n                    query = Object.assign({}, routeInfo.query || {}, query);\n                }\n                const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                if (routeMatch && pathname !== cleanedParsedPathname) {\n                    Object.keys(routeMatch).forEach((key)=>{\n                        if (routeMatch && query[key] === routeMatch[key]) {\n                            delete query[key];\n                        }\n                    });\n                }\n                if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                    const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                    let rewriteAs = prefixedAs;\n                    if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                        rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                    }\n                    if (false) {}\n                    const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n                    const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                    if (curRouteMatch) {\n                        Object.assign(query, curRouteMatch);\n                    }\n                }\n            }\n            // If the routeInfo brings a redirect we simply apply it.\n            if (\"type\" in routeInfo) {\n                if (routeInfo.type === \"redirect-internal\") {\n                    return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                } else {\n                    handleHardNavigation({\n                        url: routeInfo.destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n            }\n            const component = routeInfo.Component;\n            if (component && component.unstable_scriptLoader) {\n                const scripts = [].concat(component.unstable_scriptLoader());\n                scripts.forEach((script)=>{\n                    (0, _script.handleClientScriptLoad)(script.props);\n                });\n            }\n            // handle redirect on client-transition\n            if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                    // Use the destination from redirect without adding locale\n                    options.locale = false;\n                    const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                    // check if destination is internal (resolves to a page) and attempt\n                    // client-navigation if it is falling back to hard navigation if\n                    // it's not\n                    if (destination.startsWith(\"/\") && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                        const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                        const { url: newUrl, as: newAs } = prepareUrlAs(this, destination, destination);\n                        return this.change(method, newUrl, newAs, options);\n                    }\n                    handleHardNavigation({\n                        url: destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                // handle SSG data 404\n                if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                    let notFoundRoute;\n                    try {\n                        await this.fetchComponent(\"/404\");\n                        notFoundRoute = \"/404\";\n                    } catch (_) {\n                        notFoundRoute = \"/_error\";\n                    }\n                    routeInfo = await this.getRouteInfo({\n                        route: notFoundRoute,\n                        pathname: notFoundRoute,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isNotFound: true\n                    });\n                    if (\"type\" in routeInfo) {\n                        throw new Error(\"Unexpected middleware effect on /404\");\n                    }\n                }\n            }\n            if (isQueryUpdating && this.pathname === \"/_error\" && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                // ensure statusCode is still correct for static 500 page\n                // when updating query information\n                routeInfo.props.pageProps.statusCode = 500;\n            }\n            var _routeInfo_route;\n            // shallow routing is only allowed for same page URL changes.\n            const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n            var _options_scroll;\n            const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n            const resetScroll = shouldScroll ? {\n                x: 0,\n                y: 0\n            } : null;\n            const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n            // the new state that the router gonna set\n            const upcomingRouterState = {\n                ...nextState,\n                route,\n                pathname,\n                query,\n                asPath: cleanedAs,\n                isFallback: false\n            };\n            // When the page being rendered is the 404 page, we should only update the\n            // query parameters. Route changes here might add the basePath when it\n            // wasn't originally present. This is also why this block is before the\n            // below `changeState` call which updates the browser's history (changing\n            // the URL).\n            if (isQueryUpdating && isErrorRoute) {\n                var _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1;\n                routeInfo = await this.getRouteInfo({\n                    route: this.pathname,\n                    pathname: this.pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps: {\n                        shallow: false\n                    },\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    isQueryUpdating: isQueryUpdating && !this.isFallback\n                });\n                if (\"type\" in routeInfo) {\n                    throw new Error(\"Unexpected middleware effect on \" + this.pathname);\n                }\n                if (this.pathname === \"/_error\" && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (err) {\n                    if ((0, _iserror.default)(err) && err.cancelled) {\n                        Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                return true;\n            }\n            Router.events.emit(\"beforeHistoryChange\", as, routeProps);\n            this.changeState(method, url, as, options);\n            // for query updates we can skip it if the state is unchanged and we don't\n            // need to scroll\n            // https://github.com/vercel/next.js/issues/37139\n            const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n            if (!canSkipUpdating) {\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (e) {\n                    if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                    else throw e;\n                }\n                if (routeInfo.error) {\n                    if (!isQueryUpdating) {\n                        Router.events.emit(\"routeChangeError\", routeInfo.error, cleanedAs, routeProps);\n                    }\n                    throw routeInfo.error;\n                }\n                if (false) {}\n                if (!isQueryUpdating) {\n                    Router.events.emit(\"routeChangeComplete\", as, routeProps);\n                }\n                // A hash mark # is the optional last part of a URL\n                const hashRegex = /#.+$/;\n                if (shouldScroll && hashRegex.test(as)) {\n                    this.scrollToHash(as);\n                }\n            }\n            return true;\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.cancelled) {\n                return false;\n            }\n            throw err;\n        }\n    }\n    changeState(method, url, as, options) {\n        if (options === void 0) options = {};\n        if (true) {\n            if (typeof window.history === \"undefined\") {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === \"undefined\") {\n                console.error(\"Warning: window.history.\" + method + \" is not available\");\n                return;\n            }\n        }\n        if (method !== \"pushState\" || (0, _utils.getURL)() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== \"pushState\" ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/docs/Web/API/History/replaceState\n            \"\", as);\n        }\n    }\n    async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        console.error(err);\n        if (err.cancelled) {\n            // bubble up cancellation errors\n            throw err;\n        }\n        if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n            Router.events.emit(\"routeChangeError\", err, as, routeProps);\n            // If we can't load the page it could be one of following reasons\n            //  1. Page doesn't exists\n            //  2. Page does exist in a different zone\n            //  3. Internal error while loading the page\n            // So, doing a hard reload is the proper way to deal with this.\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            // Changing the URL doesn't block executing the current code path.\n            // So let's throw a cancellation error stop the routing logic.\n            throw buildCancellationError();\n        }\n        try {\n            let props;\n            const { page: Component, styleSheets } = await this.fetchComponent(\"/_error\");\n            const routeInfo = {\n                props,\n                Component,\n                styleSheets,\n                err,\n                error: err\n            };\n            if (!routeInfo.props) {\n                try {\n                    routeInfo.props = await this.getInitialProps(Component, {\n                        err,\n                        pathname,\n                        query\n                    });\n                } catch (gipErr) {\n                    console.error(\"Error in error page `getInitialProps`: \", gipErr);\n                    routeInfo.props = {};\n                }\n            }\n            return routeInfo;\n        } catch (routeInfoErr) {\n            return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + \"\"), pathname, query, as, routeProps, true);\n        }\n    }\n    async getRouteInfo(param) {\n        let { route: requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound } = param;\n        /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n        try {\n            var _data_effect, _data_effect1, _data_effect2, _data_response;\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: this\n            });\n            let existingInfo = this.components[route];\n            if (routeProps.shallow && existingInfo && this.route === route) {\n                return existingInfo;\n            }\n            if (hasMiddleware) {\n                existingInfo = undefined;\n            }\n            let cachedRouteInfo = existingInfo && !(\"initial\" in existingInfo) && \"development\" !== \"development\" ? 0 : undefined;\n            const isBackground = isQueryUpdating;\n            const fetchNextDataParams = {\n                dataHref: this.pageLoader.getDataHref({\n                    href: (0, _formaturl.formatWithValidation)({\n                        pathname,\n                        query\n                    }),\n                    skipInterpolation: true,\n                    asPath: isNotFound ? \"/404\" : resolvedAs,\n                    locale\n                }),\n                hasMiddleware: true,\n                isServerRender: this.isSsr,\n                parseJSON: true,\n                inflightCache: isBackground ? this.sbc : this.sdc,\n                persistCache: !isPreview,\n                isPrefetch: false,\n                unstable_skipClientCache,\n                isBackground\n            };\n            let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n                fetchData: ()=>fetchNextData(fetchNextDataParams),\n                asPath: isNotFound ? \"/404\" : resolvedAs,\n                locale: locale,\n                router: this\n            }).catch((err)=>{\n                // we don't hard error during query updating\n                // as it's un-necessary and doesn't need to be fatal\n                // unless it is a fallback route and the props can't\n                // be loaded\n                if (isQueryUpdating) {\n                    return null;\n                }\n                throw err;\n            });\n            // when rendering error routes we don't apply middleware\n            // effects\n            if (data && (pathname === \"/_error\" || pathname === \"/404\")) {\n                data.effect = undefined;\n            }\n            if (isQueryUpdating) {\n                if (!data) {\n                    data = {\n                        json: self.__NEXT_DATA__.props\n                    };\n                } else {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n            }\n            handleCancelled();\n            if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === \"redirect-internal\" || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === \"redirect-external\") {\n                return data.effect;\n            }\n            if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === \"rewrite\") {\n                const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                const pages = await this.pageLoader.getPageList();\n                // during query updating the page must match although during\n                // client-transition a redirect that doesn't match a page\n                // can be returned and this should trigger a hard navigation\n                // which is valid for incremental migration\n                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                    route = resolvedRoute;\n                    pathname = data.effect.resolvedHref;\n                    query = {\n                        ...query,\n                        ...data.effect.parsedAs.query\n                    };\n                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = this.components[route];\n                    if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return {\n                            ...existingInfo,\n                            route\n                        };\n                    }\n                }\n            }\n            if ((0, _isapiroute.isAPIRoute)(route)) {\n                handleHardNavigation({\n                    url: as,\n                    router: this\n                });\n                return new Promise(()=>{});\n            }\n            const routeInfo = cachedRouteInfo || await this.fetchComponent(route).then((res)=>({\n                    Component: res.page,\n                    styleSheets: res.styleSheets,\n                    __N_SSG: res.mod.__N_SSG,\n                    __N_SSP: res.mod.__N_SSP\n                }));\n            if (true) {\n                const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"./node_modules/next/dist/compiled/react-is/index.js\");\n                if (!isValidElementType(routeInfo.Component)) {\n                    throw new Error('The default export is not a React Component in page: \"' + pathname + '\"');\n                }\n            }\n            const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get(\"x-middleware-skip\");\n            const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n            // For non-SSG prefetches that bailed before sending data\n            // we clear the cache to fetch full response\n            if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                delete this.sdc[data.dataHref];\n            }\n            const { props, cacheKey } = await this._getData(async ()=>{\n                if (shouldFetchData) {\n                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                        return {\n                            cacheKey: data.cacheKey,\n                            props: data.json\n                        };\n                    }\n                    const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname,\n                            query\n                        }),\n                        asPath: resolvedAs,\n                        locale\n                    });\n                    const fetched = await fetchNextData({\n                        dataHref,\n                        isServerRender: this.isSsr,\n                        parseJSON: true,\n                        inflightCache: wasBailedPrefetch ? {} : this.sdc,\n                        persistCache: !isPreview,\n                        isPrefetch: false,\n                        unstable_skipClientCache\n                    });\n                    return {\n                        cacheKey: fetched.cacheKey,\n                        props: fetched.json || {}\n                    };\n                }\n                return {\n                    headers: {},\n                    props: await this.getInitialProps(routeInfo.Component, {\n                        pathname,\n                        query,\n                        asPath: as,\n                        locale,\n                        locales: this.locales,\n                        defaultLocale: this.defaultLocale\n                    })\n                };\n            });\n            // Only bust the data cache for SSP routes although\n            // middleware can skip cache per request with\n            // x-middleware-cache: no-cache as well\n            if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                delete this.sdc[cacheKey];\n            }\n            // we kick off a HEAD request in the background\n            // when a non-prefetch request is made to signal revalidation\n            if (!this.isPreview && routeInfo.__N_SSG && \"development\" !== \"development\" && 0) {}\n            props.pageProps = Object.assign({}, props.pageProps);\n            routeInfo.props = props;\n            routeInfo.route = route;\n            routeInfo.query = query;\n            routeInfo.resolvedAs = resolvedAs;\n            this.components[route] = routeInfo;\n            return routeInfo;\n        } catch (err) {\n            return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n        }\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components[\"/_app\"].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split(\"#\", 2);\n        const [newUrlNoHash, newHash] = as.split(\"#\", 2);\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = \"\"] = as.split(\"#\", 2);\n        (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n            // Scroll to top if the hash is just `#` with no value or `#top`\n            // To mirror browsers\n            if (hash === \"\" || hash === \"top\") {\n                window.scrollTo(0, 0);\n                return;\n            }\n            // Decode hash to make non-latin anchor works.\n            const rawHash = decodeURIComponent(hash);\n            // First we check if the element by id is found\n            const idEl = document.getElementById(rawHash);\n            if (idEl) {\n                idEl.scrollIntoView();\n                return;\n            }\n            // If there's no element with the id, we check the `name` property\n            // To mirror browsers\n            const nameEl = document.getElementsByName(rawHash)[0];\n            if (nameEl) {\n                nameEl.scrollIntoView();\n            }\n        }, {\n            onlyHashChange: this.onlyAHashChange(as)\n        });\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ async prefetch(url, asPath, options) {\n        if (asPath === void 0) asPath = url;\n        if (options === void 0) options = {};\n        // Prefetch is not supported in development mode because it would trigger on-demand-entries\n        if (true) {\n            return;\n        }\n        if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n            // No prefetches for bots that render the link since they are typically navigating\n            // links via the equivalent of a hard navigation and hence never utilize these\n            // prefetches.\n            return;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        const urlPathname = parsed.pathname;\n        let { pathname, query } = parsed;\n        const originalPathname = pathname;\n        if (false) {}\n        const pages = await this.pageLoader.getPageList();\n        let resolvedAs = asPath;\n        const locale = typeof options.locale !== \"undefined\" ? options.locale || undefined : this.locale;\n        const isMiddlewareMatch = await matchesMiddleware({\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        if ( true && asPath.startsWith(\"/\")) {\n            let rewrites;\n            ({ __rewrites: rewrites } = await (0, _routeloader.getClientBuildManifest)());\n            const rewritesResult = (0, _resolverewrites.default)((0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asPath, this.locale), true), pages, rewrites, parsed.query, (p)=>resolveDynamicRoute(p, pages), this.locales);\n            if (rewritesResult.externalDest) {\n                return;\n            }\n            if (!isMiddlewareMatch) {\n                resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(rewritesResult.asPath), this.locale);\n            }\n            if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n                // if this directly matches a page we need to update the href to\n                // allow the correct page chunk to be loaded\n                pathname = rewritesResult.resolvedHref;\n                parsed.pathname = pathname;\n                if (!isMiddlewareMatch) {\n                    url = (0, _formaturl.formatWithValidation)(parsed);\n                }\n            }\n        }\n        parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n        if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n            pathname = parsed.pathname;\n            parsed.pathname = pathname;\n            Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n            if (!isMiddlewareMatch) {\n                url = (0, _formaturl.formatWithValidation)(parsed);\n            }\n        }\n        const data =  false ? 0 : await withMiddlewareEffects({\n            fetchData: ()=>fetchNextData({\n                    dataHref: this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname: originalPathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: this.isSsr,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true\n                }),\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === \"rewrite\") {\n            parsed.pathname = data.effect.resolvedHref;\n            pathname = data.effect.resolvedHref;\n            query = {\n                ...query,\n                ...data.effect.parsedAs.query\n            };\n            resolvedAs = data.effect.parsedAs.pathname;\n            url = (0, _formaturl.formatWithValidation)(parsed);\n        }\n        /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === \"redirect-external\") {\n            return;\n        }\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n            this.components[urlPathname] = {\n                __appRouter: true\n            };\n        }\n        await Promise.all([\n            this.pageLoader._isSsg(route).then((isSsg)=>{\n                return isSsg ? fetchNextData({\n                    dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n                        href: url,\n                        asPath: resolvedAs,\n                        locale: locale\n                    }),\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true,\n                    unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                }).then(()=>false).catch(()=>false) : false;\n            }),\n            this.pageLoader[options.priority ? \"loadPage\" : \"prefetch\"](route)\n        ]);\n    }\n    async fetchComponent(route) {\n        const handleCancelled = getCancelledHandler({\n            route,\n            router: this\n        });\n        try {\n            const componentResult = await this.pageLoader.loadPage(route);\n            handleCancelled();\n            return componentResult;\n        } catch (err) {\n            handleCancelled();\n            throw err;\n        }\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error(\"Loading initial props cancelled\");\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    _getFlightData(dataHref) {\n        // Do not cache RSC flight response since it's not a static resource\n        return fetchNextData({\n            dataHref,\n            isServerRender: true,\n            parseJSON: false,\n            inflightCache: this.sdc,\n            persistCache: false,\n            isPrefetch: false\n        }).then((param)=>{\n            let { text } = param;\n            return {\n                data: text\n            };\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App } = this.components[\"/_app\"];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils.loadGetInitialProps)(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname, query, as, { initialProps, pageLoader, App, wrapApp, Component, err, subscription, isFallback, locale, locales, defaultLocale, domainLocales, isPreview }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname, query } = this;\n                this.changeState(\"replaceState\", (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(pathname),\n                    query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url, as, options, key } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname } = (0, _parserelativeurl.parseRelativeUrl)(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change(\"replaceState\", url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== \"/_error\") {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components[\"/_app\"] = {\n            Component: App,\n            styleSheets: []\n        };\n        if (true) {\n            const { BloomFilter } = __webpack_require__(/*! ../../lib/bloom-filter */ \"./node_modules/next/dist/shared/lib/bloom-filter.js\");\n            const staticFilterData = {\"numItems\":3,\"errorRate\":0.01,\"numBits\":29,\"numHashes\":7,\"bitArray\":[0,0,0,1,1,1,1,1,0,1,1,0,1,0,1,1,0,0,1,1,1,0,1,1,1,0,1,0,1]};\n            const dynamicFilterData = {\"numItems\":0,\"errorRate\":0.01,\"numBits\":0,\"numHashes\":null,\"bitArray\":[]};\n            if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                this._bfl_s.import(staticFilterData);\n            }\n            if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                this._bfl_d.import(dynamicFilterData);\n            }\n        }\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || \"\";\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !true);\n        if (false) {}\n        this.state = {\n            route,\n            pathname,\n            query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith(\"//\")) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    options._shouldResolveHref = as !== pathname;\n                    this.changeState(\"replaceState\", matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener(\"popstate\", this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\nRouter.events = (0, _mitt.default)(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});