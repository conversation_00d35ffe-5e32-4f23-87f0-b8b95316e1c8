"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/health/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkApiStatus: function() { return /* binding */ checkApiStatus; },\n/* harmony export */   getDetailedHealth: function() { return /* binding */ getDetailedHealth; },\n/* harmony export */   getHealth: function() { return /* binding */ getHealth; },\n/* harmony export */   getMockHealthStatus: function() { return /* binding */ getMockHealthStatus; },\n/* harmony export */   getMockVerification: function() { return /* binding */ getMockVerification; },\n/* harmony export */   submitVerification: function() { return /* binding */ submitVerification; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n// API Client for CarbonLedger Frontend\n\n\nclass ApiClient {\n    setupInterceptors() {\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            // Add request timestamp for debugging\n            config.metadata = {\n                startTime: new Date()\n            };\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>{\n            var _response_config_metadata_startTime, _response_config_metadata;\n            // Calculate request duration\n            const endTime = new Date();\n            const duration = endTime.getTime() - ((_response_config_metadata = response.config.metadata) === null || _response_config_metadata === void 0 ? void 0 : (_response_config_metadata_startTime = _response_config_metadata.startTime) === null || _response_config_metadata_startTime === void 0 ? void 0 : _response_config_metadata_startTime.getTime());\n            if (true) {\n                var _response_config_method;\n                console.log(\"API Request: \".concat((_response_config_method = response.config.method) === null || _response_config_method === void 0 ? void 0 : _response_config_method.toUpperCase(), \" \").concat(response.config.url, \" - \").concat(duration, \"ms\"));\n            }\n            return response;\n        }, (error)=>{\n            this.handleApiError(error);\n            return Promise.reject(error);\n        });\n    }\n    handleApiError(error) {\n        if (error.response) {\n            // Server responded with error status\n            const status = error.response.status;\n            const data = error.response.data;\n            let message = \"An error occurred\";\n            if (data === null || data === void 0 ? void 0 : data.user_message) {\n                message = data.user_message;\n            } else if (data === null || data === void 0 ? void 0 : data.detail) {\n                message = data.detail;\n            } else if (status === 429) {\n                message = \"Too many requests. Please try again later.\";\n            } else if (status === 500) {\n                message = \"Server error. Please try again later.\";\n            } else if (status === 503) {\n                message = \"Service temporarily unavailable.\";\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(message);\n        } else if (error.request) {\n            // Network error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n        } else {\n            // Other error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"An unexpected error occurred.\");\n        }\n    }\n    // Health endpoints\n    async getHealth() {\n        const response = await this.client.get(\"/health\");\n        return response.data;\n    }\n    async getDetailedHealth() {\n        const response = await this.client.get(\"/health/detailed\");\n        return response.data;\n    }\n    // Verification endpoints\n    async submitVerification(request) {\n        const response = await this.client.post(\"/api/v1/verify\", request);\n        return response.data;\n    }\n    // Blockchain endpoints (when enabled)\n    async mintTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/mint\", request);\n        return response.data;\n    }\n    async retireTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/retire\", request);\n        return response.data;\n    }\n    async getTokenBalance(address, tokenId) {\n        const response = await this.client.get(\"/api/v1/blockchain/balance/\".concat(address, \"/\").concat(tokenId));\n        return response.data;\n    }\n    async getTokenMetadata(tokenId) {\n        const response = await this.client.get(\"/api/v1/blockchain/metadata/\".concat(tokenId));\n        return response.data;\n    }\n    async getGasEstimate(operation) {\n        const response = await this.client.get(\"/api/v1/blockchain/gas-estimate/\".concat(operation));\n        return response.data;\n    }\n    async getBlockchainHealth() {\n        const response = await this.client.get(\"/api/v1/blockchain/health\");\n        return response.data;\n    }\n    // Utility methods\n    async checkApiStatus() {\n        try {\n            await this.getHealth();\n            return true;\n        } catch (e) {\n            return false;\n        }\n    }\n    getBaseURL() {\n        return this.baseURL;\n    }\n    // Analytics endpoints\n    async getDashboardStats() {\n        const response = await this.client.get(\"/api/v1/analytics/dashboard-stats\");\n        return response.data;\n    }\n    async getVerificationTrends() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const response = await this.client.get(\"/api/v1/analytics/trends?days=\".concat(days));\n        return response.data;\n    }\n    async getRegionalAnalysis() {\n        const response = await this.client.get(\"/api/v1/analytics/regional-analysis\");\n        return response.data;\n    }\n    async getPerformanceMetrics() {\n        const response = await this.client.get(\"/api/v1/analytics/performance-metrics\");\n        return response.data;\n    }\n    // Mock data methods for development (when backend is not available)\n    async getMockVerification(request) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const mockResponse = {\n            verification_id: \"mock-\".concat(Date.now()),\n            project_id: Math.floor(Math.random() * 1000),\n            project_name: request.project_name,\n            verification_complete: true,\n            deforestation_detected: Math.random() > 0.7,\n            deforested_area_km2: Math.random() * 10,\n            confidence_score: 0.85 + Math.random() * 0.15,\n            data_quality_score: 0.9 + Math.random() * 0.1,\n            verification_level: \"enhanced\",\n            sources_used: 3,\n            vegetation_indices: {\n                ndvi_mean: 0.7 + Math.random() * 0.2,\n                ndvi_std: 0.1 + Math.random() * 0.05,\n                evi_mean: 0.6 + Math.random() * 0.2\n            },\n            temporal_analysis: {\n                trend: Math.random() > 0.5 ? \"stable\" : \"declining\",\n                change_rate: -0.02 + Math.random() * 0.04\n            },\n            area_checked: {\n                total_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111,\n                analyzed_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111 * 0.95\n            },\n            analysis_period: {\n                start_date: \"2023-01-01\",\n                end_date: \"2023-12-31\"\n            },\n            verified_at: new Date().toISOString(),\n            processing_time_seconds: 45.2,\n            warnings: Math.random() > 0.8 ? [\n                \"Cloud cover detected in some areas\"\n            ] : undefined,\n            metadata: {\n                satellite_sources: [\n                    \"Landsat-8\",\n                    \"Sentinel-2\"\n                ],\n                processing_version: \"2.1.0\"\n            }\n        };\n        return mockResponse;\n    }\n    async getMockHealthStatus() {\n        const mockHealth = {\n            status: \"healthy\",\n            timestamp: new Date().toISOString(),\n            response_time_ms: 150,\n            version: \"1.0.0\",\n            environment: \"development\",\n            components: [\n                {\n                    name: \"database\",\n                    status: \"healthy\",\n                    message: \"Database connection healthy\",\n                    response_time_ms: 50\n                },\n                {\n                    name: \"earth_engine\",\n                    status: \"degraded\",\n                    message: \"Earth Engine not configured (development mode)\",\n                    response_time_ms: 0\n                },\n                {\n                    name: \"blockchain\",\n                    status: \"healthy\",\n                    message: \"Blockchain configuration loaded\",\n                    response_time_ms: 25\n                }\n            ]\n        };\n        return mockHealth;\n    }\n    constructor(){\n        this.baseURL = \"http://127.0.0.1:8001\" || 0;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: this.baseURL,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n// Export individual methods for convenience\nconst { getHealth, getDetailedHealth, submitVerification, checkApiStatus, getMockVerification, getMockHealthStatus } = apiClient;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});