"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard/DashboardStats.tsx":
/*!*****************************************************!*\
  !*** ./src/components/Dashboard/DashboardStats.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardStats; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentMagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,DocumentMagnifyingGlassIcon,ExclamationTriangleIcon,GlobeAltIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DashboardStats() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchStats = async ()=>{\n            try {\n                setIsLoading(true);\n                const dashboardData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getDashboardStats();\n                // Format the data for display\n                const formattedStats = [\n                    {\n                        name: \"Total Verifications\",\n                        value: dashboardData.total_verifications.toLocaleString(),\n                        change: \"+12%\",\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                        description: \"Projects verified to date\"\n                    },\n                    {\n                        name: \"Area Verified\",\n                        value: \"\".concat(Math.round(dashboardData.total_area_verified_km2).toLocaleString(), \" km\\xb2\"),\n                        change: \"+8%\",\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        description: \"Total forest area analyzed\"\n                    },\n                    {\n                        name: \"Deforestation Detected\",\n                        value: dashboardData.deforestation_detected_count.toLocaleString(),\n                        change: \"-5%\",\n                        changeType: \"decrease\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                        description: \"Projects with deforestation alerts\"\n                    },\n                    {\n                        name: \"Average Confidence\",\n                        value: \"\".concat(Math.round(dashboardData.average_confidence_score * 100), \"%\"),\n                        change: \"+2%\",\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        description: \"Average verification confidence\"\n                    }\n                ];\n                setStats(formattedStats);\n            } catch (error) {\n                console.error(\"Failed to fetch dashboard stats:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to load dashboard statistics\");\n                // Fallback to mock data if API fails\n                const fallbackStats = [\n                    {\n                        name: \"Total Verifications\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                        description: \"Unable to load data\"\n                    },\n                    {\n                        name: \"Area Verified\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        description: \"Unable to load data\"\n                    },\n                    {\n                        name: \"Deforestation Detected\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                        description: \"Unable to load data\"\n                    },\n                    {\n                        name: \"Average Confidence\",\n                        value: \"N/A\",\n                        change: \"0%\",\n                        changeType: \"neutral\",\n                        icon: _barrel_optimize_names_CheckCircleIcon_DocumentMagnifyingGlassIcon_ExclamationTriangleIcon_GlobeAltIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        description: \"Unable to load data\"\n                    }\n                ];\n                setStats(fallbackStats);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchStats();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n            children: [\n                ...Array(4)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gray-200 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-5 w-0 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded w-1/2 mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this)\n                }, i, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n        children: stats.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5,\n                    delay: index * 0.1\n                },\n                className: \"card hover:shadow-md transition-shadow duration-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-8 w-8 items-center justify-center rounded-md bg-primary-500 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-5 w-5\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-5 w-0 flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                        className: \"text-sm font-medium text-gray-500 truncate\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                        className: \"flex items-baseline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: item.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-2 flex items-baseline text-sm font-semibold \".concat(item.changeType === \"increase\" ? \"text-green-600\" : item.changeType === \"decrease\" ? \"text-red-600\" : \"text-gray-500\"),\n                                                children: [\n                                                    item.changeType === \"increase\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-3 w-3 flex-shrink-0 self-center text-green-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.changeType === \"decrease\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-3 w-3 flex-shrink-0 self-center text-red-500\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: [\n                                                            item.changeType === \"increase\" ? \"Increased\" : \"Decreased\",\n                                                            \" by\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.change\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this)\n            }, item.name, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/DashboardStats.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardStats, \"t6uReshbndM/zD8HsADkqvgIfFo=\");\n_c = DashboardStats;\nvar _c;\n$RefreshReg$(_c, \"DashboardStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/DashboardStats.tsx\n"));

/***/ })

});