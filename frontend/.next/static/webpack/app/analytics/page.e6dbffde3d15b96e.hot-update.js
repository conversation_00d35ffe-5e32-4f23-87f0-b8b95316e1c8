"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnalyticsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/__barrel_optimize__?names=CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AnalyticsPage() {\n    var _analyticsData_performanceMetrics_average_processing_time, _analyticsData_performanceMetrics, _analyticsData_performanceMetrics1, _analyticsData_performanceMetrics_total_projects, _analyticsData_performanceMetrics2, _analyticsData_performanceMetrics_active_projects, _analyticsData_performanceMetrics3;\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedTimeRange, setSelectedTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"trends\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchAnalyticsData();\n    }, [\n        selectedTimeRange\n    ]);\n    const fetchAnalyticsData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [trends, regionalAnalysis, performanceMetrics, dashboardStats] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getVerificationTrends(selectedTimeRange),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getRegionalAnalysis(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getPerformanceMetrics(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDashboardStats()\n            ]);\n            setAnalyticsData({\n                trends,\n                regionalAnalysis,\n                performanceMetrics,\n                dashboardStats\n            });\n        } catch (error) {\n            console.error(\"Failed to fetch analytics data:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load analytics data\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const timeRangeOptions = [\n        {\n            value: 7,\n            label: \"7 Days\"\n        },\n        {\n            value: 30,\n            label: \"30 Days\"\n        },\n        {\n            value: 90,\n            label: \"90 Days\"\n        },\n        {\n            value: 365,\n            label: \"1 Year\"\n        }\n    ];\n    const COLORS = [\n        \"#22c55e\",\n        \"#3b82f6\",\n        \"#f59e0b\",\n        \"#ef4444\",\n        \"#8b5cf6\",\n        \"#06b6d4\"\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"Loading analytics...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Comprehensive insights into verification trends and system performance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedTimeRange,\n                                            onChange: (e)=>setSelectedTimeRange(Number(e.target.value)),\n                                            className: \"rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                            children: timeRangeOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.1\n                        },\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\",\n                        children: (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.dashboardStats) && [\n                            {\n                                name: \"Total Verifications\",\n                                value: analyticsData.dashboardStats.total_verifications.toLocaleString(),\n                                icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                color: \"bg-blue-500\"\n                            },\n                            {\n                                name: \"Area Verified\",\n                                value: \"\".concat(Math.round(analyticsData.dashboardStats.total_area_verified_km2).toLocaleString(), \" km\\xb2\"),\n                                icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                color: \"bg-green-500\"\n                            },\n                            {\n                                name: \"Deforestation Alerts\",\n                                value: analyticsData.dashboardStats.deforestation_detected_count.toLocaleString(),\n                                icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrendingUpIcon,\n                                color: \"bg-red-500\"\n                            },\n                            {\n                                name: \"Avg Confidence\",\n                                value: \"\".concat(Math.round(analyticsData.dashboardStats.average_confidence_score * 100), \"%\"),\n                                icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                color: \"bg-purple-500\"\n                            }\n                        ].map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 p-3 rounded-lg \".concat(metric.color),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: metric.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                    children: metric.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, metric.name, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8\",\n                                children: [\n                                    {\n                                        id: \"trends\",\n                                        name: \"Verification Trends\",\n                                        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrendingUpIcon\n                                    },\n                                    {\n                                        id: \"regional\",\n                                        name: \"Regional Analysis\",\n                                        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                    },\n                                    {\n                                        id: \"performance\",\n                                        name: \"Performance Metrics\",\n                                        icon: _barrel_optimize_names_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            activeTab === \"trends\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-4 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Verification Trends\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Daily verification counts and deforestation rates\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-80\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.LineChart, {\n                                                        data: (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.trends) || [],\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.CartesianGrid, {\n                                                                strokeDasharray: \"3 3\",\n                                                                stroke: \"#f0f0f0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.XAxis, {\n                                                                dataKey: \"date\",\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.YAxis, {\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Legend, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"count\",\n                                                                stroke: \"#22c55e\",\n                                                                strokeWidth: 2,\n                                                                name: \"Verifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"deforestation_rate\",\n                                                                stroke: \"#ef4444\",\n                                                                strokeWidth: 2,\n                                                                name: \"Deforestation Rate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"regional\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-4 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Regional Distribution\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Verification activity by geographic region\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-80\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.ResponsiveContainer, {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.BarChart, {\n                                                        data: (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.regionalAnalysis) || [],\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.CartesianGrid, {\n                                                                strokeDasharray: \"3 3\",\n                                                                stroke: \"#f0f0f0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.XAxis, {\n                                                                dataKey: \"region\",\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.YAxis, {\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Bar, {\n                                                                dataKey: \"total_verifications\",\n                                                                fill: \"#22c55e\",\n                                                                name: \"Total Verifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Bar, {\n                                                                dataKey: \"deforestation_detected\",\n                                                                fill: \"#ef4444\",\n                                                                name: \"Deforestation Detected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"performance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"System Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Average Processing Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: [\n                                                                        analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics === void 0 ? void 0 : (_analyticsData_performanceMetrics_average_processing_time = _analyticsData_performanceMetrics.average_processing_time) === null || _analyticsData_performanceMetrics_average_processing_time === void 0 ? void 0 : _analyticsData_performanceMetrics_average_processing_time.toFixed(1),\n                                                                        \"s\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: [\n                                                                        Math.round(((analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics1 = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics1 === void 0 ? void 0 : _analyticsData_performanceMetrics1.success_rate) || 0) * 100),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Total Projects\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics2 = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics2 === void 0 ? void 0 : (_analyticsData_performanceMetrics_total_projects = _analyticsData_performanceMetrics2.total_projects) === null || _analyticsData_performanceMetrics_total_projects === void 0 ? void 0 : _analyticsData_performanceMetrics_total_projects.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Active Projects\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics3 = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics3 === void 0 ? void 0 : (_analyticsData_performanceMetrics_active_projects = _analyticsData_performanceMetrics3.active_projects) === null || _analyticsData_performanceMetrics_active_projects === void 0 ? void 0 : _analyticsData_performanceMetrics_active_projects.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, activeTab, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"GleUrb7Zf6X54IgPErnkPMkNYxQ=\");\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});