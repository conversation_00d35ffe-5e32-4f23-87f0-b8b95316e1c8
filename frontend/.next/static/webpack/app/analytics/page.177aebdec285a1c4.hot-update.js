"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/__barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!****************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \****************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowDownTrayIcon: function() { return /* reexport safe */ _ArrowDownTrayIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   CalendarIcon: function() { return /* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   ChartBarIcon: function() { return /* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   DocumentChartBarIcon: function() { return /* reexport safe */ _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   GlobeAltIcon: function() { return /* reexport safe */ _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ArrowDownTrayIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowDownTrayIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DocumentChartBarIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js\");\n/* harmony import */ var _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./GlobeAltIcon.js */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFycm93RG93blRyYXlJY29uLENhbGVuZGFySWNvbixDaGFydEJhckljb24sRG9jdW1lbnRDaGFydEJhckljb24sR2xvYmVBbHRJY29uLFRyZW5kaW5nVXBJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQ3FFO0FBQ1Y7QUFDQTtBQUNnQjtBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8zY2EwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd0Rvd25UcmF5SWNvbiB9IGZyb20gXCIuL0Fycm93RG93blRyYXlJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsZW5kYXJJY29uIH0gZnJvbSBcIi4vQ2FsZW5kYXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hhcnRCYXJJY29uIH0gZnJvbSBcIi4vQ2hhcnRCYXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG9jdW1lbnRDaGFydEJhckljb24gfSBmcm9tIFwiLi9Eb2N1bWVudENoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JlQWx0SWNvbiB9IGZyb20gXCIuL0dsb2JlQWx0SWNvbi5qc1wiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJBcnJvd0Rvd25UcmF5SWNvbiIsIkNhbGVuZGFySWNvbiIsIkNoYXJ0QmFySWNvbiIsIkRvY3VtZW50Q2hhcnRCYXJJY29uIiwiR2xvYmVBbHRJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnalyticsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/__barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,DocumentChartBarIcon,GlobeAltIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AnalyticsPage() {\n    var _analyticsData_performanceMetrics_average_processing_time, _analyticsData_performanceMetrics, _analyticsData_performanceMetrics1, _analyticsData_performanceMetrics_total_projects, _analyticsData_performanceMetrics2, _analyticsData_performanceMetrics_active_projects, _analyticsData_performanceMetrics3;\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedTimeRange, setSelectedTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"trends\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchAnalyticsData();\n    }, [\n        selectedTimeRange\n    ]);\n    const fetchAnalyticsData = async ()=>{\n        try {\n            setIsLoading(true);\n            const [trends, regionalAnalysis, performanceMetrics, dashboardStats] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getVerificationTrends(selectedTimeRange),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getRegionalAnalysis(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getPerformanceMetrics(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDashboardStats()\n            ]);\n            setAnalyticsData({\n                trends,\n                regionalAnalysis,\n                performanceMetrics,\n                dashboardStats\n            });\n        } catch (error) {\n            console.error(\"Failed to fetch analytics data:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load analytics data\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const timeRangeOptions = [\n        {\n            value: 7,\n            label: \"7 Days\"\n        },\n        {\n            value: 30,\n            label: \"30 Days\"\n        },\n        {\n            value: 90,\n            label: \"90 Days\"\n        },\n        {\n            value: 365,\n            label: \"1 Year\"\n        }\n    ];\n    const COLORS = [\n        \"#22c55e\",\n        \"#3b82f6\",\n        \"#f59e0b\",\n        \"#ef4444\",\n        \"#8b5cf6\",\n        \"#06b6d4\"\n    ];\n    const exportToCSV = (data, filename)=>{\n        if (!data || data.length === 0) return;\n        const headers = Object.keys(data[0]);\n        const csvContent = [\n            headers.join(\",\"),\n            ...data.map((row)=>headers.map((header)=>row[header]).join(\",\"))\n        ].join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement(\"a\");\n        link.href = url;\n        link.download = \"\".concat(filename, \"-\").concat(new Date().toISOString().split(\"T\")[0], \".csv\");\n        link.click();\n        window.URL.revokeObjectURL(url);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"Loading analytics...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"Comprehensive insights into verification trends and system performance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedTimeRange,\n                                            onChange: (e)=>setSelectedTimeRange(Number(e.target.value)),\n                                            className: \"rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                            children: timeRangeOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                if (activeTab === \"trends\" && (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.trends)) {\n                                                    exportToCSV(analyticsData.trends, \"verification-trends\");\n                                                } else if (activeTab === \"regional\" && (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.regionalAnalysis)) {\n                                                    exportToCSV(analyticsData.regionalAnalysis, \"regional-analysis\");\n                                                } else if (activeTab === \"performance\" && (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.performanceMetrics)) {\n                                                    exportToCSV([\n                                                        analyticsData.performanceMetrics\n                                                    ], \"performance-metrics\");\n                                                }\n                                            },\n                                            className: \"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.1\n                        },\n                        className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\",\n                        children: (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.dashboardStats) && [\n                            {\n                                name: \"Total Verifications\",\n                                value: analyticsData.dashboardStats.total_verifications.toLocaleString(),\n                                icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                color: \"bg-blue-500\"\n                            },\n                            {\n                                name: \"Area Verified\",\n                                value: \"\".concat(Math.round(analyticsData.dashboardStats.total_area_verified_km2).toLocaleString(), \" km\\xb2\"),\n                                icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                color: \"bg-green-500\"\n                            },\n                            {\n                                name: \"Deforestation Alerts\",\n                                value: analyticsData.dashboardStats.deforestation_detected_count.toLocaleString(),\n                                icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.TrendingUpIcon,\n                                color: \"bg-red-500\"\n                            },\n                            {\n                                name: \"Avg Confidence\",\n                                value: \"\".concat(Math.round(analyticsData.dashboardStats.average_confidence_score * 100), \"%\"),\n                                icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                color: \"bg-purple-500\"\n                            }\n                        ].map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 p-3 rounded-lg \".concat(metric.color),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(metric.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: metric.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                    children: metric.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, this)\n                            }, metric.name, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8\",\n                                children: [\n                                    {\n                                        id: \"trends\",\n                                        name: \"Verification Trends\",\n                                        icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.TrendingUpIcon\n                                    },\n                                    {\n                                        id: \"regional\",\n                                        name: \"Regional Analysis\",\n                                        icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                    },\n                                    {\n                                        id: \"performance\",\n                                        name: \"Performance Metrics\",\n                                        icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_DocumentChartBarIcon_GlobeAltIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            activeTab === \"trends\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-4 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Verification Trends\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Daily verification counts and deforestation rates\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-80\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.ResponsiveContainer, {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.LineChart, {\n                                                        data: (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.trends) || [],\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.CartesianGrid, {\n                                                                strokeDasharray: \"3 3\",\n                                                                stroke: \"#f0f0f0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.XAxis, {\n                                                                dataKey: \"date\",\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.YAxis, {\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.Legend, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"count\",\n                                                                stroke: \"#22c55e\",\n                                                                strokeWidth: 2,\n                                                                name: \"Verifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"deforestation_rate\",\n                                                                stroke: \"#ef4444\",\n                                                                strokeWidth: 2,\n                                                                name: \"Deforestation Rate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"regional\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-4 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Regional Distribution\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Verification activity by geographic region\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-80\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.ResponsiveContainer, {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.BarChart, {\n                                                        data: (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.regionalAnalysis) || [],\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.CartesianGrid, {\n                                                                strokeDasharray: \"3 3\",\n                                                                stroke: \"#f0f0f0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.XAxis, {\n                                                                dataKey: \"region\",\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.YAxis, {\n                                                                axisLine: false,\n                                                                tickLine: false,\n                                                                tick: {\n                                                                    fontSize: 12,\n                                                                    fill: \"#6b7280\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Tooltip, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.Bar, {\n                                                                dataKey: \"total_verifications\",\n                                                                fill: \"#22c55e\",\n                                                                name: \"Total Verifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.Bar, {\n                                                                dataKey: \"deforestation_detected\",\n                                                                fill: \"#ef4444\",\n                                                                name: \"Deforestation Detected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"performance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 py-4 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"System Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Average Processing Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: [\n                                                                        analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics === void 0 ? void 0 : (_analyticsData_performanceMetrics_average_processing_time = _analyticsData_performanceMetrics.average_processing_time) === null || _analyticsData_performanceMetrics_average_processing_time === void 0 ? void 0 : _analyticsData_performanceMetrics_average_processing_time.toFixed(1),\n                                                                        \"s\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: [\n                                                                        Math.round(((analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics1 = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics1 === void 0 ? void 0 : _analyticsData_performanceMetrics1.success_rate) || 0) * 100),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Total Projects\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics2 = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics2 === void 0 ? void 0 : (_analyticsData_performanceMetrics_total_projects = _analyticsData_performanceMetrics2.total_projects) === null || _analyticsData_performanceMetrics_total_projects === void 0 ? void 0 : _analyticsData_performanceMetrics_total_projects.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Active Projects\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_performanceMetrics3 = analyticsData.performanceMetrics) === null || _analyticsData_performanceMetrics3 === void 0 ? void 0 : (_analyticsData_performanceMetrics_active_projects = _analyticsData_performanceMetrics3.active_projects) === null || _analyticsData_performanceMetrics_active_projects === void 0 ? void 0 : _analyticsData_performanceMetrics_active_projects.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, activeTab, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cl/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"GleUrb7Zf6X54IgPErnkPMkNYxQ=\");\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowDownTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowDownTrayIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0Fycm93RG93blRyYXlJY29uLmpzIiwibWFwcGluZ3MiOiI7O0FBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELHNCQUFzQixnREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRywrQkFBK0IsZ0RBQW1CO0FBQ3JEO0FBQ0EsR0FBRyw4QkFBOEIsZ0RBQW1CO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLGlDQUFpQyw2Q0FBZ0I7QUFDakQsK0RBQWUsVUFBVSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9BcnJvd0Rvd25UcmF5SWNvbi5qcz8zOWE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gQXJyb3dEb3duVHJheUljb24oe1xuICB0aXRsZSxcbiAgdGl0bGVJZCxcbiAgLi4ucHJvcHNcbn0sIHN2Z1JlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzdmdcIiwgT2JqZWN0LmFzc2lnbih7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICBmaWxsOiBcIm5vbmVcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgIHN0cm9rZVdpZHRoOiAxLjUsXG4gICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogXCJ0cnVlXCIsXG4gICAgXCJkYXRhLXNsb3RcIjogXCJpY29uXCIsXG4gICAgcmVmOiBzdmdSZWYsXG4gICAgXCJhcmlhLWxhYmVsbGVkYnlcIjogdGl0bGVJZFxuICB9LCBwcm9wcyksIHRpdGxlID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJ0aXRsZVwiLCB7XG4gICAgaWQ6IHRpdGxlSWRcbiAgfSwgdGl0bGUpIDogbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXRoXCIsIHtcbiAgICBzdHJva2VMaW5lY2FwOiBcInJvdW5kXCIsXG4gICAgc3Ryb2tlTGluZWpvaW46IFwicm91bmRcIixcbiAgICBkOiBcIk0zIDE2LjV2Mi4yNUEyLjI1IDIuMjUgMCAwIDAgNS4yNSAyMWgxMy41QTIuMjUgMi4yNSAwIDAgMCAyMSAxOC43NVYxNi41TTE2LjUgMTIgMTIgMTYuNW0wIDBMNy41IDEybTQuNSA0LjVWM1wiXG4gIH0pKTtcbn1cbmNvbnN0IEZvcndhcmRSZWYgPSAvKiNfX1BVUkVfXyovIFJlYWN0LmZvcndhcmRSZWYoQXJyb3dEb3duVHJheUljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\n"));

/***/ })

});