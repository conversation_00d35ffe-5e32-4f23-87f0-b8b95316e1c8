"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard/RecentVerifications.tsx":
/*!**********************************************************!*\
  !*** ./src/components/Dashboard/RecentVerifications.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RecentVerifications; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RecentVerifications() {\n    _s();\n    const [verifications, setVerifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate API call to fetch recent verifications\n        const fetchVerifications = async ()=>{\n            // Mock data\n            const mockVerifications = [\n                {\n                    id: \"1\",\n                    project_name: \"Amazon Forest Conservation Project\",\n                    status: \"completed\",\n                    deforestation_detected: false,\n                    confidence_score: 0.94,\n                    area_km2: 1250.5,\n                    verified_at: \"2024-01-15T10:30:00Z\"\n                },\n                {\n                    id: \"2\",\n                    project_name: \"Congo Basin Protection Initiative\",\n                    status: \"completed\",\n                    deforestation_detected: true,\n                    confidence_score: 0.87,\n                    area_km2: 890.2,\n                    verified_at: \"2024-01-14T15:45:00Z\"\n                },\n                {\n                    id: \"3\",\n                    project_name: \"Borneo Rainforest Preservation\",\n                    status: \"processing\",\n                    deforestation_detected: false,\n                    confidence_score: 0,\n                    area_km2: 2100.8,\n                    verified_at: \"2024-01-14T09:20:00Z\"\n                },\n                {\n                    id: \"4\",\n                    project_name: \"Madagascar Forest Restoration\",\n                    status: \"completed\",\n                    deforestation_detected: false,\n                    confidence_score: 0.91,\n                    area_km2: 567.3,\n                    verified_at: \"2024-01-13T14:15:00Z\"\n                },\n                {\n                    id: \"5\",\n                    project_name: \"Atlantic Forest Recovery\",\n                    status: \"failed\",\n                    deforestation_detected: false,\n                    confidence_score: 0,\n                    area_km2: 0,\n                    verified_at: \"2024-01-13T11:00:00Z\"\n                }\n            ];\n            setTimeout(()=>{\n                setVerifications(mockVerifications);\n                setIsLoading(false);\n            }, 1000);\n        };\n        fetchVerifications();\n    }, []);\n    const getStatusIcon = (status, deforestation)=>{\n        switch(status){\n            case \"completed\":\n                return deforestation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status, deforestation)=>{\n        switch(status){\n            case \"completed\":\n                return deforestation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-error\",\n                    children: \"Deforestation Detected\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-success\",\n                    children: \"Verified Clean\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-warning\",\n                    children: \"Processing\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-error\",\n                    children: \"Failed\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-info\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 w-5 bg-gray-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-16 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Recent Verifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/verifications\",\n                        className: \"text-sm text-primary-600 hover:text-primary-500\",\n                        children: \"View all\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: verifications.map((verification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.3,\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    getStatusIcon(verification.status, verification.deforestation_detected),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: verification.project_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: verification.area_km2 > 0 ? \"\".concat(verification.area_km2.toFixed(1), \" km\\xb2\") : \"Area pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    verification.confidence_score > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            (verification.confidence_score * 100).toFixed(0),\n                                                            \"% confidence\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: formatDate(verification.verified_at)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    getStatusBadge(verification.status, verification.deforestation_detected),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, verification.id, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            verifications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: 1.5,\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0.621 0 1.125-.504 1.125-1.125V9.375c0-.621.504-1.125 1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"No verifications yet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/verify\",\n                        className: \"mt-2 btn-primary inline-flex\",\n                        children: \"Start First Verification\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentVerifications, \"s13gZ0WWqi5yAWQxpzZE6xVi0yc=\");\n_c = RecentVerifications;\nvar _c;\n$RefreshReg$(_c, \"RecentVerifications\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/RecentVerifications.tsx\n"));

/***/ })

});