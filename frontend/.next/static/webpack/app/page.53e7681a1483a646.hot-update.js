"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard/RecentVerifications.tsx":
/*!**********************************************************!*\
  !*** ./src/components/Dashboard/RecentVerifications.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RecentVerifications; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,EyeIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction RecentVerifications() {\n    _s();\n    const [verifications, setVerifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchVerifications = async ()=>{\n            try {\n                setIsLoading(true);\n                const dashboardData = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDashboardStats();\n                // Transform API data to match component format\n                const formattedVerifications = dashboardData.recent_verifications.map((verification)=>({\n                        id: verification.verification_id,\n                        project_name: verification.project_name,\n                        status: verification.verification_complete ? \"completed\" : \"processing\",\n                        deforestation_detected: verification.deforestation_detected,\n                        confidence_score: verification.confidence_score || 0.85,\n                        area_km2: verification.deforested_area_km2,\n                        verified_at: verification.verified_at\n                    }));\n                setVerifications(formattedVerifications);\n            } catch (error) {\n                console.error(\"Failed to fetch recent verifications:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load recent verifications\");\n                // Fallback to empty array if API fails\n                setVerifications([]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchVerifications();\n    }, []);\n    const getStatusIcon = (status, deforestation)=>{\n        switch(status){\n            case \"completed\":\n                return deforestation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status, deforestation)=>{\n        switch(status){\n            case \"completed\":\n                return deforestation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-error\",\n                    children: \"Deforestation Detected\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-success\",\n                    children: \"Verified Clean\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-warning\",\n                    children: \"Processing\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 16\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-error\",\n                    children: \"Failed\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"badge-info\",\n                    children: \"Unknown\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 w-5 bg-gray-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-16 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Recent Verifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/verifications\",\n                        className: \"text-sm text-primary-600 hover:text-primary-500\",\n                        children: \"View all\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: verifications.map((verification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.3,\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    getStatusIcon(verification.status, verification.deforestation_detected),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                                children: verification.project_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: verification.area_km2 > 0 ? \"\".concat(verification.area_km2.toFixed(1), \" km\\xb2\") : \"Area pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    verification.confidence_score > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            (verification.confidence_score * 100).toFixed(0),\n                                                            \"% confidence\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: formatDate(verification.verified_at)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    getStatusBadge(verification.status, verification.deforestation_detected),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ClockIcon_ExclamationTriangleIcon_EyeIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, verification.id, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            verifications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: 1.5,\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0.621 0 1.125-.504 1.125-1.125V9.375c0-.621.504-1.125 1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"No verifications yet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/verify\",\n                        className: \"mt-2 btn-primary inline-flex\",\n                        children: \"Start First Verification\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/RecentVerifications.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentVerifications, \"s13gZ0WWqi5yAWQxpzZE6xVi0yc=\");\n_c = RecentVerifications;\nvar _c;\n$RefreshReg$(_c, \"RecentVerifications\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/RecentVerifications.tsx\n"));

/***/ })

});