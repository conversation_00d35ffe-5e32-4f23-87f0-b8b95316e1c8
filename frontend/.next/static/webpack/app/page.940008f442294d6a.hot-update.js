"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard/VerificationTrends.tsx":
/*!*********************************************************!*\
  !*** ./src/components/Dashboard/VerificationTrends.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerificationTrends; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction VerificationTrends() {\n    _s();\n    const [trendData, setTrendData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeChart, setActiveChart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"verifications\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate API call to fetch trend data\n        const fetchTrendData = async ()=>{\n            // Mock data\n            const mockData = [\n                {\n                    month: \"Jul\",\n                    verifications: 45,\n                    deforestation_rate: 0.12,\n                    average_confidence: 0.89\n                },\n                {\n                    month: \"Aug\",\n                    verifications: 52,\n                    deforestation_rate: 0.08,\n                    average_confidence: 0.91\n                },\n                {\n                    month: \"Sep\",\n                    verifications: 38,\n                    deforestation_rate: 0.15,\n                    average_confidence: 0.87\n                },\n                {\n                    month: \"Oct\",\n                    verifications: 67,\n                    deforestation_rate: 0.06,\n                    average_confidence: 0.93\n                },\n                {\n                    month: \"Nov\",\n                    verifications: 74,\n                    deforestation_rate: 0.09,\n                    average_confidence: 0.90\n                },\n                {\n                    month: \"Dec\",\n                    verifications: 89,\n                    deforestation_rate: 0.04,\n                    average_confidence: 0.95\n                }\n            ];\n            setTimeout(()=>{\n                setTrendData(mockData);\n                setIsLoading(false);\n            }, 800);\n        };\n        fetchTrendData();\n    }, []);\n    const CustomTooltip = (param)=>{\n        let { active, payload, label } = param;\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium text-gray-900\",\n                        children: \"\".concat(label, \" 2023\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    payload.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            style: {\n                                color: entry.color\n                            },\n                            children: \"\".concat(entry.name, \": \").concat(entry.dataKey === \"deforestation_rate\" ? \"\".concat((entry.value * 100).toFixed(1), \"%\") : entry.dataKey === \"average_confidence\" ? \"\".concat((entry.value * 100).toFixed(1), \"%\") : entry.value)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Verification Trends\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveChart(\"verifications\"),\n                                className: \"px-3 py-1 text-sm rounded-md transition-colors duration-200 \".concat(activeChart === \"verifications\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                children: \"Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveChart(\"deforestation\"),\n                                className: \"px-3 py-1 text-sm rounded-md transition-colors duration-200 \".concat(activeChart === \"deforestation\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                children: \"Deforestation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: activeChart === \"verifications\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.BarChart, {\n                        data: trendData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"#f0f0f0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.YAxis, {\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Bar, {\n                                dataKey: \"verifications\",\n                                fill: \"#22c55e\",\n                                radius: [\n                                    4,\n                                    4,\n                                    0,\n                                    0\n                                ],\n                                name: \"Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.LineChart, {\n                        data: trendData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"#f0f0f0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.YAxis, {\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                },\n                                tickFormatter: (value)=>\"\".concat((value * 100).toFixed(0), \"%\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"deforestation_rate\",\n                                stroke: \"#ef4444\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#ef4444\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6,\n                                    stroke: \"#ef4444\",\n                                    strokeWidth: 2\n                                },\n                                name: \"Deforestation Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"average_confidence\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#3b82f6\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6,\n                                    stroke: \"#3b82f6\",\n                                    strokeWidth: 2\n                                },\n                                name: \"Avg Confidence\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: trendData.reduce((sum, item)=>sum + item.verifications, 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-red-600\",\n                                children: [\n                                    (trendData.reduce((sum, item)=>sum + item.deforestation_rate, 0) / trendData.length * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg Deforestation Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: [\n                                    (trendData.reduce((sum, item)=>sum + item.average_confidence, 0) / trendData.length * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg Confidence\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(VerificationTrends, \"eSBnxnGSSimzZTKIfF3rDP97+KI=\");\n_c = VerificationTrends;\nvar _c;\n$RefreshReg$(_c, \"VerificationTrends\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/VerificationTrends.tsx\n"));

/***/ })

});