"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/verify/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkApiStatus: function() { return /* binding */ checkApiStatus; },\n/* harmony export */   getDetailedHealth: function() { return /* binding */ getDetailedHealth; },\n/* harmony export */   getHealth: function() { return /* binding */ getHealth; },\n/* harmony export */   getMockHealthStatus: function() { return /* binding */ getMockHealthStatus; },\n/* harmony export */   getMockVerification: function() { return /* binding */ getMockVerification; },\n/* harmony export */   submitVerification: function() { return /* binding */ submitVerification; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n// API Client for CarbonLedger Frontend\n\n\nclass ApiClient {\n    setupInterceptors() {\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            // Add request timestamp for debugging\n            config.metadata = {\n                startTime: new Date()\n            };\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>{\n            var _response_config_metadata_startTime, _response_config_metadata;\n            // Calculate request duration\n            const endTime = new Date();\n            const duration = endTime.getTime() - ((_response_config_metadata = response.config.metadata) === null || _response_config_metadata === void 0 ? void 0 : (_response_config_metadata_startTime = _response_config_metadata.startTime) === null || _response_config_metadata_startTime === void 0 ? void 0 : _response_config_metadata_startTime.getTime());\n            if (true) {\n                var _response_config_method;\n                console.log(\"API Request: \".concat((_response_config_method = response.config.method) === null || _response_config_method === void 0 ? void 0 : _response_config_method.toUpperCase(), \" \").concat(response.config.url, \" - \").concat(duration, \"ms\"));\n            }\n            return response;\n        }, (error)=>{\n            this.handleApiError(error);\n            return Promise.reject(error);\n        });\n    }\n    handleApiError(error) {\n        if (error.response) {\n            // Server responded with error status\n            const status = error.response.status;\n            const data = error.response.data;\n            let message = \"An error occurred\";\n            if (data === null || data === void 0 ? void 0 : data.user_message) {\n                message = data.user_message;\n            } else if (data === null || data === void 0 ? void 0 : data.detail) {\n                message = data.detail;\n            } else if (status === 429) {\n                message = \"Too many requests. Please try again later.\";\n            } else if (status === 500) {\n                message = \"Server error. Please try again later.\";\n            } else if (status === 503) {\n                message = \"Service temporarily unavailable.\";\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(message);\n        } else if (error.request) {\n            // Network error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"Network error. Please check your connection.\");\n        } else {\n            // Other error\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(\"An unexpected error occurred.\");\n        }\n    }\n    // Health endpoints\n    async getHealth() {\n        const response = await this.client.get(\"/health\");\n        return response.data;\n    }\n    async getDetailedHealth() {\n        const response = await this.client.get(\"/health/detailed\");\n        return response.data;\n    }\n    // Verification endpoints\n    async submitVerification(request) {\n        const response = await this.client.post(\"/api/v1/verify\", request);\n        return response.data;\n    }\n    // Blockchain endpoints (when enabled)\n    async mintTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/mint\", request);\n        return response.data;\n    }\n    async retireTokens(request) {\n        const response = await this.client.post(\"/api/v1/blockchain/retire\", request);\n        return response.data;\n    }\n    async getTokenBalance(address, tokenId) {\n        const response = await this.client.get(\"/api/v1/blockchain/balance/\".concat(address, \"/\").concat(tokenId));\n        return response.data;\n    }\n    async getTokenMetadata(tokenId) {\n        const response = await this.client.get(\"/api/v1/blockchain/metadata/\".concat(tokenId));\n        return response.data;\n    }\n    async getGasEstimate(operation) {\n        const response = await this.client.get(\"/api/v1/blockchain/gas-estimate/\".concat(operation));\n        return response.data;\n    }\n    async getBlockchainHealth() {\n        const response = await this.client.get(\"/api/v1/blockchain/health\");\n        return response.data;\n    }\n    // Utility methods\n    async checkApiStatus() {\n        try {\n            await this.getHealth();\n            return true;\n        } catch (e) {\n            return false;\n        }\n    }\n    getBaseURL() {\n        return this.baseURL;\n    }\n    // Mock data methods for development (when backend is not available)\n    async getMockVerification(request) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        const mockResponse = {\n            verification_id: \"mock-\".concat(Date.now()),\n            project_id: Math.floor(Math.random() * 1000),\n            project_name: request.project_name,\n            verification_complete: true,\n            deforestation_detected: Math.random() > 0.7,\n            deforested_area_km2: Math.random() * 10,\n            confidence_score: 0.85 + Math.random() * 0.15,\n            data_quality_score: 0.9 + Math.random() * 0.1,\n            verification_level: \"enhanced\",\n            sources_used: 3,\n            vegetation_indices: {\n                ndvi_mean: 0.7 + Math.random() * 0.2,\n                ndvi_std: 0.1 + Math.random() * 0.05,\n                evi_mean: 0.6 + Math.random() * 0.2\n            },\n            temporal_analysis: {\n                trend: Math.random() > 0.5 ? \"stable\" : \"declining\",\n                change_rate: -0.02 + Math.random() * 0.04\n            },\n            area_checked: {\n                total_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111,\n                analyzed_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111 * 0.95\n            },\n            analysis_period: {\n                start_date: \"2023-01-01\",\n                end_date: \"2023-12-31\"\n            },\n            verified_at: new Date().toISOString(),\n            processing_time_seconds: 45.2,\n            warnings: Math.random() > 0.8 ? [\n                \"Cloud cover detected in some areas\"\n            ] : undefined,\n            metadata: {\n                satellite_sources: [\n                    \"Landsat-8\",\n                    \"Sentinel-2\"\n                ],\n                processing_version: \"2.1.0\"\n            }\n        };\n        return mockResponse;\n    }\n    async getMockHealthStatus() {\n        const mockHealth = {\n            status: \"healthy\",\n            timestamp: new Date().toISOString(),\n            response_time_ms: 150,\n            version: \"1.0.0\",\n            environment: \"development\",\n            components: [\n                {\n                    name: \"database\",\n                    status: \"healthy\",\n                    message: \"Database connection healthy\",\n                    response_time_ms: 50\n                },\n                {\n                    name: \"earth_engine\",\n                    status: \"degraded\",\n                    message: \"Earth Engine not configured (development mode)\",\n                    response_time_ms: 0\n                },\n                {\n                    name: \"blockchain\",\n                    status: \"healthy\",\n                    message: \"Blockchain configuration loaded\",\n                    response_time_ms: 25\n                }\n            ]\n        };\n        return mockHealth;\n    }\n    constructor(){\n        this.baseURL = \"http://127.0.0.1:8001\" || 0;\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: this.baseURL,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n// Export individual methods for convenience\nconst { getHealth, getDetailedHealth, submitVerification, checkApiStatus, getMockVerification, getMockHealthStatus } = apiClient;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});