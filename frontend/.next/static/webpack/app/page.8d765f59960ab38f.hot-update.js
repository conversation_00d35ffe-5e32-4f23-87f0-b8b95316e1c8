"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard/VerificationTrends.tsx":
/*!*********************************************************!*\
  !*** ./src/components/Dashboard/VerificationTrends.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerificationTrends; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction VerificationTrends() {\n    _s();\n    const [trendData, setTrendData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeChart, setActiveChart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"verifications\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchTrendData = async ()=>{\n            try {\n                setIsLoading(true);\n                const trendsData = await _lib_api__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getVerificationTrends(30);\n                // Transform API data to match component format\n                const formattedData = trendsData.map((trend, index)=>{\n                    const date = new Date(trend.date);\n                    const monthNames = [\n                        \"Jan\",\n                        \"Feb\",\n                        \"Mar\",\n                        \"Apr\",\n                        \"May\",\n                        \"Jun\",\n                        \"Jul\",\n                        \"Aug\",\n                        \"Sep\",\n                        \"Oct\",\n                        \"Nov\",\n                        \"Dec\"\n                    ];\n                    return {\n                        month: \"\".concat(monthNames[date.getMonth()], \" \").concat(date.getDate()),\n                        verifications: trend.count,\n                        deforestation_rate: trend.deforestation_rate,\n                        average_confidence: trend.average_confidence\n                    };\n                });\n                setTrendData(formattedData);\n            } catch (error) {\n                console.error(\"Failed to fetch trend data:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to load verification trends\");\n                // Fallback to mock data if API fails\n                const fallbackData = [\n                    {\n                        month: \"No Data\",\n                        verifications: 0,\n                        deforestation_rate: 0,\n                        average_confidence: 0\n                    }\n                ];\n                setTrendData(fallbackData);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchTrendData();\n    }, []);\n    const CustomTooltip = (param)=>{\n        let { active, payload, label } = param;\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium text-gray-900\",\n                        children: \"\".concat(label, \" 2023\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    payload.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            style: {\n                                color: entry.color\n                            },\n                            children: \"\".concat(entry.name, \": \").concat(entry.dataKey === \"deforestation_rate\" ? \"\".concat((entry.value * 100).toFixed(1), \"%\") : entry.dataKey === \"average_confidence\" ? \"\".concat((entry.value * 100).toFixed(1), \"%\") : entry.value)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-64 bg-gray-200 rounded\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: \"Verification Trends\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveChart(\"verifications\"),\n                                className: \"px-3 py-1 text-sm rounded-md transition-colors duration-200 \".concat(activeChart === \"verifications\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                children: \"Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveChart(\"deforestation\"),\n                                className: \"px-3 py-1 text-sm rounded-md transition-colors duration-200 \".concat(activeChart === \"deforestation\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                children: \"Deforestation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: \"100%\",\n                    children: activeChart === \"verifications\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.BarChart, {\n                        data: trendData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"#f0f0f0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Bar, {\n                                dataKey: \"verifications\",\n                                fill: \"#22c55e\",\n                                radius: [\n                                    4,\n                                    4,\n                                    0,\n                                    0\n                                ],\n                                name: \"Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.LineChart, {\n                        data: trendData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"#f0f0f0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                                dataKey: \"month\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fontSize: 12,\n                                    fill: \"#6b7280\"\n                                },\n                                tickFormatter: (value)=>\"\".concat((value * 100).toFixed(0), \"%\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"deforestation_rate\",\n                                stroke: \"#ef4444\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#ef4444\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6,\n                                    stroke: \"#ef4444\",\n                                    strokeWidth: 2\n                                },\n                                name: \"Deforestation Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"average_confidence\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: \"#3b82f6\",\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6,\n                                    stroke: \"#3b82f6\",\n                                    strokeWidth: 2\n                                },\n                                name: \"Avg Confidence\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: trendData.reduce((sum, item)=>sum + item.verifications, 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Total Verifications\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-red-600\",\n                                children: [\n                                    (trendData.reduce((sum, item)=>sum + item.deforestation_rate, 0) / trendData.length * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg Deforestation Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: [\n                                    (trendData.reduce((sum, item)=>sum + item.average_confidence, 0) / trendData.length * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg Confidence\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cl/frontend/src/components/Dashboard/VerificationTrends.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(VerificationTrends, \"eSBnxnGSSimzZTKIfF3rDP97+KI=\");\n_c = VerificationTrends;\nvar _c;\n$RefreshReg$(_c, \"VerificationTrends\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard/VerificationTrends.tsx\n"));

/***/ })

});