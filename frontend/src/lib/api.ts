// API Client for CarbonLedger Frontend

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';
import type {
  VerificationRequest,
  VerificationResponse,
  HealthStatus,
  ApiResponse,
  MintTokenRequest,
  RetireTokenRequest,
  TokenBalanceResponse,
  TokenMetadataResponse,
  TransactionResponse,
  GasEstimate,
  DashboardStats,
} from '@/types/api';

class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8002';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30 seconds
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add request timestamp for debugging
        config.metadata = { startTime: new Date() };
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // Calculate request duration
        const endTime = new Date();
        const duration = endTime.getTime() - response.config.metadata?.startTime?.getTime();
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);
        }
        
        return response;
      },
      (error: AxiosError) => {
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  private handleApiError(error: AxiosError) {
    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const data = error.response.data as any;
      
      let message = 'An error occurred';
      
      if (data?.user_message) {
        message = data.user_message;
      } else if (data?.detail) {
        message = data.detail;
      } else if (status === 429) {
        message = 'Too many requests. Please try again later.';
      } else if (status === 500) {
        message = 'Server error. Please try again later.';
      } else if (status === 503) {
        message = 'Service temporarily unavailable.';
      }
      
      toast.error(message);
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      toast.error('An unexpected error occurred.');
    }
  }

  // Health endpoints
  async getHealth(): Promise<{ status: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }

  async getDetailedHealth(): Promise<HealthStatus> {
    const response = await this.client.get('/health/detailed');
    return response.data;
  }

  // Verification endpoints
  async submitVerification(request: VerificationRequest): Promise<VerificationResponse> {
    const response = await this.client.post('/api/v1/verify', request);
    return response.data;
  }

  // Blockchain endpoints (when enabled)
  async mintTokens(request: MintTokenRequest): Promise<TransactionResponse> {
    const response = await this.client.post('/api/v1/blockchain/mint', request);
    return response.data;
  }

  async retireTokens(request: RetireTokenRequest): Promise<TransactionResponse> {
    const response = await this.client.post('/api/v1/blockchain/retire', request);
    return response.data;
  }

  async getTokenBalance(address: string, tokenId: number): Promise<TokenBalanceResponse> {
    const response = await this.client.get(`/api/v1/blockchain/balance/${address}/${tokenId}`);
    return response.data;
  }

  async getTokenMetadata(tokenId: number): Promise<TokenMetadataResponse> {
    const response = await this.client.get(`/api/v1/blockchain/metadata/${tokenId}`);
    return response.data;
  }

  async getGasEstimate(operation: string): Promise<GasEstimate> {
    const response = await this.client.get(`/api/v1/blockchain/gas-estimate/${operation}`);
    return response.data;
  }

  async getBlockchainHealth(): Promise<{ status: string; details: any }> {
    const response = await this.client.get('/api/v1/blockchain/health');
    return response.data;
  }

  // Utility methods
  async checkApiStatus(): Promise<boolean> {
    try {
      await this.getHealth();
      return true;
    } catch {
      return false;
    }
  }

  getBaseURL(): string {
    return this.baseURL;
  }

  // Analytics endpoints
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.client.get('/api/v1/analytics/dashboard-stats');
    return response.data;
  }

  async getVerificationTrends(days: number = 30): Promise<any[]> {
    const response = await this.client.get(`/api/v1/analytics/trends?days=${days}`);
    return response.data;
  }

  async getRegionalAnalysis(): Promise<any[]> {
    const response = await this.client.get('/api/v1/analytics/regional-analysis');
    return response.data;
  }

  async getPerformanceMetrics(): Promise<any> {
    const response = await this.client.get('/api/v1/analytics/performance-metrics');
    return response.data;
  }

  // Mock data methods for development (when backend is not available)
  async getMockVerification(request: VerificationRequest): Promise<VerificationResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const mockResponse: VerificationResponse = {
      verification_id: `mock-${Date.now()}`,
      project_id: Math.floor(Math.random() * 1000),
      project_name: request.project_name,
      verification_complete: true,
      deforestation_detected: Math.random() > 0.7,
      deforested_area_km2: Math.random() * 10,
      confidence_score: 0.85 + Math.random() * 0.15,
      data_quality_score: 0.9 + Math.random() * 0.1,
      verification_level: 'enhanced',
      sources_used: 3,
      vegetation_indices: {
        ndvi_mean: 0.7 + Math.random() * 0.2,
        ndvi_std: 0.1 + Math.random() * 0.05,
        evi_mean: 0.6 + Math.random() * 0.2,
      },
      temporal_analysis: {
        trend: Math.random() > 0.5 ? 'stable' : 'declining',
        change_rate: -0.02 + Math.random() * 0.04,
      },
      area_checked: {
        total_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111,
        analyzed_area_km2: Math.abs((request.lon_max - request.lon_min) * (request.lat_max - request.lat_min)) * 111 * 111 * 0.95,
      },
      analysis_period: {
        start_date: '2023-01-01',
        end_date: '2023-12-31',
      },
      verified_at: new Date().toISOString(),
      processing_time_seconds: 45.2,
      warnings: Math.random() > 0.8 ? ['Cloud cover detected in some areas'] : undefined,
      metadata: {
        satellite_sources: ['Landsat-8', 'Sentinel-2'],
        processing_version: '2.1.0',
      },
    };
    
    return mockResponse;
  }

  async getMockHealthStatus(): Promise<HealthStatus> {
    const mockHealth: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      response_time_ms: 150,
      version: '1.0.0',
      environment: 'development',
      components: [
        {
          name: 'database',
          status: 'healthy',
          message: 'Database connection healthy',
          response_time_ms: 50,
        },
        {
          name: 'earth_engine',
          status: 'degraded',
          message: 'Earth Engine not configured (development mode)',
          response_time_ms: 0,
        },
        {
          name: 'blockchain',
          status: 'healthy',
          message: 'Blockchain configuration loaded',
          response_time_ms: 25,
        },
      ],
    };
    
    return mockHealth;
  }
}

// Create singleton instance
const apiClient = new ApiClient();

export default apiClient;

// Export individual methods for convenience
export const {
  getHealth,
  getDetailedHealth,
  submitVerification,
  checkApiStatus,
  getDashboardStats,
  getVerificationTrends,
  getRegionalAnalysis,
  getPerformanceMetrics,
  getMockVerification,
  getMockHealthStatus,
} = apiClient;
