'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import apiClient from '@/lib/api';
import { toast } from 'react-hot-toast';

interface TrendData {
  month: string;
  verifications: number;
  deforestation_rate: number;
  average_confidence: number;
}

export default function VerificationTrends() {
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeChart, setActiveChart] = useState<'verifications' | 'deforestation'>('verifications');

  useEffect(() => {
    const fetchTrendData = async () => {
      try {
        setIsLoading(true);
        const trendsData = await apiClient.getVerificationTrends(30);

        // Transform API data to match component format
        const formattedData: TrendData[] = trendsData.map((trend, index) => {
          const date = new Date(trend.date);
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

          return {
            month: `${monthNames[date.getMonth()]} ${date.getDate()}`,
            verifications: trend.count,
            deforestation_rate: trend.deforestation_rate,
            average_confidence: trend.average_confidence,
          };
        });

        setTrendData(formattedData);
      } catch (error) {
        console.error('Failed to fetch trend data:', error);
        toast.error('Failed to load verification trends');

        // Fallback to mock data if API fails
        const fallbackData: TrendData[] = [
          {
            month: 'No Data',
            verifications: 0,
            deforestation_rate: 0,
            average_confidence: 0,
          },
        ];
        setTrendData(fallbackData);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrendData();
  }, []);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{`${label} 2023`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.name}: ${
                entry.dataKey === 'deforestation_rate'
                  ? `${(entry.value * 100).toFixed(1)}%`
                  : entry.dataKey === 'average_confidence'
                  ? `${(entry.value * 100).toFixed(1)}%`
                  : entry.value
              }`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="card"
    >
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-gray-900">Verification Trends</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveChart('verifications')}
            className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${
              activeChart === 'verifications'
                ? 'bg-primary-100 text-primary-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Verifications
          </button>
          <button
            onClick={() => setActiveChart('deforestation')}
            className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${
              activeChart === 'deforestation'
                ? 'bg-primary-100 text-primary-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Deforestation
          </button>
        </div>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          {activeChart === 'verifications' ? (
            <BarChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6b7280' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6b7280' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="verifications"
                fill="#22c55e"
                radius={[4, 4, 0, 0]}
                name="Verifications"
              />
            </BarChart>
          ) : (
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6b7280' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6b7280' }}
                tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="deforestation_rate"
                stroke="#ef4444"
                strokeWidth={3}
                dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2 }}
                name="Deforestation Rate"
              />
              <Line
                type="monotone"
                dataKey="average_confidence"
                stroke="#3b82f6"
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                name="Avg Confidence"
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* Summary Stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">
            {trendData.reduce((sum, item) => sum + item.verifications, 0)}
          </div>
          <div className="text-sm text-gray-600">Total Verifications</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">
            {(
              (trendData.reduce((sum, item) => sum + item.deforestation_rate, 0) /
                trendData.length) *
              100
            ).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600">Avg Deforestation Rate</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {(
              (trendData.reduce((sum, item) => sum + item.average_confidence, 0) /
                trendData.length) *
              100
            ).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600">Avg Confidence</div>
        </div>
      </div>
    </motion.div>
  );
}
