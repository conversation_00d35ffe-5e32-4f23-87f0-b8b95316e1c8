'use client';

import { useState, useEffect } from 'react';
import {
  DocumentMagnifyingGlassIcon,
  GlobeAltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import apiClient from '@/lib/api';
import { toast } from 'react-hot-toast';

interface StatItem {
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ComponentType<any>;
  description: string;
}

export default function DashboardStats() {
  const [stats, setStats] = useState<StatItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        const dashboardData = await apiClient.getDashboardStats();

        // Format the data for display
        const formattedStats: StatItem[] = [
          {
            name: 'Total Verifications',
            value: dashboardData.total_verifications.toLocaleString(),
            change: '+12%', // TODO: Calculate actual change from trends
            changeType: 'increase',
            icon: DocumentMagnifyingGlassIcon,
            description: 'Projects verified to date',
          },
          {
            name: 'Area Verified',
            value: `${Math.round(dashboardData.total_area_verified_km2).toLocaleString()} km²`,
            change: '+8%', // TODO: Calculate actual change from trends
            changeType: 'increase',
            icon: GlobeAltIcon,
            description: 'Total forest area analyzed',
          },
          {
            name: 'Deforestation Detected',
            value: dashboardData.deforestation_detected_count.toLocaleString(),
            change: '-5%', // TODO: Calculate actual change from trends
            changeType: 'decrease',
            icon: ExclamationTriangleIcon,
            description: 'Projects with deforestation alerts',
          },
          {
            name: 'Average Confidence',
            value: `${Math.round(dashboardData.average_confidence_score * 100)}%`,
            change: '+2%', // TODO: Calculate actual change from trends
            changeType: 'increase',
            icon: CheckCircleIcon,
            description: 'Average verification confidence',
          },
        ];

        setStats(formattedStats);
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
        toast.error('Failed to load dashboard statistics');

        // Fallback to mock data if API fails
        const fallbackStats: StatItem[] = [
          {
            name: 'Total Verifications',
            value: 'N/A',
            change: '0%',
            changeType: 'neutral',
            icon: DocumentMagnifyingGlassIcon,
            description: 'Unable to load data',
          },
          {
            name: 'Area Verified',
            value: 'N/A',
            change: '0%',
            changeType: 'neutral',
            icon: GlobeAltIcon,
            description: 'Unable to load data',
          },
          {
            name: 'Deforestation Detected',
            value: 'N/A',
            change: '0%',
            changeType: 'neutral',
            icon: ExclamationTriangleIcon,
            description: 'Unable to load data',
          },
          {
            name: 'Average Confidence',
            value: 'N/A',
            change: '0%',
            changeType: 'neutral',
            icon: CheckCircleIcon,
            description: 'Unable to load data',
          },
        ];
        setStats(fallbackStats);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="card animate-pulse">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-gray-200 rounded-md"></div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      {stats.map((item, index) => (
        <motion.div
          key={item.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="card hover:shadow-md transition-shadow duration-200"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary-500 text-white">
                <item.icon className="h-5 w-5" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  {item.name}
                </dt>
                <dd className="flex items-baseline">
                  <div className="text-2xl font-semibold text-gray-900">
                    {item.value}
                  </div>
                  <div
                    className={`ml-2 flex items-baseline text-sm font-semibold ${
                      item.changeType === 'increase'
                        ? 'text-green-600'
                        : item.changeType === 'decrease'
                        ? 'text-red-600'
                        : 'text-gray-500'
                    }`}
                  >
                    {item.changeType === 'increase' && (
                      <svg
                        className="h-3 w-3 flex-shrink-0 self-center text-green-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                    {item.changeType === 'decrease' && (
                      <svg
                        className="h-3 w-3 flex-shrink-0 self-center text-red-500"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                    <span className="sr-only">
                      {item.changeType === 'increase' ? 'Increased' : 'Decreased'} by
                    </span>
                    {item.change}
                  </div>
                </dd>
                <dd className="text-xs text-gray-500 mt-1">
                  {item.description}
                </dd>
              </dl>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
