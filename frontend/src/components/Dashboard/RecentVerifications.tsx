'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import apiClient from '@/lib/api';
import { toast } from 'react-hot-toast';

interface Verification {
  id: string;
  project_name: string;
  status: 'completed' | 'processing' | 'failed';
  deforestation_detected: boolean;
  confidence_score: number;
  area_km2: number;
  verified_at: string;
}

export default function RecentVerifications() {
  const [verifications, setVerifications] = useState<Verification[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchVerifications = async () => {
      try {
        setIsLoading(true);
        const dashboardData = await apiClient.getDashboardStats();

        // Transform API data to match component format
        const formattedVerifications: Verification[] = dashboardData.recent_verifications.map((verification) => ({
          id: verification.verification_id,
          project_name: verification.project_name,
          status: verification.verification_complete ? 'completed' : 'processing',
          deforestation_detected: verification.deforestation_detected,
          confidence_score: verification.confidence_score || 0.85,
          area_km2: verification.deforested_area_km2,
          verified_at: verification.verified_at,
        }));

        setVerifications(formattedVerifications);
      } catch (error) {
        console.error('Failed to fetch recent verifications:', error);
        toast.error('Failed to load recent verifications');

        // Fallback to empty array if API fails
        setVerifications([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVerifications();
  }, []);

  const getStatusIcon = (status: string, deforestation: boolean) => {
    switch (status) {
      case 'completed':
        return deforestation ? (
          <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
        ) : (
          <CheckCircleIcon className="h-5 w-5 text-green-500" />
        );
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string, deforestation: boolean) => {
    switch (status) {
      case 'completed':
        return deforestation ? (
          <span className="badge-error">Deforestation Detected</span>
        ) : (
          <span className="badge-success">Verified Clean</span>
        );
      case 'processing':
        return <span className="badge-warning">Processing</span>;
      case 'failed':
        return <span className="badge-error">Failed</span>;
      default:
        return <span className="badge-info">Unknown</span>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="h-6 w-16 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-gray-900">Recent Verifications</h2>
        <Link
          href="/verifications"
          className="text-sm text-primary-600 hover:text-primary-500"
        >
          View all
        </Link>
      </div>

      <div className="space-y-4">
        {verifications.map((verification, index) => (
          <motion.div
            key={verification.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all duration-200"
          >
            <div className="flex items-center space-x-4">
              {getStatusIcon(verification.status, verification.deforestation_detected)}
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {verification.project_name}
                </p>
                <div className="flex items-center space-x-4 mt-1">
                  <p className="text-xs text-gray-500">
                    {verification.area_km2 > 0 ? `${verification.area_km2.toFixed(1)} km²` : 'Area pending'}
                  </p>
                  {verification.confidence_score > 0 && (
                    <p className="text-xs text-gray-500">
                      {(verification.confidence_score * 100).toFixed(0)}% confidence
                    </p>
                  )}
                  <p className="text-xs text-gray-500">
                    {formatDate(verification.verified_at)}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(verification.status, verification.deforestation_detected)}
              <button className="p-1 text-gray-400 hover:text-gray-600">
                <EyeIcon className="h-4 w-4" />
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {verifications.length === 0 && (
        <div className="text-center py-8">
          <svg
            className="h-12 w-12 text-gray-400 mx-auto mb-4"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0.621 0 1.125-.504 1.125-1.125V9.375c0-.621.504-1.125 1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"
            />
          </svg>
          <p className="text-gray-500">No verifications yet</p>
          <Link href="/verify" className="mt-2 btn-primary inline-flex">
            Start First Verification
          </Link>
        </div>
      )}
    </div>
  );
}
