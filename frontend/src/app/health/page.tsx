'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/Layout/MainLayout';
import { motion } from 'framer-motion';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  ServerIcon,
  CpuChipIcon,
  CloudIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';
import apiClient from '@/lib/api';
import type { HealthStatus, ComponentHealth } from '@/types/api';

export default function HealthPage() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchHealthStatus();
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(fetchHealthStatus, 30000); // Every 30 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const fetchHealthStatus = async () => {
    try {
      const status = await apiClient.getDetailedHealth();
      setHealthStatus(status);
      setLastUpdated(new Date());
    } catch (error) {
      // If API is not available, use mock data
      try {
        const mockStatus = await apiClient.getMockHealthStatus();
        setHealthStatus(mockStatus);
        setLastUpdated(new Date());
      } catch (mockError) {
        console.error('Failed to fetch health status:', mockError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string, size = 'h-6 w-6') => {
    const iconClass = `${size}`;
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon className={`${iconClass} text-green-500`} />;
      case 'degraded':
        return <ExclamationTriangleIcon className={`${iconClass} text-yellow-500`} />;
      case 'unhealthy':
        return <XCircleIcon className={`${iconClass} text-red-500`} />;
      default:
        return <ClockIcon className={`${iconClass} text-gray-500`} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'unhealthy':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getComponentIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'database':
        return <ServerIcon className="h-6 w-6" />;
      case 'earth_engine':
        return <CloudIcon className="h-6 w-6" />;
      case 'blockchain':
        return <LinkIcon className="h-6 w-6" />;
      case 'system_resources':
        return <CpuChipIcon className="h-6 w-6" />;
      default:
        return <ServerIcon className="h-6 w-6" />;
    }
  };

  return (
    <MainLayout>
      <div className="py-8">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Page header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">System Health</h1>
                <p className="mt-2 text-gray-600">
                  Monitor the health and performance of all system components
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-600">Auto-refresh</span>
                </label>
                <button
                  onClick={fetchHealthStatus}
                  disabled={isLoading}
                  className="btn-outline"
                >
                  <ArrowPathIcon className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
              </div>
            </div>
          </motion.div>

          {isLoading && !healthStatus ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="card animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : healthStatus ? (
            <>
              {/* Overall Status */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className={`p-6 rounded-lg border-2 mb-8 ${getStatusColor(healthStatus.status)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(healthStatus.status, 'h-8 w-8')}
                    <div>
                      <h2 className="text-2xl font-bold capitalize">
                        System {healthStatus.status}
                      </h2>
                      <p className="text-sm opacity-90">
                        Overall response time: {healthStatus.response_time_ms}ms
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">Version {healthStatus.version}</p>
                    <p className="text-xs opacity-75 capitalize">{healthStatus.environment}</p>
                  </div>
                </div>
              </motion.div>

              {/* Component Status Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {healthStatus.components.map((component: ComponentHealth, index: number) => (
                  <motion.div
                    key={component.name}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                    className="card hover:shadow-md transition-shadow duration-200"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="text-gray-600">
                          {getComponentIcon(component.name)}
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 capitalize">
                            {component.name.replace('_', ' ')}
                          </h3>
                          <p className="text-sm text-gray-600">{component.message}</p>
                        </div>
                      </div>
                      {getStatusIcon(component.status)}
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status</span>
                        <span className={`font-medium capitalize ${
                          component.status === 'healthy' ? 'text-green-600' :
                          component.status === 'degraded' ? 'text-yellow-600' :
                          'text-red-600'
                        }`}>
                          {component.status}
                        </span>
                      </div>
                      
                      {component.response_time_ms && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Response Time</span>
                          <span className="font-medium">{component.response_time_ms}ms</span>
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* System Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="card"
              >
                <h2 className="text-lg font-medium text-gray-900 mb-4">System Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Environment</h3>
                    <p className="text-lg font-semibold text-gray-900 capitalize">
                      {healthStatus.environment}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Version</h3>
                    <p className="text-lg font-semibold text-gray-900">
                      {healthStatus.version}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h3>
                    <p className="text-lg font-semibold text-gray-900">
                      {lastUpdated.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              </motion.div>
            </>
          ) : (
            <div className="text-center py-12">
              <XCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Unable to fetch system status
              </h3>
              <p className="text-gray-600 mb-4">
                There was an error connecting to the health monitoring service.
              </p>
              <button onClick={fetchHealthStatus} className="btn-primary">
                Try Again
              </button>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
