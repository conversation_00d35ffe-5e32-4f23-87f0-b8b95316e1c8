'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import MainLayout from '@/components/Layout/MainLayout';
import {
  ChartBarIcon,
  GlobeAltIcon,
  TrendingUpIcon,
  DocumentChartBarIcon,
  CalendarIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import apiClient from '@/lib/api';
import { toast } from 'react-hot-toast';

interface AnalyticsData {
  trends: any[];
  regionalAnalysis: any[];
  performanceMetrics: any;
  dashboardStats: any;
}

export default function AnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(30);
  const [activeTab, setActiveTab] = useState<'trends' | 'regional' | 'performance'>('trends');

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedTimeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      const [trends, regionalAnalysis, performanceMetrics, dashboardStats] = await Promise.all([
        apiClient.getVerificationTrends(selectedTimeRange),
        apiClient.getRegionalAnalysis(),
        apiClient.getPerformanceMetrics(),
        apiClient.getDashboardStats(),
      ]);

      setAnalyticsData({
        trends,
        regionalAnalysis,
        performanceMetrics,
        dashboardStats,
      });
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const timeRangeOptions = [
    { value: 7, label: '7 Days' },
    { value: 30, label: '30 Days' },
    { value: 90, label: '90 Days' },
    { value: 365, label: '1 Year' },
  ];

  const COLORS = ['#22c55e', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  const exportToCSV = (data: any[], filename: string) => {
    if (!data || data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => row[header]).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading analytics...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="py-8">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
                <p className="mt-2 text-gray-600">
                  Comprehensive insights into verification trends and system performance
                </p>
              </div>
              
              {/* Time Range Selector and Export */}
              <div className="flex items-center space-x-4">
                <CalendarIcon className="h-5 w-5 text-gray-400" />
                <select
                  value={selectedTimeRange}
                  onChange={(e) => setSelectedTimeRange(Number(e.target.value))}
                  className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  {timeRangeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                {/* Export Button */}
                <button
                  onClick={() => {
                    if (activeTab === 'trends' && analyticsData?.trends) {
                      exportToCSV(analyticsData.trends, 'verification-trends');
                    } else if (activeTab === 'regional' && analyticsData?.regionalAnalysis) {
                      exportToCSV(analyticsData.regionalAnalysis, 'regional-analysis');
                    } else if (activeTab === 'performance' && analyticsData?.performanceMetrics) {
                      exportToCSV([analyticsData.performanceMetrics], 'performance-metrics');
                    }
                  }}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                  Export
                </button>
              </div>
            </div>
          </motion.div>

          {/* Key Metrics Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8"
          >
            {analyticsData?.dashboardStats && [
              {
                name: 'Total Verifications',
                value: analyticsData.dashboardStats.total_verifications.toLocaleString(),
                icon: DocumentChartBarIcon,
                color: 'bg-blue-500',
              },
              {
                name: 'Area Verified',
                value: `${Math.round(analyticsData.dashboardStats.total_area_verified_km2).toLocaleString()} km²`,
                icon: GlobeAltIcon,
                color: 'bg-green-500',
              },
              {
                name: 'Deforestation Alerts',
                value: analyticsData.dashboardStats.deforestation_detected_count.toLocaleString(),
                icon: TrendingUpIcon,
                color: 'bg-red-500',
              },
              {
                name: 'Avg Confidence',
                value: `${Math.round(analyticsData.dashboardStats.average_confidence_score * 100)}%`,
                icon: ChartBarIcon,
                color: 'bg-purple-500',
              },
            ].map((metric, index) => (
              <div key={metric.name} className="card">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-lg ${metric.color}`}>
                    <metric.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">{metric.name}</p>
                    <p className="text-2xl font-semibold text-gray-900">{metric.value}</p>
                  </div>
                </div>
              </div>
            ))}
          </motion.div>

          {/* Tab Navigation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-8"
          >
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'trends', name: 'Verification Trends', icon: TrendingUpIcon },
                  { id: 'regional', name: 'Regional Analysis', icon: GlobeAltIcon },
                  { id: 'performance', name: 'Performance Metrics', icon: ChartBarIcon },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <tab.icon className="h-5 w-5 mr-2" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>
          </motion.div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'trends' && (
              <div className="space-y-8">
                {/* Verification Trends Chart */}
                <div className="card">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Verification Trends</h3>
                    <p className="text-sm text-gray-500">Daily verification counts and deforestation rates</p>
                  </div>
                  <div className="p-6">
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={analyticsData?.trends || []}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="date"
                            axisLine={false}
                            tickLine={false}
                            tick={{ fontSize: 12, fill: '#6b7280' }}
                          />
                          <YAxis
                            axisLine={false}
                            tickLine={false}
                            tick={{ fontSize: 12, fill: '#6b7280' }}
                          />
                          <Tooltip />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="count"
                            stroke="#22c55e"
                            strokeWidth={2}
                            name="Verifications"
                          />
                          <Line
                            type="monotone"
                            dataKey="deforestation_rate"
                            stroke="#ef4444"
                            strokeWidth={2}
                            name="Deforestation Rate"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'regional' && (
              <div className="space-y-8">
                {/* Regional Analysis Chart */}
                <div className="card">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Regional Distribution</h3>
                    <p className="text-sm text-gray-500">Verification activity by geographic region</p>
                  </div>
                  <div className="p-6">
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={analyticsData?.regionalAnalysis || []}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="region"
                            axisLine={false}
                            tickLine={false}
                            tick={{ fontSize: 12, fill: '#6b7280' }}
                          />
                          <YAxis
                            axisLine={false}
                            tickLine={false}
                            tick={{ fontSize: 12, fill: '#6b7280' }}
                          />
                          <Tooltip />
                          <Bar dataKey="total_verifications" fill="#22c55e" name="Total Verifications" />
                          <Bar dataKey="deforestation_detected" fill="#ef4444" name="Deforestation Detected" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'performance' && (
              <div className="space-y-8">
                {/* Performance Metrics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="card">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <h3 className="text-lg font-medium text-gray-900">System Performance</h3>
                    </div>
                    <div className="p-6">
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Average Processing Time</span>
                          <span className="text-sm font-medium">
                            {analyticsData?.performanceMetrics?.average_processing_time?.toFixed(1)}s
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Success Rate</span>
                          <span className="text-sm font-medium">
                            {Math.round((analyticsData?.performanceMetrics?.success_rate || 0) * 100)}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Total Projects</span>
                          <span className="text-sm font-medium">
                            {analyticsData?.performanceMetrics?.total_projects?.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Active Projects</span>
                          <span className="text-sm font-medium">
                            {analyticsData?.performanceMetrics?.active_projects?.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </MainLayout>
  );
}
