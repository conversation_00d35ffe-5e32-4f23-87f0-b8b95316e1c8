'use client';

import { useState } from 'react';
import MainLayout from '@/components/Layout/MainLayout';
import { motion } from 'framer-motion';
import {
  BellIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  CogIcon,
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: false,
      deforestation_alerts: true,
      verification_complete: true,
      system_updates: false,
    },
    api: {
      rate_limit: 100,
      timeout: 30,
      retry_attempts: 3,
    },
    verification: {
      default_confidence_threshold: 0.8,
      auto_retry_failed: true,
      save_intermediate_results: false,
    },
    display: {
      theme: 'light',
      language: 'en',
      timezone: 'UTC',
    },
  });

  const handleSave = () => {
    // Simulate saving settings
    toast.success('Settings saved successfully!');
  };

  const handleReset = () => {
    // Reset to defaults
    toast.success('Settings reset to defaults');
  };

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  return (
    <MainLayout>
      <div className="py-8">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
          {/* Page header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="mt-2 text-gray-600">
              Configure your CarbonLedger experience and system preferences
            </p>
          </motion.div>

          <div className="space-y-8">
            {/* Notifications */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="card"
            >
              <div className="flex items-center mb-6">
                <BellIcon className="h-6 w-6 text-gray-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-900">Notifications</h2>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Email Notifications</label>
                    <p className="text-sm text-gray-500">Receive updates via email</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.notifications.email}
                    onChange={(e) => updateSetting('notifications', 'email', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Push Notifications</label>
                    <p className="text-sm text-gray-500">Browser push notifications</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.notifications.push}
                    onChange={(e) => updateSetting('notifications', 'push', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Deforestation Alerts</label>
                    <p className="text-sm text-gray-500">Immediate alerts when deforestation is detected</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.notifications.deforestation_alerts}
                    onChange={(e) => updateSetting('notifications', 'deforestation_alerts', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>
              </div>
            </motion.div>

            {/* API Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="card"
            >
              <div className="flex items-center mb-6">
                <CogIcon className="h-6 w-6 text-gray-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-900">API Configuration</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">Rate Limit (requests/minute)</label>
                  <input
                    type="number"
                    value={settings.api.rate_limit}
                    onChange={(e) => updateSetting('api', 'rate_limit', parseInt(e.target.value))}
                    className="input"
                    min="1"
                    max="1000"
                  />
                </div>
                
                <div>
                  <label className="label">Timeout (seconds)</label>
                  <input
                    type="number"
                    value={settings.api.timeout}
                    onChange={(e) => updateSetting('api', 'timeout', parseInt(e.target.value))}
                    className="input"
                    min="5"
                    max="300"
                  />
                </div>
              </div>
            </motion.div>

            {/* Verification Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="card"
            >
              <div className="flex items-center mb-6">
                <ShieldCheckIcon className="h-6 w-6 text-gray-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-900">Verification Settings</h2>
              </div>
              
              <div className="space-y-6">
                <div>
                  <label className="label">Default Confidence Threshold</label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="range"
                      min="0.5"
                      max="1"
                      step="0.05"
                      value={settings.verification.default_confidence_threshold}
                      onChange={(e) => updateSetting('verification', 'default_confidence_threshold', parseFloat(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-sm font-medium text-gray-900 w-12">
                      {(settings.verification.default_confidence_threshold * 100).toFixed(0)}%
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Minimum confidence score required for verification acceptance
                  </p>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-900">Auto-retry Failed Verifications</label>
                    <p className="text-sm text-gray-500">Automatically retry failed verification attempts</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.verification.auto_retry_failed}
                    onChange={(e) => updateSetting('verification', 'auto_retry_failed', e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>
              </div>
            </motion.div>

            {/* Display Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="card"
            >
              <div className="flex items-center mb-6">
                <GlobeAltIcon className="h-6 w-6 text-gray-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-900">Display & Localization</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="label">Theme</label>
                  <select
                    value={settings.display.theme}
                    onChange={(e) => updateSetting('display', 'theme', e.target.value)}
                    className="input"
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="auto">Auto</option>
                  </select>
                </div>
                
                <div>
                  <label className="label">Language</label>
                  <select
                    value={settings.display.language}
                    onChange={(e) => updateSetting('display', 'language', e.target.value)}
                    className="input"
                  >
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="pt">Português</option>
                    <option value="fr">Français</option>
                  </select>
                </div>
                
                <div>
                  <label className="label">Timezone</label>
                  <select
                    value={settings.display.timezone}
                    onChange={(e) => updateSetting('display', 'timezone', e.target.value)}
                    className="input"
                  >
                    <option value="UTC">UTC</option>
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                    <option value="Europe/London">London</option>
                    <option value="Asia/Tokyo">Tokyo</option>
                  </select>
                </div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="flex justify-end space-x-4"
            >
              <button onClick={handleReset} className="btn-outline">
                Reset to Defaults
              </button>
              <button onClick={handleSave} className="btn-primary">
                Save Settings
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
