{"version": 3, "sources": ["../../src/export/worker.ts"], "names": ["exportPage", "process", "env", "NEXT_IS_EXPORT_WORKER", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "exportPageImpl", "input", "fileWriter", "dir", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeFonts", "optimizeCss", "disableOptimizedLoading", "debugOutput", "isrMemoryCacheSize", "fetchCache", "fetchCacheKeyPrefix", "incremental<PERSON>ache<PERSON>andlerPath", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "enabledDirectories", "__NEXT_EXPERIMENTAL_REACT", "page", "_isAppDir", "isAppDir", "_isDynamicError", "isDynamicError", "query", "originalQuery", "req", "pathname", "normalizeAppPath", "isDynamic", "isDynamicRoute", "outDir", "join", "params", "filePath", "normalizePagePath", "ampPath", "renderAmpPath", "updatedPath", "__nextSsgPath", "locale", "__next<PERSON><PERSON><PERSON>", "renderOpts", "localePathResult", "normalizeLocalePath", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "normalizedPage", "getParams", "res", "createRequestResponseMocks", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "addRequestMeta", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "sep", "htmlFilename", "pageExt", "extname", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "dirname", "htmlFilepath", "fs", "mkdir", "recursive", "incrementalCache", "createIncrementalCache", "experimental", "ppr", "flushToDisk", "hasNextSupport", "undefined", "isAppRouteRoute", "exportAppRoute", "components", "loadComponents", "isAppPath", "fontManifest", "requireFontManifest", "supportsDynamicHTML", "originalPathname", "isRevalidate", "exportAppPage", "exportPages", "err", "isMissingPostponeDataError", "console", "error", "isError", "stack", "setHttpClientAndAgentOptions", "httpAgentOptions", "files", "baseFileWriter", "type", "content", "encodingOptions", "writeFile", "push", "exportPageSpan", "trace", "parentSpanId", "start", "Date", "now", "result", "traceAsyncFn", "duration", "ampValidations", "revalidate", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "on", "isPostpone", "isDynamicUsageError"], "mappings": ";;;;+BAyUA;;;eAA8BA;;;QAhUvB;sBAIqC;iEAC7B;gCACgB;2BACA;mCACG;yBACE;qCACA;uBACd;mCACuB;gEACzB;6BACW;0BACE;6BAEU;iCACX;wBACD;0BACA;yBACD;uBACF;2BACF;wCACa;4BACZ;wCACgB;qCACP;;;;;;AAzBpCC,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AA2BpC,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,eAAeC,eACbC,KAAsB,EACtBC,UAAsB;IAEtB,MAAM,EACJC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,aAAa,EACbC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,kBAAkB,EAClBC,UAAU,EACVC,mBAAmB,EACnBC,2BAA2B,EAC3BC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACbC,kBAAkB,EACnB,GAAGrB;IAEJ,IAAIkB,yBAAyB;QAC3B3B,QAAQC,GAAG,CAAC8B,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,oGAAoG;IACpG,gDAAgD;IAChD,yCAAyC;IAEzC,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAGzB;IAEJ,IAAI;YAwEoB0B;QAvEtB,IAAIF,QAAQ;YAAE,GAAGC,aAAa;QAAC;QAC/B,MAAME,WAAWC,IAAAA,0BAAgB,EAACT;QAClC,MAAMU,YAAYC,IAAAA,yBAAc,EAACX;QACjC,MAAMY,SAASV,WAAWW,IAAAA,UAAI,EAAC/B,SAAS,gBAAgBL,MAAMmC,MAAM;QAEpE,IAAIE;QAEJ,MAAMC,WAAWC,IAAAA,oCAAiB,EAACpC;QACnC,MAAMqC,UAAU,CAAC,EAAEF,SAAS,IAAI,CAAC;QACjC,IAAIG,gBAAgBD;QAEpB,IAAIE,cAAcd,MAAMe,aAAa,IAAIxC;QACzC,OAAOyB,MAAMe,aAAa;QAE1B,IAAIC,SAAShB,MAAMiB,YAAY,IAAI7C,MAAM8C,UAAU,CAACF,MAAM;QAC1D,OAAOhB,MAAMiB,YAAY;QAEzB,IAAI7C,MAAM8C,UAAU,CAACF,MAAM,EAAE;YAC3B,MAAMG,mBAAmBC,IAAAA,wCAAmB,EAC1C7C,MACAH,MAAM8C,UAAU,CAACG,OAAO;YAG1B,IAAIF,iBAAiBG,cAAc,EAAE;gBACnCR,cAAcK,iBAAiBhB,QAAQ;gBACvCa,SAASG,iBAAiBG,cAAc;gBAExC,IAAIN,WAAW5C,MAAM8C,UAAU,CAACK,aAAa,EAAE;oBAC7CV,gBAAgB,CAAC,EAAEF,IAAAA,oCAAiB,EAACG,aAAa,IAAI,CAAC;gBACzD;YACF;QACF;QAEA,gEAAgE;QAChE,0DAA0D;QAC1D,MAAMU,qBAAqBC,OAAOC,IAAI,CAACzB,eAAe0B,MAAM,GAAG;QAE/D,iDAAiD;QACjD,MAAM,EAAExB,UAAUyB,gBAAgB,EAAE,GAAGR,IAAAA,wCAAmB,EACxD7C,MACAH,MAAM8C,UAAU,CAACG,OAAO;QAG1B,IAAIhB,aAAaV,SAASiC,kBAAkB;YAC1C,MAAMC,iBAAiBhC,WAAWO,IAAAA,0BAAgB,EAACT,QAAQA;YAE3Dc,SAASqB,IAAAA,oBAAS,EAACD,gBAAgBf;YACnC,IAAIL,QAAQ;gBACVT,QAAQ;oBACN,GAAGA,KAAK;oBACR,GAAGS,MAAM;gBACX;YACF;QACF;QAEA,MAAM,EAAEP,GAAG,EAAE6B,GAAG,EAAE,GAAGC,IAAAA,uCAA0B,EAAC;YAAEC,KAAKnB;QAAY;QAEnE,6DAA6D;QAC7D,KAAK,MAAMoB,cAAc;YAAC;YAAK;SAAI,CAAE;YACnC,IACE;gBACE,CAAC,CAAC,EAAEA,WAAW,CAAC;gBAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;gBACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;aAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMtB,eAAe,CAAC,CAAC,EAAEE,OAAO,EAAEoB,EAAE,CAAC,KAAKtB,cACxD;gBACAiB,IAAIG,UAAU,GAAGA;YACnB;QACF;QAEA,+DAA+D;QAC/D,IAAI1C,iBAAiB,GAACU,WAAAA,IAAI+B,GAAG,qBAAP/B,SAASmC,QAAQ,CAAC,OAAM;YAC5CnC,IAAI+B,GAAG,IAAI;QACb;QAEA,IACEjB,UACArC,eACAP,MAAM8C,UAAU,CAACoB,aAAa,IAC9BlE,MAAM8C,UAAU,CAACoB,aAAa,CAACH,IAAI,CACjC,CAACI;gBACgCA;mBAA/BA,GAAGhB,aAAa,KAAKP,YAAUuB,cAAAA,GAAGlB,OAAO,qBAAVkB,YAAYC,QAAQ,CAACxB,UAAU;YAElE;YACAyB,IAAAA,2BAAc,EAACvC,KAAK,kBAAkB;QACxC;QAEApC,UAAU4E,SAAS,CAAC;YAClB9D;YACA+D,qBAAqBvE,MAAM8C,UAAU,CAAC0B,aAAa;QACrD;QAEA,MAAMC,kBAAkB,CAACT,IACvBvD,aAAa,CAAC,EAAEuD,EAAE,EAAEU,SAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAEV,EAAE,KAAK,CAAC;QAEnD,IAAIW,eAAeF,gBAAgBnC;QAEnC,gFAAgF;QAChF,wBAAwB;QACxB,MAAMsC,UAAU3C,aAAaR,WAAW,KAAKoD,IAAAA,aAAO,EAACtD;QACrD,MAAMuD,UAAU7C,aAAaR,WAAW,KAAKoD,IAAAA,aAAO,EAAC1E;QAErD,6CAA6C;QAC7C,IAAIA,SAAS,aAAa;YACxBwE,eAAexE;QACjB,OAEK,IAAIyE,YAAYE,WAAWA,YAAY,IAAI;YAC9C,MAAMC,iBAAiB;gBAAC;gBAAQ;aAAO,CAAChB,IAAI,CAC1C,CAACC,IAAMA,MAAM7D,QAAQ6D,MAAM7D,OAAO;YAEpC,mFAAmF;YACnF,8CAA8C;YAC9C,MAAM6E,gBAAgB,CAACD,kBAAkB5E,KAAK8D,QAAQ,CAAC;YACvDU,eAAeK,gBAAgBP,gBAAgBtE,QAAQA;QACzD,OAAO,IAAIA,SAAS,KAAK;YACvB,+CAA+C;YAC/CwE,eAAe;QACjB;QAEA,MAAMM,UAAU7C,IAAAA,UAAI,EAACD,QAAQ+C,IAAAA,aAAO,EAACP;QACrC,IAAIQ,eAAe/C,IAAAA,UAAI,EAACD,QAAQwC;QAEhC,MAAMS,iBAAE,CAACC,KAAK,CAACJ,SAAS;YAAEK,WAAW;QAAK;QAE1C,mEAAmE;QACnE,gCAAgC;QAChC,MAAMC,mBACJ9D,YAAYV,aACRyE,IAAAA,8CAAsB,EAAC;YACrBvE;YACAH;YACAE;YACAX;YACAH;YACAmB;YACA,kCAAkC;YAClCoE,cAAc;gBAAEC,KAAK;YAAM;YAC3B,6DAA6D;YAC7D,+BAA+B;YAC/BC,aAAa,CAACC,sBAAc;QAC9B,KACAC;QAEN,qBAAqB;QACrB,IAAIpE,YAAYqE,IAAAA,gCAAe,EAACvE,OAAO;YACrC,OAAO,MAAMwE,IAAAA,wBAAc,EACzBjE,KACA6B,KACAtB,QACAd,MACAgE,kBACAlF,SACA8E,cACAlF;QAEJ;QAEA,MAAM+F,aAAa,MAAMC,IAAAA,8BAAc,EAAC;YACtC5F;YACAkB;YACA2E,WAAWzE;QACb;QAEA,MAAMqB,aAA+B;YACnC,GAAGkD,UAAU;YACb,GAAGhG,MAAM8C,UAAU;YACnBN,SAASC;YACTJ;YACA3B;YACAC;YACAC;YACAuF,cAAczF,gBAAgB0F,IAAAA,4BAAmB,EAAC/F,WAAW;YAC7DuC;YACAyD,qBAAqB;YACrBC,kBAAkB/E;QACpB;QAEA,IAAIqE,sBAAc,EAAE;YAClB9C,WAAWyD,YAAY,GAAG;QAC5B;QAEA,mBAAmB;QACnB,IAAI9E,UAAU;YACZ,qEAAqE;YACrE,cAAc;YACdqB,WAAWyC,gBAAgB,GAAGA;YAE9B,OAAO,MAAMiB,IAAAA,sBAAa,EACxB1E,KACA6B,KACApC,MACApB,MACA4B,UACAH,OACAkB,YACAqC,cACAtE,aACAc,gBACA1B;QAEJ;QAEA,OAAO,MAAMwG,IAAAA,kBAAW,EACtB3E,KACA6B,KACAxD,MACAoB,MACAK,OACAuD,cACAR,cACAnC,SACA/B,YACA0B,QACAhB,kBACAb,cACAC,aACA0B,WACAmB,oBACAN,YACAkD,YACA/F;IAEJ,EAAE,OAAOyG,KAAK;QACZ,sFAAsF;QACtF,IAAI,CAACC,IAAAA,kDAA0B,EAACD,MAAM;YACpCE,QAAQC,KAAK,CACX,CAAC,oCAAoC,EAAE1G,KAAK,gEAAgE,CAAC,GAC1G2G,CAAAA,IAAAA,gBAAO,EAACJ,QAAQA,IAAIK,KAAK,GAAGL,IAAIK,KAAK,GAAGL,GAAE;QAEjD;QAEA,OAAO;YAAEG,OAAO;QAAK;IACvB;AACF;AAEe,eAAevH,WAC5BU,KAAsB;IAEtB,4BAA4B;IAC5BgH,IAAAA,+CAA4B,EAAC;QAC3BC,kBAAkBjH,MAAMiH,gBAAgB;IAC1C;IAEA,MAAMC,QAA4B,EAAE;IACpC,MAAMC,iBAA6B,OACjCC,MACAjH,MACAkH,SACAC,kBAAkB,OAAO;QAEzB,MAAMlC,iBAAE,CAACC,KAAK,CAACH,IAAAA,aAAO,EAAC/E,OAAO;YAAEmF,WAAW;QAAK;QAChD,MAAMF,iBAAE,CAACmC,SAAS,CAACpH,MAAMkH,SAASC;QAClCJ,MAAMM,IAAI,CAAC;YAAEJ;YAAMjH;QAAK;IAC1B;IAEA,MAAMsH,iBAAiBC,IAAAA,YAAK,EAAC,sBAAsB1H,MAAM2H,YAAY;IAErE,MAAMC,QAAQC,KAAKC,GAAG;IAEtB,mBAAmB;IACnB,MAAMC,SAAS,MAAMN,eAAeO,YAAY,CAAC;QAC/C,OAAO,MAAMjI,eAAeC,OAAOmH;IACrC;IAEA,kDAAkD;IAClD,IAAI,CAACY,QAAQ;IAEb,iDAAiD;IACjD,IAAI,WAAWA,QAAQ;QACrB,OAAO;YAAElB,OAAOkB,OAAOlB,KAAK;YAAEoB,UAAUJ,KAAKC,GAAG,KAAKF;YAAOV,OAAO,EAAE;QAAC;IACxE;IAEA,sCAAsC;IACtC,OAAO;QACLe,UAAUJ,KAAKC,GAAG,KAAKF;QACvBV;QACAgB,gBAAgBH,OAAOG,cAAc;QACrCC,YAAYJ,OAAOI,UAAU;QAC7BC,UAAUL,OAAOK,QAAQ;QACzBC,aAAaN,OAAOM,WAAW;QAC/BC,iBAAiBP,OAAOO,eAAe;QACvCC,cAAcR,OAAOQ,YAAY;IACnC;AACF;AAEAhJ,QAAQiJ,EAAE,CAAC,sBAAsB,CAAC9B;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAI+B,IAAAA,sBAAU,EAAC/B,MAAM;QACnB;IACF;IAEA,oCAAoC;IACpC,IAAIgC,IAAAA,wCAAmB,EAAChC,MAAM;QAC5B;IACF;IAEAE,QAAQC,KAAK,CAACH;AAChB;AAEAnH,QAAQiJ,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC/D"}