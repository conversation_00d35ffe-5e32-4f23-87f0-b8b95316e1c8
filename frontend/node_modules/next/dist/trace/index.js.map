{"version": 3, "sources": ["../../src/trace/index.ts"], "names": ["trace", "exportTraceState", "flushAllTraces", "getTraceEvents", "initializeTraceState", "recordTraceEvents", "Span", "setGlobal", "SpanStatus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAcEA,KAAK;eAALA,YAAK;;IACLC,gBAAgB;eAAhBA,uBAAgB;;IAChBC,cAAc;eAAdA,qBAAc;;IACdC,cAAc;eAAdA,qBAAc;;IACdC,oBAAoB;eAApBA,2BAAoB;;IACpBC,iBAAiB;eAAjBA,wBAAiB;;IACjBC,IAAI;eAAJA,WAAI;;IACJC,SAAS;eAATA,iBAAS;;IACTC,UAAU;eAAVA,iBAAU;;;uBAbL;wBACmB"}