import type { NextConfigComplete } from '../../../server/config-shared';
import type { MiddlewareMatcher } from '../../analysis/get-page-static-info';
import { webpack } from 'next/dist/compiled/webpack/webpack';
export interface DefineEnvPluginOptions {
    isTurbopack: boolean;
    allowedRevalidateHeaderKeys: string[] | undefined;
    clientRouterFilters?: {
        staticFilter: ReturnType<import('../../../shared/lib/bloom-filter').BloomFilter['export']>;
        dynamicFilter: ReturnType<import('../../../shared/lib/bloom-filter').BloomFilter['export']>;
    };
    config: NextConfigComplete;
    dev: boolean;
    distDir: string;
    fetchCacheKeyPrefix: string | undefined;
    hasRewrites: boolean;
    isClient: boolean;
    isEdgeServer: boolean;
    isNodeOrEdgeCompilation: boolean;
    isNodeServer: boolean;
    middlewareMatchers: MiddlewareMatcher[] | undefined;
    previewModeId: string | undefined;
}
export declare function getDefineEnv({ isTurbopack, allowedRevalidateHeaderKeys, clientRouterFilters, config, dev, distDir, fetchCacheKeyPrefix, hasRewrites, isClient, isEdgeServer, isNodeOrEdgeCompilation, isNodeServer, middlewareMatchers, previewModeId, }: DefineEnvPluginOptions): {
    'process.env.__NEXT_EXPERIMENTAL_REACT'?: string | undefined;
    'global.GENTLY'?: string | undefined;
    'process.env.__NEXT_TRAILING_SLASH': string;
    'process.env.__NEXT_BUILD_INDICATOR': string;
    'process.env.__NEXT_BUILD_INDICATOR_POSITION': string;
    'process.env.__NEXT_STRICT_MODE': string;
    'process.env.__NEXT_STRICT_MODE_APP': string;
    'process.env.__NEXT_OPTIMIZE_FONTS': string;
    'process.env.__NEXT_OPTIMIZE_CSS': string;
    'process.env.__NEXT_SCRIPT_WORKERS': string;
    'process.env.__NEXT_SCROLL_RESTORATION': string;
    'process.env.__NEXT_IMAGE_OPTS': string;
    'process.env.__NEXT_ROUTER_BASEPATH': string;
    'process.env.__NEXT_STRICT_NEXT_HEAD': string;
    'process.env.__NEXT_HAS_REWRITES': string;
    'process.env.__NEXT_CONFIG_OUTPUT': string;
    'process.env.__NEXT_I18N_SUPPORT': string;
    'process.env.__NEXT_I18N_DOMAINS': string;
    'process.env.__NEXT_ANALYTICS_ID': string;
    'process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE': string;
    'process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE': string;
    'process.env.__NEXT_MANUAL_TRAILING_SLASH': string;
    'process.env.__NEXT_HAS_WEB_VITALS_ATTRIBUTION': string;
    'process.env.__NEXT_WEB_VITALS_ATTRIBUTION': string;
    'process.env.__NEXT_ASSET_PREFIX': string;
    'process.env.__NEXT_DIST_DIR'?: string | undefined;
    'process.turbopack': string;
    'process.env.TURBOPACK': string;
    'process.env.NODE_ENV': string;
    'process.env.NEXT_RUNTIME': string;
    'process.env.NEXT_MINIMAL': string;
    'process.env.__NEXT_WINDOW_HISTORY_SUPPORT': string;
    'process.env.__NEXT_PPR': string;
    'process.env.__NEXT_ACTIONS_DEPLOYMENT_ID': string;
    'process.env.NEXT_DEPLOYMENT_ID': string;
    'process.env.__NEXT_FETCH_CACHE_KEY_PREFIX': string;
    'process.env.__NEXT_PREVIEW_MODE_ID': string;
    'process.env.__NEXT_ALLOWED_REVALIDATE_HEADERS': string;
    'process.env.__NEXT_MIDDLEWARE_MATCHERS': string;
    'process.env.__NEXT_MANUAL_CLIENT_BASE_PATH': string;
    'process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED': string;
    'process.env.__NEXT_CLIENT_ROUTER_S_FILTER': string;
    'process.env.__NEXT_CLIENT_ROUTER_D_FILTER': string;
    'process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE': string;
    'process.env.__NEXT_MIDDLEWARE_PREFETCH': string;
    'process.env.__NEXT_CROSS_ORIGIN': string;
    'process.browser': string;
    'process.env.__NEXT_TEST_MODE': string;
    EdgeRuntime?: string | undefined;
    __NEXT_DEFINE_ENV: string;
};
export declare function getDefineEnvPlugin(options: DefineEnvPluginOptions): webpack.DefinePlugin;
