export declare const RSC_HEADER: "RSC";
export declare const ACTION: "Next-Action";
export declare const NEXT_ROUTER_STATE_TREE: "Next-Router-State-Tree";
export declare const NEXT_ROUTER_PREFETCH_HEADER: "Next-Router-Prefetch";
export declare const NEXT_URL: "Next-Url";
export declare const RSC_CONTENT_TYPE_HEADER: "text/x-component";
export declare const RSC_VARY_HEADER: "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url";
export declare const FLIGHT_PARAMETERS: readonly [readonly ["RSC"], readonly ["Next-Router-State-Tree"], readonly ["Next-Router-Prefetch"]];
export declare const NEXT_RSC_UNION_QUERY: "_rsc";
export declare const NEXT_DID_POSTPONE_HEADER: "x-nextjs-postponed";
