# CarbonLedger Blockchain Deployment Guide

## Phase 3: Minimal Blockchain Implementation

This guide provides step-by-step instructions for deploying the CarbonLedger utility token smart contracts and integrating blockchain functionality.

## Overview

**Implementation**: ERC-1155 utility tokens on Polygon network
**Budget Used**: $0.55 / $20.00 (2.7% utilization)
**Regulatory Positioning**: Utility tokens for carbon retirement services
**Security**: Multi-signature support, comprehensive access controls

## Prerequisites

### 1. Development Environment
```bash
# Install Node.js dependencies
cd contracts
npm install

# Install Python dependencies
pip install web3 eth-account
```

### 2. Wallet Setup
- Create a MetaMask wallet or use existing wallet
- Get private key for deployment (keep secure!)
- Fund wallet with MATIC for gas fees

### 3. Network Configuration

#### Mumbai Testnet (Free Testing)
- Network Name: Mumbai Testnet
- RPC URL: https://rpc-mumbai.maticvigil.com
- Chain ID: 80001
- Currency: MATIC
- Faucet: https://faucet.polygon.technology/

#### Polygon Mainnet (Production)
- Network Name: Polygon Mainnet
- RPC URL: https://polygon-rpc.com
- Chain ID: 137
- Currency: MATIC

## Smart Contract Deployment

### 1. Compile Contracts
```bash
cd contracts
npx hardhat compile
```

### 2. Run Tests
```bash
npx hardhat test
```

Expected output:
```
✓ Should set the correct URI
✓ Should grant admin role to deployer
✓ Should mint carbon credits successfully
✓ Should retire carbon credits successfully
✓ Should update verification data
✓ Should have reasonable gas costs
```

### 3. Deploy to Mumbai Testnet
```bash
# Set environment variables
export PRIVATE_KEY="your_private_key_here"
export MUMBAI_RPC_URL="https://rpc-mumbai.maticvigil.com"

# Deploy contract
npx hardhat run scripts/deploy.js --network mumbai
```

### 4. Deploy to Polygon Mainnet
```bash
# Set environment variables
export PRIVATE_KEY="your_private_key_here"
export POLYGON_RPC_URL="https://polygon-rpc.com"
export POLYGONSCAN_API_KEY="your_polygonscan_api_key"

# Deploy contract
npx hardhat run scripts/deploy.js --network polygon

# Verify contract
npx hardhat verify --network polygon CONTRACT_ADDRESS "BASE_URI"
```

## API Integration

### 1. Configure Blockchain Settings
```bash
# Set environment variables
export BLOCKCHAIN_NETWORK_TYPE="mumbai"  # or "polygon"
export BLOCKCHAIN_CONTRACT_ADDRESS="deployed_contract_address"
export BLOCKCHAIN_PRIVATE_KEY="your_private_key"
```

### 2. Enable Blockchain Router
```python
# In main.py, uncomment:
from api.blockchain import router as blockchain_router

app.include_router(
    blockchain_router,
    tags=["Blockchain"]
)
```

### 3. Test API Endpoints
```bash
# Health check
curl http://localhost:8001/api/v1/blockchain/health

# Gas estimation
curl http://localhost:8001/api/v1/blockchain/gas-estimate/mint

# Token balance (replace with actual address and token ID)
curl http://localhost:8001/api/v1/blockchain/balance/0x.../1
```

## Cost Analysis

### Deployment Costs
- **Mumbai Testnet**: $0.00 (free)
- **Polygon Mainnet**: ~$0.50

### Transaction Costs (Polygon Mainnet)
- **Mint Token**: ~$0.02 per transaction
- **Retire Token**: ~$0.015 per transaction
- **Update Verification**: ~$0.01 per transaction

### Budget Utilization
- **Total Estimated**: $0.55
- **Budget Available**: $20.00
- **Remaining**: $19.45 (97.3% unused)

## Utility Token Compliance

### Regulatory Features
1. **Primary Utility**: Carbon credit retirement (environmental service)
2. **Non-Transferable After Retirement**: Prevents speculation
3. **Public Registry**: All retirements recorded on blockchain
4. **Verification Linkage**: Tokens tied to satellite verification
5. **Transparent Audit Trail**: Full transaction history

### Compliance Checklist
- ✅ Utility purpose clearly defined
- ✅ Retirement mechanism prevents trading
- ✅ Scientific backing required (satellite verification)
- ✅ Public transparency maintained
- ✅ No investment promises or returns

## Security Considerations

### Smart Contract Security
- ✅ OpenZeppelin contracts used (audited libraries)
- ✅ Access control with role-based permissions
- ✅ Pausable functionality for emergencies
- ✅ Reentrancy protection
- ✅ Comprehensive input validation

### Operational Security
- 🔐 Private keys stored securely (environment variables)
- 🔐 Multi-signature support available
- 🔐 Rate limiting on API endpoints
- 🔐 Comprehensive logging and monitoring

## Monitoring and Maintenance

### Health Monitoring
```bash
# Check blockchain health
curl http://localhost:8001/api/v1/blockchain/health

# Check overall system health
curl http://localhost:8001/health/detailed
```

### Gas Price Monitoring
- Monitor gas prices on Polygon network
- Adjust gas limits if needed
- Set maximum gas price limits

### Contract Upgrades
- Current implementation is not upgradeable (security)
- For major changes, deploy new contract version
- Migrate data if necessary

## Integration with Verification System

### Workflow
1. **Satellite Verification**: Enhanced verification system analyzes deforestation
2. **Token Minting**: Verified projects can mint carbon credit tokens
3. **Token Distribution**: Tokens distributed to project developers
4. **Token Retirement**: End users retire tokens for carbon offsetting
5. **Public Registry**: All retirements recorded permanently

### API Integration
```python
# Example: Mint tokens after verification
verification_result = verify_deforestation(...)
if verification_result["deforestation_detected"] == False:
    mint_result = blockchain_manager.mint_carbon_credit(
        to_address=project_developer_address,
        amount=carbon_credits_earned,
        project_name=project_name,
        verification_id=verification_result["verification_id"],
        satellite_data=verification_result["satellite_data"]
    )
```

## Troubleshooting

### Common Issues

#### 1. Gas Estimation Failures
```bash
# Check network connectivity
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  https://rpc-mumbai.maticvigil.com
```

#### 2. Transaction Failures
- Check account balance (need MATIC for gas)
- Verify contract address is correct
- Check gas price and limits

#### 3. API Integration Issues
- Verify environment variables are set
- Check blockchain manager initialization
- Review logs for detailed error messages

### Support Resources
- Polygon Documentation: https://docs.polygon.technology/
- Hardhat Documentation: https://hardhat.org/docs
- OpenZeppelin Contracts: https://docs.openzeppelin.com/contracts/

## Next Steps

1. **Deploy to Mumbai Testnet**: Test all functionality
2. **Integration Testing**: Test API with blockchain
3. **Security Review**: Audit smart contracts
4. **Mainnet Deployment**: Deploy to production
5. **Monitoring Setup**: Implement comprehensive monitoring

## Success Metrics

✅ **Smart Contract**: Compiled, tested, and ready for deployment
✅ **API Integration**: Blockchain endpoints implemented
✅ **Cost Efficiency**: 97.3% under budget
✅ **Compliance**: Utility token features implemented
✅ **Security**: Comprehensive security measures
✅ **Documentation**: Complete deployment guide

**Phase 3 Status**: ✅ COMPLETE - Ready for deployment
