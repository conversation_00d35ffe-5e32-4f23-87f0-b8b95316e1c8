'use client';

import { motion } from 'framer-motion';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  DocumentArrowDownIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import type { VerificationResponse } from '@/types/api';

interface VerificationResultsProps {
  result: VerificationResponse;
  onNewVerification: () => void;
}

export default function VerificationResults({
  result,
  onNewVerification,
}: VerificationResultsProps) {
  const getStatusIcon = () => {
    if (!result.deforestation_detected) {
      return <CheckCircleIcon className="h-8 w-8 text-green-500" />;
    } else {
      return <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    if (!result.deforestation_detected) {
      return 'text-green-600 bg-green-50 border-green-200';
    } else {
      return 'text-red-600 bg-red-50 border-red-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Main Result Card */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Verification Results
          </h2>
          <button
            onClick={onNewVerification}
            className="btn-outline text-sm"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            New Verification
          </button>
        </div>

        {/* Status Banner */}
        <div className={`p-4 rounded-lg border ${getStatusColor()} mb-6`}>
          <div className="flex items-center">
            {getStatusIcon()}
            <div className="ml-4">
              <h3 className="text-lg font-medium">
                {result.deforestation_detected
                  ? 'Deforestation Detected'
                  : 'No Deforestation Detected'}
              </h3>
              <p className="text-sm opacity-90">
                {result.deforestation_detected
                  ? `${result.deforested_area_km2.toFixed(2)} km² of deforestation found`
                  : 'Forest area appears to be preserved'}
              </p>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {result.confidence_score ? formatPercentage(result.confidence_score) : 'N/A'}
            </div>
            <div className="text-sm text-gray-600">Confidence Score</div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {result.area_checked.total_area_km2.toFixed(1)} km²
            </div>
            <div className="text-sm text-gray-600">Area Analyzed</div>
          </div>
        </div>

        {/* Project Information */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Project Details</h4>
          <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Project Name</dt>
              <dd className="mt-1 text-sm text-gray-900">{result.project_name}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Verification ID</dt>
              <dd className="mt-1 text-sm text-gray-900 font-mono">{result.verification_id}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Verified At</dt>
              <dd className="mt-1 text-sm text-gray-900">{formatDate(result.verified_at)}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Processing Time</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {result.processing_time_seconds.toFixed(1)} seconds
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Detailed Analysis */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Detailed Analysis</h3>
        
        {/* Quality Metrics */}
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">Data Quality Score</span>
              <span className="font-medium">
                {result.data_quality_score ? formatPercentage(result.data_quality_score) : 'N/A'}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{
                  width: result.data_quality_score
                    ? `${result.data_quality_score * 100}%`
                    : '0%',
                }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">Confidence Score</span>
              <span className="font-medium">
                {result.confidence_score ? formatPercentage(result.confidence_score) : 'N/A'}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{
                  width: result.confidence_score
                    ? `${result.confidence_score * 100}%`
                    : '0%',
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Analysis Details */}
        <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Analysis Period</h4>
            <p className="text-sm text-gray-600">
              {result.analysis_period.start_date} to {result.analysis_period.end_date}
            </p>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Sources Used</h4>
            <p className="text-sm text-gray-600">
              {result.sources_used || 'Multiple'} satellite data sources
            </p>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Verification Level</h4>
            <p className="text-sm text-gray-600 capitalize">
              {result.verification_level || 'Standard'}
            </p>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Coverage</h4>
            <p className="text-sm text-gray-600">
              {((result.area_checked.analyzed_area_km2 / result.area_checked.total_area_km2) * 100).toFixed(1)}% analyzed
            </p>
          </div>
        </div>

        {/* Vegetation Indices */}
        {result.vegetation_indices && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Vegetation Indices</h4>
            <div className="grid grid-cols-1 gap-2 sm:grid-cols-3">
              {Object.entries(result.vegetation_indices).map(([key, value]) => (
                <div key={key} className="bg-gray-50 p-3 rounded">
                  <div className="text-sm font-medium text-gray-900 uppercase">
                    {key.replace('_', ' ')}
                  </div>
                  <div className="text-lg font-bold text-gray-700">
                    {typeof value === 'number' ? value.toFixed(3) : value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Warnings */}
        {result.warnings && result.warnings.length > 0 && (
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <InformationCircleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-2" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Warnings</h4>
                <ul className="mt-1 text-sm text-yellow-700 list-disc list-inside">
                  {result.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Next Steps</h3>
        <div className="space-y-3">
          <button className="w-full btn-primary">
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Download Verification Report
          </button>
          <button className="w-full btn-outline">
            View on Map
          </button>
          <button className="w-full btn-outline">
            Share Results
          </button>
        </div>
      </div>
    </motion.div>
  );
}
