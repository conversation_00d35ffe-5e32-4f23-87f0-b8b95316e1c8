# CarbonLedger Environment Configuration
# Copy this file to .env and update with your actual values
# DO NOT COMMIT the .env file to version control.

# Application Environment
ENVIRONMENT=development
DEBUG=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true
API_WORKERS=1
API_TITLE=CarbonLedger API
API_DESCRIPTION=Satellite-verified carbon credit platform
API_VERSION=1.0.0

# Database Configuration
DB_URL=sqlite:///carbonledger.db
DB_ECHO=false
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10

# Google Earth Engine Configuration
# Option 1: Service Account Key File (recommended for development)
GEE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GEE_SERVICE_ACCOUNT_KEY_PATH=path/to/your/service-account-key.json
GEE_PROJECT_ID=your-google-cloud-project-id

# Option 2: Service Account Key JSON (for cloud deployment)
# GEE_SERVICE_ACCOUNT_KEY_JSON={"type":"service_account","project_id":"..."}

# Earth Engine Rate Limiting
GEE_MAX_REQUESTS_PER_MINUTE=50
GEE_REQUEST_TIMEOUT_SECONDS=30

# Security Configuration
SECURITY_SECRET_KEY=your-super-secret-key-at-least-32-characters-long
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Rate Limiting
SECURITY_RATE_LIMIT_REQUESTS=100
SECURITY_RATE_LIMIT_WINDOW=60

# CORS Configuration
SECURITY_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://127.0.0.1:3000"]
SECURITY_CORS_ALLOW_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=console
LOG_ENABLE_REQUEST_LOGGING=true
# LOG_FILE_PATH=logs/carbonledger.log
