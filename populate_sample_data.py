#!/usr/bin/env python3
"""
Sample data population script for CarbonLedger Analytics

This script creates sample projects and verifications to demonstrate
the analytics functionality with realistic data.
"""

import random
from datetime import datetime, timezone, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import Project, Verification

# Database setup
DATABASE_URL = "sqlite:///carbonledger.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_sample_projects():
    """Create sample forest conservation projects."""
    projects = [
        {
            "name": "Amazon Forest Conservation Project",
            "description": "Large-scale forest conservation in the Amazon rainforest",
            "lon_min": -70.0, "lat_min": -10.0, "lon_max": -65.0, "lat_max": -5.0
        },
        {
            "name": "Congo Basin Protection Initiative", 
            "description": "Protecting the Congo Basin rainforest",
            "lon_min": 15.0, "lat_min": -5.0, "lon_max": 25.0, "lat_max": 5.0
        },
        {
            "name": "Borneo Rainforest Preservation",
            "description": "Preserving Borneo's unique biodiversity",
            "lon_min": 109.0, "lat_min": -4.0, "lon_max": 119.0, "lat_max": 7.0
        },
        {
            "name": "Madagascar Forest Restoration",
            "description": "Restoring Madagascar's endemic forests",
            "lon_min": 43.0, "lat_min": -25.0, "lon_max": 50.0, "lat_max": -12.0
        },
        {
            "name": "Atlantic Forest Recovery",
            "description": "Recovering Brazil's Atlantic Forest",
            "lon_min": -48.0, "lat_min": -30.0, "lon_max": -35.0, "lat_max": -15.0
        },
        {
            "name": "Sumatran Forest Conservation",
            "description": "Protecting Sumatra's remaining forests",
            "lon_min": 95.0, "lat_min": -6.0, "lon_max": 106.0, "lat_max": 6.0
        },
        {
            "name": "Central African Forest Initiative",
            "description": "Multi-country forest conservation effort",
            "lon_min": 10.0, "lat_min": -10.0, "lon_max": 30.0, "lat_max": 10.0
        },
        {
            "name": "Tasmanian Forest Protection",
            "description": "Protecting Tasmania's temperate rainforests",
            "lon_min": 144.0, "lat_min": -44.0, "lon_max": 149.0, "lat_max": -40.0
        }
    ]
    
    session = SessionLocal()
    try:
        created_projects = []
        for project_data in projects:
            project = Project(**project_data)
            session.add(project)
            session.flush()  # Get the ID
            created_projects.append(project)
        
        session.commit()
        print(f"Created {len(created_projects)} sample projects")
        return created_projects
    finally:
        session.close()

def create_sample_verifications():
    """Create sample verification records for the projects."""
    session = SessionLocal()
    try:
        # Get all projects from database
        projects = session.query(Project).all()
        verifications = []

        # Create verifications over the last 90 days
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=90)

        for project in projects:
            # Create 5-15 verifications per project
            num_verifications = random.randint(5, 15)

            for i in range(num_verifications):
                # Random date within the last 90 days
                days_ago = random.randint(0, 90)
                verified_at = end_date - timedelta(days=days_ago)

                # Random deforestation detection (20% chance)
                deforestation_detected = random.random() < 0.2

                # Random deforested area (0-50 km² if detected, 0-5 km² if not)
                if deforestation_detected:
                    deforested_area = random.uniform(5.0, 50.0)
                else:
                    deforested_area = random.uniform(0.0, 5.0)

                verification = Verification(
                    project_id=project.id,
                    deforestation_detected=deforestation_detected,
                    deforested_area_km2=deforested_area,
                    verified_at=verified_at
                )

                session.add(verification)
                verifications.append(verification)

        session.commit()
        print(f"Created {len(verifications)} sample verifications")
        return verifications
    finally:
        session.close()

def main():
    """Main function to populate sample data."""
    print("Populating CarbonLedger database with sample data...")
    
    # Create sample projects
    projects = create_sample_projects()
    
    # Create sample verifications
    verifications = create_sample_verifications()
    
    print(f"\nSample data creation completed!")
    print(f"- Projects: {len(projects)}")
    print(f"- Verifications: {len(verifications)}")
    print("\nYou can now test the analytics endpoints with real data.")

if __name__ == "__main__":
    main()
