"""
Enhanced Satellite Verification Module for CarbonLedger

This module provides comprehensive satellite-based verification using multiple
data sources, advanced algorithms, and confidence scoring for accurate
deforestation detection and carbon credit verification.

Architectural Decision: Multi-tiered verification approach
- Multiple satellite data sources for cross-validation
- Advanced vegetation indices beyond NDVI
- Temporal analysis for trend detection
- Confidence scoring and quality assessment
- Adaptive algorithm selection based on data availability
"""

import ee
import math
from typing import Dict, Any, Tuple, List, Optional
from datetime import datetime, timedelta
from enum import Enum

from .earth_engine import EarthEngineManager, EarthEngineError, DataQualityError
from .logging import get_logger, log_performance, LoggerMixin


class VerificationLevel(str, Enum):
    """Verification analysis levels with different complexity and accuracy."""
    BASIC = "basic"          # Single source, NDVI only
    STANDARD = "standard"    # Multi-source, multiple indices
    COMPREHENSIVE = "comprehensive"  # Full analysis with temporal trends


class DataSource(str, Enum):
    """Available satellite data sources."""
    SENTINEL2 = "COPERNICUS/S2_SR_HARMONIZED"
    LANDSAT8 = "LANDSAT/LC08/C02/T1_L2"
    LANDSAT9 = "LANDSAT/LC09/C02/T1_L2"
    MODIS = "MODIS/061/MOD13Q1"


class VegetationIndex(str, Enum):
    """Supported vegetation indices."""
    NDVI = "ndvi"    # Normalized Difference Vegetation Index
    EVI = "evi"      # Enhanced Vegetation Index
    SAVI = "savi"    # Soil Adjusted Vegetation Index
    MSAVI = "msavi"  # Modified Soil Adjusted Vegetation Index


class EnhancedVerificationEngine(LoggerMixin):
    """
    Enhanced satellite verification engine with multi-source analysis.

    Provides comprehensive deforestation detection using multiple satellite
    data sources, advanced algorithms, and confidence scoring.
    """

    def __init__(self, ee_manager: EarthEngineManager):
        self.ee_manager = ee_manager

        # Configuration for different verification levels
        self.level_configs = {
            VerificationLevel.BASIC: {
                "data_sources": [DataSource.SENTINEL2],
                "vegetation_indices": [VegetationIndex.NDVI],
                "temporal_analysis": False,
                "cross_validation": False
            },
            VerificationLevel.STANDARD: {
                "data_sources": [DataSource.SENTINEL2, DataSource.LANDSAT8],
                "vegetation_indices": [VegetationIndex.NDVI, VegetationIndex.EVI],
                "temporal_analysis": True,
                "cross_validation": True
            },
            VerificationLevel.COMPREHENSIVE: {
                "data_sources": [DataSource.SENTINEL2, DataSource.LANDSAT8, DataSource.LANDSAT9],
                "vegetation_indices": [VegetationIndex.NDVI, VegetationIndex.EVI, VegetationIndex.SAVI],
                "temporal_analysis": True,
                "cross_validation": True
            }
        }

    def verify_deforestation(
        self,
        geometry: ee.Geometry,
        start_date: str,
        end_date: str,
        verification_level: VerificationLevel = VerificationLevel.STANDARD
    ) -> Dict[str, Any]:
        """
        Perform enhanced deforestation verification with confidence scoring.

        Args:
            geometry: Earth Engine geometry for analysis area
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            verification_level: Level of analysis complexity

        Returns:
            Comprehensive verification results with confidence scoring
        """
        self.logger.info(
            "Starting enhanced verification",
            verification_level=verification_level.value,
            date_range=f"{start_date} to {end_date}"
        )

        try:
            config = self.level_configs[verification_level]

            # Split time period for before/after analysis
            before_period, after_period = self._split_time_period(start_date, end_date)

            # Perform multi-source analysis
            source_results = []
            for data_source in config["data_sources"]:
                try:
                    result = self._analyze_single_source(
                        geometry, data_source, before_period, after_period, config
                    )
                    if result:
                        source_results.append(result)
                except Exception as e:
                    self.logger.warning(
                        "Single source analysis failed",
                        data_source=data_source.value,
                        error=str(e)
                    )
                    continue

            if not source_results:
                raise DataQualityError("No usable satellite data found for analysis")

            # Aggregate results and calculate confidence
            aggregated_result = self._aggregate_source_results(source_results, config)

            # Add temporal analysis if enabled
            if config["temporal_analysis"]:
                temporal_analysis = self._perform_temporal_analysis(
                    geometry, config["data_sources"][0], start_date, end_date
                )
                aggregated_result["temporal_analysis"] = temporal_analysis

            # Calculate final confidence score
            confidence_score = self._calculate_confidence_score(
                source_results, aggregated_result, config
            )
            aggregated_result["confidence_score"] = confidence_score

            self.logger.info(
                "Enhanced verification completed",
                deforestation_detected=aggregated_result["deforestation_detected"],
                confidence_score=confidence_score,
                sources_used=len(source_results)
            )

            return aggregated_result

        except Exception as e:
            self.logger.error(
                "Enhanced verification failed",
                error=str(e),
                error_type=type(e).__name__
            )
            raise EarthEngineError(f"Enhanced verification failed: {e}")

    def _split_time_period(self, start_date: str, end_date: str) -> Tuple[Dict[str, str], Dict[str, str]]:
        """Split time period into before and after periods."""
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")

        # Use 2023 as before period and 2024 as after period for consistency
        before_period = {
            "start": "2023-01-01",
            "end": "2023-12-31"
        }
        after_period = {
            "start": "2024-01-01",
            "end": "2024-12-31"
        }

        return before_period, after_period

    def _analyze_single_source(
        self,
        geometry: ee.Geometry,
        data_source: DataSource,
        before_period: Dict[str, str],
        after_period: Dict[str, str],
        config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Analyze deforestation using a single satellite data source."""
        try:
            with self.ee_manager._earth_engine_operation(f"analyze_{data_source.value}"):
                # Get image collections for both periods
                before_collection = self._get_image_collection(
                    data_source, geometry, before_period["start"], before_period["end"]
                )
                after_collection = self._get_image_collection(
                    data_source, geometry, after_period["start"], after_period["end"]
                )

                # Check data availability
                before_count = before_collection.size().getInfo()
                after_count = after_collection.size().getInfo()

                if before_count == 0 or after_count == 0:
                    self.logger.warning(
                        "Insufficient data for analysis",
                        data_source=data_source.value,
                        before_count=before_count,
                        after_count=after_count
                    )
                    return None

                # Create composite images
                before_image = before_collection.median().clip(geometry)
                after_image = after_collection.median().clip(geometry)

                # Calculate vegetation indices
                vegetation_results = {}
                for vi in config["vegetation_indices"]:
                    vi_before = self._calculate_vegetation_index(before_image, vi, data_source)
                    vi_after = self._calculate_vegetation_index(after_image, vi, data_source)

                    # Calculate change
                    vi_change = vi_after.subtract(vi_before)

                    # Detect deforestation (negative change beyond threshold)
                    threshold = self._get_deforestation_threshold(vi)
                    deforestation_mask = vi_change.lt(threshold)

                    # Calculate area
                    area_km2 = self._calculate_deforestation_area(deforestation_mask, geometry)

                    vegetation_results[vi.value] = {
                        "change": vi_change.reduceRegion(
                            reducer=ee.Reducer.mean(),
                            geometry=geometry,
                            scale=30,
                            maxPixels=1e9
                        ).getInfo(),
                        "deforestation_area_km2": area_km2,
                        "threshold_used": threshold
                    }

                # Calculate data quality metrics
                quality_score = self._calculate_data_quality(
                    before_collection, after_collection, data_source
                )

                return {
                    "data_source": data_source.value,
                    "vegetation_indices": vegetation_results,
                    "data_quality_score": quality_score,
                    "image_counts": {
                        "before": before_count,
                        "after": after_count
                    },
                    "analysis_periods": {
                        "before": f"{before_period['start']} to {before_period['end']}",
                        "after": f"{after_period['start']} to {after_period['end']}"
                    }
                }

        except Exception as e:
            self.logger.error(
                "Single source analysis failed",
                data_source=data_source.value,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    def _get_image_collection(
        self,
        data_source: DataSource,
        geometry: ee.Geometry,
        start_date: str,
        end_date: str
    ) -> ee.ImageCollection:
        """Get filtered image collection for a specific data source."""
        collection = ee.ImageCollection(data_source.value)

        # Apply common filters
        filtered = collection.filterBounds(geometry).filterDate(start_date, end_date)

        # Apply source-specific filters
        if data_source in [DataSource.SENTINEL2]:
            # Sentinel-2 specific filters
            filtered = filtered.filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20))
        elif data_source in [DataSource.LANDSAT8, DataSource.LANDSAT9]:
            # Landsat specific filters
            filtered = filtered.filter(ee.Filter.lt('CLOUD_COVER', 20))
        elif data_source == DataSource.MODIS:
            # MODIS specific filters (already processed, less filtering needed)
            pass

        return filtered

    def _calculate_vegetation_index(
        self,
        image: ee.Image,
        vi_type: VegetationIndex,
        data_source: DataSource
    ) -> ee.Image:
        """Calculate vegetation index for a given image and data source."""
        if vi_type == VegetationIndex.NDVI:
            if data_source == DataSource.SENTINEL2:
                return image.normalizedDifference(['B8', 'B4']).rename('NDVI')
            elif data_source in [DataSource.LANDSAT8, DataSource.LANDSAT9]:
                return image.normalizedDifference(['SR_B5', 'SR_B4']).rename('NDVI')
            elif data_source == DataSource.MODIS:
                return image.select('NDVI').multiply(0.0001)  # MODIS NDVI scaling

        elif vi_type == VegetationIndex.EVI:
            if data_source == DataSource.SENTINEL2:
                # EVI = 2.5 * (NIR - Red) / (NIR + 6 * Red - 7.5 * Blue + 1)
                nir = image.select('B8')
                red = image.select('B4')
                blue = image.select('B2')
                return image.expression(
                    '2.5 * (NIR - RED) / (NIR + 6 * RED - 7.5 * BLUE + 1)',
                    {'NIR': nir, 'RED': red, 'BLUE': blue}
                ).rename('EVI')
            elif data_source in [DataSource.LANDSAT8, DataSource.LANDSAT9]:
                nir = image.select('SR_B5')
                red = image.select('SR_B4')
                blue = image.select('SR_B2')
                return image.expression(
                    '2.5 * (NIR - RED) / (NIR + 6 * RED - 7.5 * BLUE + 1)',
                    {'NIR': nir, 'RED': red, 'BLUE': blue}
                ).rename('EVI')

        elif vi_type == VegetationIndex.SAVI:
            # SAVI = (NIR - Red) / (NIR + Red + L) * (1 + L), where L = 0.5
            L = 0.5
            if data_source == DataSource.SENTINEL2:
                nir = image.select('B8')
                red = image.select('B4')
                return image.expression(
                    '(NIR - RED) / (NIR + RED + L) * (1 + L)',
                    {'NIR': nir, 'RED': red, 'L': L}
                ).rename('SAVI')
            elif data_source in [DataSource.LANDSAT8, DataSource.LANDSAT9]:
                nir = image.select('SR_B5')
                red = image.select('SR_B4')
                return image.expression(
                    '(NIR - RED) / (NIR + RED + L) * (1 + L)',
                    {'NIR': nir, 'RED': red, 'L': L}
                ).rename('SAVI')

        # Default to NDVI if unsupported combination
        return self._calculate_vegetation_index(image, VegetationIndex.NDVI, data_source)

    def _get_deforestation_threshold(self, vi_type: VegetationIndex) -> float:
        """Get deforestation threshold for different vegetation indices."""
        thresholds = {
            VegetationIndex.NDVI: -0.2,
            VegetationIndex.EVI: -0.15,
            VegetationIndex.SAVI: -0.18,
            VegetationIndex.MSAVI: -0.18
        }
        return thresholds.get(vi_type, -0.2)

    def _calculate_deforestation_area(
        self,
        deforestation_mask: ee.Image,
        geometry: ee.Geometry
    ) -> float:
        """Calculate deforestation area in square kilometers."""
        try:
            # Calculate area using pixel area
            pixel_area = ee.Image.pixelArea()
            deforestation_area_image = deforestation_mask.multiply(pixel_area)

            # Get total area
            stats = deforestation_area_image.reduceRegion(
                reducer=ee.Reducer.sum(),
                geometry=geometry,
                scale=30,
                maxPixels=1e10
            )

            # Get the first band value (vegetation index name varies)
            area_m2 = ee.Number(stats.values().get(0)).getInfo() or 0
            area_km2 = area_m2 / 1_000_000

            return area_km2

        except Exception as e:
            self.logger.warning(
                "Failed to calculate deforestation area",
                error=str(e)
            )
            return 0.0

    def _calculate_data_quality(
        self,
        before_collection: ee.ImageCollection,
        after_collection: ee.ImageCollection,
        data_source: DataSource
    ) -> float:
        """Calculate data quality score based on various factors."""
        try:
            # Image count factor
            before_count = before_collection.size().getInfo()
            after_count = after_collection.size().getInfo()
            count_score = min(1.0, (before_count + after_count) / 20.0)  # Ideal: 10+ images per period

            # Cloud cover factor (for applicable sources)
            if data_source in [DataSource.SENTINEL2, DataSource.LANDSAT8, DataSource.LANDSAT9]:
                cloud_property = 'CLOUDY_PIXEL_PERCENTAGE' if data_source == DataSource.SENTINEL2 else 'CLOUD_COVER'

                before_cloud = before_collection.aggregate_mean(cloud_property).getInfo() or 50
                after_cloud = after_collection.aggregate_mean(cloud_property).getInfo() or 50
                avg_cloud = (before_cloud + after_cloud) / 2

                # Lower cloud cover = higher quality
                cloud_score = max(0.0, (100 - avg_cloud) / 100.0)
            else:
                cloud_score = 1.0  # MODIS doesn't have cloud cover in same way

            # Temporal distribution factor
            temporal_score = self._calculate_temporal_distribution_score(
                before_collection, after_collection
            )

            # Weighted average
            quality_score = (count_score * 0.4 + cloud_score * 0.4 + temporal_score * 0.2)

            return round(quality_score, 3)

        except Exception as e:
            self.logger.warning(
                "Failed to calculate data quality",
                error=str(e)
            )
            return 0.5  # Default moderate quality

    def _calculate_temporal_distribution_score(
        self,
        before_collection: ee.ImageCollection,
        after_collection: ee.ImageCollection
    ) -> float:
        """Calculate score based on temporal distribution of images."""
        try:
            # Simple implementation: check if we have images across different months
            # More sophisticated implementation would check for even distribution

            # For now, return a fixed good score if we have multiple images
            before_count = before_collection.size().getInfo()
            after_count = after_collection.size().getInfo()

            if before_count >= 3 and after_count >= 3:
                return 1.0
            elif before_count >= 2 and after_count >= 2:
                return 0.8
            elif before_count >= 1 and after_count >= 1:
                return 0.6
            else:
                return 0.2

        except Exception:
            return 0.5

    def _aggregate_source_results(
        self,
        source_results: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Aggregate results from multiple data sources."""
        if not source_results:
            raise DataQualityError("No source results to aggregate")

        # Collect all vegetation index results
        all_vi_results = {}
        total_quality_score = 0

        for result in source_results:
            total_quality_score += result["data_quality_score"]

            for vi_name, vi_data in result["vegetation_indices"].items():
                if vi_name not in all_vi_results:
                    all_vi_results[vi_name] = []
                all_vi_results[vi_name].append(vi_data)

        # Calculate consensus for each vegetation index
        aggregated_vi = {}
        for vi_name, vi_results in all_vi_results.items():
            areas = [r["deforestation_area_km2"] for r in vi_results]

            # Use median for robustness against outliers
            areas.sort()
            n = len(areas)
            median_area = areas[n//2] if n % 2 == 1 else (areas[n//2-1] + areas[n//2]) / 2

            aggregated_vi[vi_name] = {
                "deforestation_area_km2": median_area,
                "source_count": len(vi_results),
                "area_range": [min(areas), max(areas)] if len(areas) > 1 else [areas[0], areas[0]]
            }

        # Determine overall deforestation detection
        # Use NDVI as primary, others as confirmation
        primary_vi = "ndvi"
        deforestation_detected = False
        primary_area = 0.0

        if primary_vi in aggregated_vi:
            primary_area = aggregated_vi[primary_vi]["deforestation_area_km2"]
            deforestation_detected = primary_area > 0

        # Cross-validation if enabled
        if config.get("cross_validation", False) and len(aggregated_vi) > 1:
            # Check if multiple indices agree
            detection_votes = sum(1 for vi_data in aggregated_vi.values()
                                if vi_data["deforestation_area_km2"] > 0)
            consensus_threshold = len(aggregated_vi) * 0.6  # 60% agreement

            if detection_votes < consensus_threshold:
                # Reduce confidence if indices disagree
                primary_area *= 0.7  # Reduce area estimate

        avg_quality_score = total_quality_score / len(source_results)

        return {
            "deforestation_detected": deforestation_detected,
            "deforestation_area_km2": primary_area,
            "vegetation_indices": aggregated_vi,
            "data_quality_score": round(avg_quality_score, 3),
            "sources_used": len(source_results),
            "analysis_metadata": {
                "cross_validation_enabled": config.get("cross_validation", False),
                "temporal_analysis_enabled": config.get("temporal_analysis", False)
            }
        }

    def _perform_temporal_analysis(
        self,
        geometry: ee.Geometry,
        data_source: DataSource,
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Perform temporal trend analysis to detect gradual changes."""
        try:
            self.logger.info("Performing temporal analysis")

            # Create quarterly periods for trend analysis
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d")

            # Generate quarterly periods
            periods = []
            current = start
            while current < end:
                period_end = min(current + timedelta(days=90), end)
                periods.append({
                    "start": current.strftime("%Y-%m-%d"),
                    "end": period_end.strftime("%Y-%m-%d"),
                    "label": f"Q{len(periods)+1}"
                })
                current = period_end + timedelta(days=1)

            # Calculate NDVI for each period
            ndvi_values = []
            for period in periods:
                try:
                    collection = self._get_image_collection(
                        data_source, geometry, period["start"], period["end"]
                    )

                    if collection.size().getInfo() > 0:
                        image = collection.median().clip(geometry)
                        ndvi = self._calculate_vegetation_index(image, VegetationIndex.NDVI, data_source)

                        mean_ndvi = ndvi.reduceRegion(
                            reducer=ee.Reducer.mean(),
                            geometry=geometry,
                            scale=30,
                            maxPixels=1e9
                        ).getInfo()

                        ndvi_value = list(mean_ndvi.values())[0] if mean_ndvi else None
                        if ndvi_value is not None:
                            ndvi_values.append({
                                "period": period["label"],
                                "date_range": f"{period['start']} to {period['end']}",
                                "ndvi": round(ndvi_value, 4)
                            })

                except Exception as e:
                    self.logger.warning(
                        "Failed to process temporal period",
                        period=period["label"],
                        error=str(e)
                    )
                    continue

            # Calculate trend
            trend_analysis = self._calculate_trend(ndvi_values)

            return {
                "periods_analyzed": len(ndvi_values),
                "ndvi_time_series": ndvi_values,
                "trend": trend_analysis
            }

        except Exception as e:
            self.logger.error(
                "Temporal analysis failed",
                error=str(e),
                error_type=type(e).__name__
            )
            return {
                "periods_analyzed": 0,
                "error": str(e)
            }

    def _calculate_trend(self, ndvi_values: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate trend from NDVI time series."""
        if len(ndvi_values) < 2:
            return {"trend": "insufficient_data", "slope": 0, "confidence": 0}

        # Simple linear regression
        n = len(ndvi_values)
        x_values = list(range(n))
        y_values = [item["ndvi"] for item in ndvi_values]

        # Calculate slope
        x_mean = sum(x_values) / n
        y_mean = sum(y_values) / n

        numerator = sum((x_values[i] - x_mean) * (y_values[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            slope = 0
        else:
            slope = numerator / denominator

        # Classify trend
        if slope < -0.01:
            trend = "declining"
        elif slope > 0.01:
            trend = "improving"
        else:
            trend = "stable"

        # Simple confidence based on data points and variance
        variance = sum((y_values[i] - y_mean) ** 2 for i in range(n)) / n
        confidence = min(1.0, n / 8.0) * max(0.1, 1.0 - variance)  # More points = higher confidence

        return {
            "trend": trend,
            "slope": round(slope, 6),
            "confidence": round(confidence, 3),
            "variance": round(variance, 6)
        }

    def _calculate_confidence_score(
        self,
        source_results: List[Dict[str, Any]],
        aggregated_result: Dict[str, Any],
        config: Dict[str, Any]
    ) -> float:
        """Calculate overall confidence score for the verification."""
        try:
            # Data availability factor (0-1)
            sources_used = len(source_results)
            max_sources = len(config["data_sources"])
            availability_score = sources_used / max_sources

            # Data quality factor (0-1)
            quality_score = aggregated_result["data_quality_score"]

            # Cross-validation factor (0-1)
            if config.get("cross_validation", False) and len(aggregated_result["vegetation_indices"]) > 1:
                # Check agreement between vegetation indices
                areas = [vi_data["deforestation_area_km2"]
                        for vi_data in aggregated_result["vegetation_indices"].values()]

                if len(areas) > 1:
                    mean_area = sum(areas) / len(areas)
                    if mean_area > 0:
                        # Calculate coefficient of variation
                        variance = sum((area - mean_area) ** 2 for area in areas) / len(areas)
                        std_dev = math.sqrt(variance)
                        cv = std_dev / mean_area if mean_area > 0 else 1

                        # Lower CV = higher agreement = higher confidence
                        cross_validation_score = max(0.0, 1.0 - cv)
                    else:
                        # All indices agree on no deforestation
                        cross_validation_score = 1.0
                else:
                    cross_validation_score = 0.8  # Single index
            else:
                cross_validation_score = 0.7  # No cross-validation

            # Temporal consistency factor (0-1)
            temporal_score = 0.8  # Default
            if "temporal_analysis" in aggregated_result:
                temporal_data = aggregated_result["temporal_analysis"]
                if "trend" in temporal_data:
                    trend_confidence = temporal_data["trend"].get("confidence", 0.5)
                    temporal_score = trend_confidence

            # Weighted combination
            confidence = (
                availability_score * 0.25 +
                quality_score * 0.35 +
                cross_validation_score * 0.25 +
                temporal_score * 0.15
            )

            return round(confidence, 3)

        except Exception as e:
            self.logger.warning(
                "Failed to calculate confidence score",
                error=str(e)
            )
            return 0.5  # Default moderate confidence


# Legacy function for backward compatibility
def analyze_deforestation(aoi: ee.Geometry.Rectangle) -> tuple[bool, float, str]:
    """
    Legacy function for backward compatibility.

    This function maintains the original interface while using the enhanced
    verification engine internally.
    """
    try:
        # Create a basic Earth Engine manager for legacy support
        from .config import GoogleEarthEngineSettings
        from .earth_engine import EarthEngineManager

        # Use default settings
        ee_settings = GoogleEarthEngineSettings()
        ee_manager = EarthEngineManager(ee_settings)

        # Create enhanced verification engine
        engine = EnhancedVerificationEngine(ee_manager)

        # Perform basic verification
        result = engine.verify_deforestation(
            geometry=aoi,
            start_date="2023-01-01",
            end_date="2024-12-31",
            verification_level=VerificationLevel.BASIC
        )

        deforestation_detected = result["deforestation_detected"]
        area_km2 = result["deforestation_area_km2"]

        return deforestation_detected, area_km2, ""

    except Exception as e:
        return False, 0.0, str(e)
