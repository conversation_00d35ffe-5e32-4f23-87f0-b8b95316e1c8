import ee

def analyze_deforestation(aoi: ee.Geometry.Rectangle) -> tuple[bool, float, str]:
    """
    Analyzes deforestation within a given Area of Interest (AOI).
    Returns a tuple containing: (deforestation_detected, area_in_sq_km, error_message).
    """
    try:
        print("Initializing Earth Engine for analysis...")
        ee.Initialize()

        def calculate_ndvi(image):
            return image.normalizedDifference(['B8', 'B4']).rename('NDVI')

        image_collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')

        image_before = image_collection.filterBounds(aoi) \
                                       .filterDate('2023-01-01', '2023-12-31') \
                                       .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20)) \
                                       .median().clip(aoi)

        image_after = image_collection.filterBounds(aoi) \
                                      .filterDate('2024-01-01', '2024-12-31') \
                                      .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20)) \
                                      .median().clip(aoi)

        ndvi_before = calculate_ndvi(image_before)
        ndvi_after = calculate_ndvi(image_after)

        ndvi_difference = ndvi_after.subtract(ndvi_before)

        deforestation_threshold = -0.2
        deforestation_areas = ndvi_difference.lt(deforestation_threshold)

        # Calculate the area of deforestation
        pixel_area = ee.Image.pixelArea() # Area in square meters
        deforestation_area_image = deforestation_areas.multiply(pixel_area)

        stats = deforestation_area_image.reduceRegion(
            reducer=ee.Reducer.sum(),
            geometry=aoi,
            scale=30,
            maxPixels=1e10
        ).get('NDVI')

        # ee.Number is a server-side object, getInfo() brings it client-side
        deforestation_area_m2 = ee.Number(stats).getInfo() or 0
        deforestation_area_km2 = deforestation_area_m2 / 1_000_000

        return deforestation_area_km2 > 0, deforestation_area_km2, ""

    except ee.EEException as e:
        print(f"An Earth Engine error occurred: {e}")
        return False, 0, str(e)
    except Exception as e:
        print(f"A general error occurred: {e}")
        return False, 0, str(e)
