"""
CarbonLedger Exception Handling System

This module provides comprehensive exception handling with proper error classification,
logging, and user-friendly error responses while maintaining security.

Architectural Decision: Structured exception hierarchy
- Clear error classification for different failure modes
- Security-conscious error messages (no sensitive data exposure)
- Comprehensive logging for debugging and monitoring
- Consistent error response format for API consumers
"""

import traceback
from typing import Dict, Any, Optional, Union
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .logging import get_logger


class CarbonLedgerError(Exception):
    """
    Base exception for all CarbonLedger-specific errors.
    
    Provides a consistent interface for error handling with proper
    logging and user-friendly error messages.
    """
    
    def __init__(
        self,
        message: str,
        error_code: str = "INTERNAL_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        self.user_message = user_message or "An error occurred while processing your request."
        
        super().__init__(self.message)


class ValidationError(CarbonLedgerError):
    """Raised when input validation fails."""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=status.HTTP_400_BAD_REQUEST,
            user_message="The provided input is invalid. Please check your data and try again.",
            **kwargs
        )
        if field:
            self.details["field"] = field


class AuthenticationError(CarbonLedgerError):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=status.HTTP_401_UNAUTHORIZED,
            user_message="Authentication is required to access this resource.",
            **kwargs
        )


class AuthorizationError(CarbonLedgerError):
    """Raised when authorization fails."""
    
    def __init__(self, message: str = "Access denied", **kwargs):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=status.HTTP_403_FORBIDDEN,
            user_message="You don't have permission to access this resource.",
            **kwargs
        )


class ResourceNotFoundError(CarbonLedgerError):
    """Raised when a requested resource is not found."""
    
    def __init__(self, resource_type: str, resource_id: Union[str, int], **kwargs):
        message = f"{resource_type} with ID {resource_id} not found"
        super().__init__(
            message=message,
            error_code="RESOURCE_NOT_FOUND",
            status_code=status.HTTP_404_NOT_FOUND,
            user_message=f"The requested {resource_type.lower()} was not found.",
            details={"resource_type": resource_type, "resource_id": str(resource_id)},
            **kwargs
        )


class ConflictError(CarbonLedgerError):
    """Raised when a resource conflict occurs."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code="CONFLICT_ERROR",
            status_code=status.HTTP_409_CONFLICT,
            user_message="The request conflicts with the current state of the resource.",
            **kwargs
        )


class RateLimitError(CarbonLedgerError):
    """Raised when rate limits are exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: Optional[int] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            user_message="Too many requests. Please try again later.",
            **kwargs
        )
        if retry_after:
            self.details["retry_after"] = retry_after


class ExternalServiceError(CarbonLedgerError):
    """Raised when external service calls fail."""
    
    def __init__(self, service_name: str, message: str, **kwargs):
        super().__init__(
            message=f"{service_name}: {message}",
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=status.HTTP_502_BAD_GATEWAY,
            user_message="An external service is temporarily unavailable. Please try again later.",
            details={"service": service_name},
            **kwargs
        )


class DataQualityError(CarbonLedgerError):
    """Raised when data quality issues are detected."""
    
    def __init__(self, message: str, data_source: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="DATA_QUALITY_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            user_message="The data quality is insufficient for processing. Please try with different parameters.",
            **kwargs
        )
        if data_source:
            self.details["data_source"] = data_source


class ConfigurationError(CarbonLedgerError):
    """Raised when configuration issues are detected."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            user_message="A configuration error has occurred. Please contact support.",
            **kwargs
        )


class BlockchainError(CarbonLedgerError):
    """Raised when blockchain operations fail."""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code="BLOCKCHAIN_ERROR",
            status_code=status.HTTP_502_BAD_GATEWAY,
            user_message="Blockchain service is temporarily unavailable. Please try again later.",
            **kwargs
        )


class ErrorHandler:
    """
    Centralized error handler for the application.
    
    Provides consistent error logging and response formatting
    while ensuring sensitive information is not exposed.
    """
    
    def __init__(self):
        self.logger = get_logger(__name__, component="ErrorHandler")
    
    def create_error_response(
        self,
        error: Exception,
        request: Optional[Request] = None
    ) -> JSONResponse:
        """
        Create a standardized error response.
        
        Args:
            error: The exception that occurred
            request: The FastAPI request object (optional)
        
        Returns:
            JSONResponse with error details
        """
        # Extract request information for logging
        request_info = {}
        if request:
            request_info = {
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else "unknown"
            }
        
        # Handle CarbonLedger-specific errors
        if isinstance(error, CarbonLedgerError):
            self._log_application_error(error, request_info)
            return self._create_json_response(
                status_code=error.status_code,
                error_code=error.error_code,
                message=error.user_message,
                details=error.details
            )
        
        # Handle FastAPI validation errors
        elif isinstance(error, RequestValidationError):
            self._log_validation_error(error, request_info)
            return self._create_json_response(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                error_code="VALIDATION_ERROR",
                message="Request validation failed",
                details={"validation_errors": error.errors()}
            )
        
        # Handle HTTP exceptions
        elif isinstance(error, (HTTPException, StarletteHTTPException)):
            self._log_http_error(error, request_info)
            return self._create_json_response(
                status_code=error.status_code,
                error_code="HTTP_ERROR",
                message=error.detail,
                details={}
            )
        
        # Handle unexpected errors
        else:
            self._log_unexpected_error(error, request_info)
            return self._create_json_response(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code="INTERNAL_ERROR",
                message="An unexpected error occurred",
                details={}
            )
    
    def _log_application_error(self, error: CarbonLedgerError, request_info: Dict[str, Any]) -> None:
        """Log application-specific errors."""
        log_level = "warning" if error.status_code < 500 else "error"
        
        getattr(self.logger, log_level)(
            "Application error occurred",
            error_code=error.error_code,
            error_message=error.message,
            status_code=error.status_code,
            details=error.details,
            **request_info
        )
    
    def _log_validation_error(self, error: RequestValidationError, request_info: Dict[str, Any]) -> None:
        """Log validation errors."""
        self.logger.warning(
            "Request validation failed",
            validation_errors=error.errors(),
            **request_info
        )
    
    def _log_http_error(self, error: Union[HTTPException, StarletteHTTPException], request_info: Dict[str, Any]) -> None:
        """Log HTTP errors."""
        log_level = "warning" if error.status_code < 500 else "error"
        
        getattr(self.logger, log_level)(
            "HTTP error occurred",
            status_code=error.status_code,
            detail=error.detail,
            **request_info
        )
    
    def _log_unexpected_error(self, error: Exception, request_info: Dict[str, Any]) -> None:
        """Log unexpected errors with full traceback."""
        self.logger.error(
            "Unexpected error occurred",
            error_type=type(error).__name__,
            error_message=str(error),
            traceback=traceback.format_exc(),
            **request_info
        )
    
    def _create_json_response(
        self,
        status_code: int,
        error_code: str,
        message: str,
        details: Dict[str, Any]
    ) -> JSONResponse:
        """Create a standardized JSON error response."""
        response_data = {
            "error": {
                "code": error_code,
                "message": message,
                "timestamp": self._get_current_timestamp(),
            }
        }
        
        # Add details if present
        if details:
            response_data["error"]["details"] = details
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime, timezone
        return datetime.now(timezone.utc).isoformat()


# Global error handler instance
error_handler = ErrorHandler()


def setup_exception_handlers(app) -> None:
    """
    Setup exception handlers for the FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    
    @app.exception_handler(CarbonLedgerError)
    async def carbonledger_exception_handler(request: Request, exc: CarbonLedgerError):
        return error_handler.create_error_response(exc, request)
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        return error_handler.create_error_response(exc, request)
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return error_handler.create_error_response(exc, request)
    
    @app.exception_handler(StarletteHTTPException)
    async def starlette_exception_handler(request: Request, exc: StarletteHTTPException):
        return error_handler.create_error_response(exc, request)
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        return error_handler.create_error_response(exc, request)
