"""
CarbonLedger Health Check and Monitoring System

This module provides comprehensive health checks and system monitoring
for all critical components of the application.

Architectural Decision: Comprehensive health monitoring
- Database connectivity and performance checks
- Google Earth Engine authentication and quota status
- External service availability
- System resource monitoring
- Detailed health reporting for operations teams
"""

import time
import psutil
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from enum import Enum

from .config import Settings
from .db import DatabaseManager
from .earth_engine import EarthEngineManager
from .logging import get_logger, LoggerMixin


class HealthStatus(str, Enum):
    """Health check status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class ComponentHealth:
    """Represents the health status of a system component."""
    
    def __init__(
        self,
        name: str,
        status: HealthStatus,
        message: str = "",
        details: Optional[Dict[str, Any]] = None,
        response_time_ms: Optional[float] = None
    ):
        self.name = name
        self.status = status
        self.message = message
        self.details = details or {}
        self.response_time_ms = response_time_ms
        self.timestamp = datetime.now(timezone.utc).isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        result = {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "timestamp": self.timestamp,
        }
        
        if self.details:
            result["details"] = self.details
        
        if self.response_time_ms is not None:
            result["response_time_ms"] = round(self.response_time_ms, 2)
        
        return result


class HealthChecker(LoggerMixin):
    """
    Comprehensive health checker for all system components.
    
    Performs health checks on critical system components and provides
    detailed status information for monitoring and alerting.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.db_manager: Optional[DatabaseManager] = None
        self.ee_manager: Optional[EarthEngineManager] = None
    
    def set_managers(
        self,
        db_manager: Optional[DatabaseManager] = None,
        ee_manager: Optional[EarthEngineManager] = None
    ) -> None:
        """Set manager instances for health checking."""
        self.db_manager = db_manager
        self.ee_manager = ee_manager
    
    async def check_all(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check of all components.
        
        Returns:
            Dictionary containing overall health status and component details
        """
        start_time = time.time()
        
        self.logger.info("Starting comprehensive health check")
        
        # Perform individual component checks
        checks = [
            self._check_database(),
            self._check_earth_engine(),
            self._check_system_resources(),
            self._check_configuration(),
        ]
        
        # Execute all checks
        component_results = []
        for check in checks:
            try:
                result = await check if hasattr(check, '__await__') else check
                component_results.append(result)
            except Exception as e:
                self.logger.error(
                    "Health check failed",
                    component=getattr(check, '__name__', 'unknown'),
                    error=str(e),
                    error_type=type(e).__name__
                )
                component_results.append(ComponentHealth(
                    name=getattr(check, '__name__', 'unknown'),
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {str(e)}"
                ))
        
        # Calculate overall health
        overall_status = self._calculate_overall_status(component_results)
        total_time = (time.time() - start_time) * 1000
        
        result = {
            "status": overall_status.value,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "response_time_ms": round(total_time, 2),
            "version": self.settings.api.version,
            "environment": self.settings.environment,
            "components": [comp.to_dict() for comp in component_results]
        }
        
        self.logger.info(
            "Health check completed",
            overall_status=overall_status.value,
            total_time_ms=round(total_time, 2),
            component_count=len(component_results)
        )
        
        return result
    
    def _check_database(self) -> ComponentHealth:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            if not self.db_manager:
                return ComponentHealth(
                    name="database",
                    status=HealthStatus.UNHEALTHY,
                    message="Database manager not initialized"
                )
            
            # Test database connection
            with self.db_manager.get_session() as session:
                # Simple query to test connectivity
                session.execute("SELECT 1")
            
            response_time = (time.time() - start_time) * 1000
            connection_info = self.db_manager.get_connection_info()
            
            # Determine status based on response time
            if response_time > 1000:  # > 1 second
                status = HealthStatus.DEGRADED
                message = "Database responding slowly"
            else:
                status = HealthStatus.HEALTHY
                message = "Database connection healthy"
            
            return ComponentHealth(
                name="database",
                status=status,
                message=message,
                details=connection_info,
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {str(e)}",
                response_time_ms=response_time
            )
    
    def _check_earth_engine(self) -> ComponentHealth:
        """Check Google Earth Engine connectivity and authentication."""
        start_time = time.time()
        
        try:
            if not self.ee_manager:
                return ComponentHealth(
                    name="earth_engine",
                    status=HealthStatus.UNHEALTHY,
                    message="Earth Engine manager not initialized"
                )
            
            # Check authentication status
            if not self.ee_manager.is_authenticated():
                return ComponentHealth(
                    name="earth_engine",
                    status=HealthStatus.UNHEALTHY,
                    message="Earth Engine not authenticated"
                )
            
            # Get usage statistics
            usage_stats = self.ee_manager.get_usage_stats()
            response_time = (time.time() - start_time) * 1000
            
            # Determine status based on usage
            requests_used = usage_stats.get("requests_in_current_window", 0)
            max_requests = usage_stats.get("max_requests_per_minute", 100)
            usage_percentage = (requests_used / max_requests) * 100
            
            if usage_percentage > 90:
                status = HealthStatus.DEGRADED
                message = "Earth Engine quota nearly exhausted"
            elif usage_percentage > 50:
                status = HealthStatus.DEGRADED
                message = "Earth Engine quota usage high"
            else:
                status = HealthStatus.HEALTHY
                message = "Earth Engine connection healthy"
            
            return ComponentHealth(
                name="earth_engine",
                status=status,
                message=message,
                details={
                    "usage_percentage": round(usage_percentage, 1),
                    "requests_used": requests_used,
                    "max_requests": max_requests
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ComponentHealth(
                name="earth_engine",
                status=HealthStatus.UNHEALTHY,
                message=f"Earth Engine check failed: {str(e)}",
                response_time_ms=response_time
            )
    
    def _check_system_resources(self) -> ComponentHealth:
        """Check system resource usage."""
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Determine status based on resource usage
            critical_threshold = 90
            warning_threshold = 75
            
            issues = []
            if cpu_percent > critical_threshold:
                issues.append(f"CPU usage critical: {cpu_percent}%")
            elif cpu_percent > warning_threshold:
                issues.append(f"CPU usage high: {cpu_percent}%")
            
            if memory.percent > critical_threshold:
                issues.append(f"Memory usage critical: {memory.percent}%")
            elif memory.percent > warning_threshold:
                issues.append(f"Memory usage high: {memory.percent}%")
            
            if disk.percent > critical_threshold:
                issues.append(f"Disk usage critical: {disk.percent}%")
            elif disk.percent > warning_threshold:
                issues.append(f"Disk usage high: {disk.percent}%")
            
            # Determine overall status
            if any("critical" in issue for issue in issues):
                status = HealthStatus.UNHEALTHY
                message = "Critical resource usage detected"
            elif issues:
                status = HealthStatus.DEGRADED
                message = "High resource usage detected"
            else:
                status = HealthStatus.HEALTHY
                message = "System resources healthy"
            
            return ComponentHealth(
                name="system_resources",
                status=status,
                message=message,
                details={
                    "cpu_percent": round(cpu_percent, 1),
                    "memory_percent": round(memory.percent, 1),
                    "disk_percent": round(disk.percent, 1),
                    "memory_available_gb": round(memory.available / (1024**3), 2),
                    "disk_free_gb": round(disk.free / (1024**3), 2),
                    "issues": issues
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="system_resources",
                status=HealthStatus.UNHEALTHY,
                message=f"System resource check failed: {str(e)}"
            )
    
    def _check_configuration(self) -> ComponentHealth:
        """Check application configuration."""
        try:
            issues = []
            
            # Check critical configuration
            if self.settings.environment == "production":
                if self.settings.debug:
                    issues.append("Debug mode enabled in production")
                
                if self.settings.security.secret_key == "dev-secret-key-change-in-production":
                    issues.append("Default secret key in production")
            
            # Check Earth Engine configuration
            if not self.settings.gee.service_account_email and not self.settings.gee.service_account_key_path:
                issues.append("No Earth Engine authentication configured")
            
            # Determine status
            if issues:
                status = HealthStatus.DEGRADED
                message = "Configuration issues detected"
            else:
                status = HealthStatus.HEALTHY
                message = "Configuration healthy"
            
            return ComponentHealth(
                name="configuration",
                status=status,
                message=message,
                details={
                    "environment": self.settings.environment,
                    "debug_mode": self.settings.debug,
                    "issues": issues
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="configuration",
                status=HealthStatus.UNHEALTHY,
                message=f"Configuration check failed: {str(e)}"
            )
    
    def _calculate_overall_status(self, components: List[ComponentHealth]) -> HealthStatus:
        """Calculate overall health status from component statuses."""
        if not components:
            return HealthStatus.UNHEALTHY
        
        statuses = [comp.status for comp in components]
        
        if HealthStatus.UNHEALTHY in statuses:
            return HealthStatus.UNHEALTHY
        elif HealthStatus.DEGRADED in statuses:
            return HealthStatus.DEGRADED
        else:
            return HealthStatus.HEALTHY
