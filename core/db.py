"""
CarbonLedger Database Management

This module provides secure, production-ready database management with proper
connection pooling, error handling, and security considerations.

Architectural Decision: SQLModel with enhanced security and performance
- Type-safe database operations
- Connection pooling for performance
- Proper error handling and logging
- Security measures against injection attacks
- Support for multiple database backends
"""

import os
import contextlib
from typing import Generator, Optional, Dict, Any
from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlmodel import create_engine, SQLModel, Session

from .config import DatabaseSettings
from .logging import get_logger, LoggerMixin


class DatabaseError(Exception):
    """Base exception for database operations."""
    pass


class ConnectionError(DatabaseError):
    """Raised when database connection fails."""
    pass


class DatabaseManager(LoggerMixin):
    """
    Manages database connections and operations with security and performance considerations.

    This class provides a secure, production-ready interface to the database
    with proper connection pooling, error handling, and monitoring.
    """

    def __init__(self, config: DatabaseSettings):
        self.config = config
        self._engine: Optional[Engine] = None
        self._setup_engine()

    def _setup_engine(self) -> None:
        """Setup database engine with proper configuration."""
        try:
            self.logger.info(
                "Setting up database engine",
                database_url=self._mask_database_url(self.config.url)
            )

            # Engine configuration based on database type
            engine_kwargs = {
                "echo": self.config.echo,
                "pool_pre_ping": True,  # Verify connections before use
                "pool_recycle": 3600,   # Recycle connections every hour
            }

            # SQLite-specific configuration
            if self.config.url.startswith("sqlite"):
                engine_kwargs.update({
                    "connect_args": {
                        "check_same_thread": False,  # Allow multiple threads
                        "timeout": 20,  # Connection timeout
                    }
                })
            else:
                # PostgreSQL/MySQL configuration
                engine_kwargs.update({
                    "pool_size": self.config.pool_size,
                    "max_overflow": self.config.max_overflow,
                })

            self._engine = create_engine(self.config.url, **engine_kwargs)

            # Setup event listeners for security and monitoring
            self._setup_event_listeners()

            # Test connection
            self._test_connection()

            self.logger.info("Database engine setup completed successfully")

        except Exception as e:
            self.logger.error(
                "Failed to setup database engine",
                error=str(e),
                error_type=type(e).__name__
            )
            raise ConnectionError(f"Failed to setup database engine: {e}")

    def _setup_event_listeners(self) -> None:
        """Setup SQLAlchemy event listeners for security and monitoring."""

        @event.listens_for(self._engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """Set SQLite pragmas for security and performance."""
            if self.config.url.startswith("sqlite"):
                cursor = dbapi_connection.cursor()
                # Enable foreign key constraints
                cursor.execute("PRAGMA foreign_keys=ON")
                # Enable WAL mode for better concurrency
                cursor.execute("PRAGMA journal_mode=WAL")
                # Set secure delete
                cursor.execute("PRAGMA secure_delete=ON")
                cursor.close()

        @event.listens_for(self._engine, "before_cursor_execute")
        def log_sql_queries(conn, cursor, statement, parameters, context, executemany):
            """Log SQL queries for monitoring (excluding sensitive data)."""
            if self.config.echo:
                # Mask potential sensitive data in parameters
                safe_params = self._mask_sensitive_parameters(parameters)
                self.logger.debug(
                    "Executing SQL query",
                    statement=statement[:200] + "..." if len(statement) > 200 else statement,
                    parameters=safe_params
                )

    def _mask_database_url(self, url: str) -> str:
        """Mask sensitive information in database URL for logging."""
        if "://" in url:
            scheme, rest = url.split("://", 1)
            if "@" in rest:
                credentials, host_part = rest.split("@", 1)
                return f"{scheme}://***:***@{host_part}"
        return url

    def _mask_sensitive_parameters(self, parameters) -> Any:
        """Mask sensitive data in SQL parameters."""
        if isinstance(parameters, dict):
            masked = {}
            for key, value in parameters.items():
                key_lower = key.lower()
                if any(sensitive in key_lower for sensitive in ['password', 'secret', 'key', 'token']):
                    masked[key] = "***MASKED***"
                else:
                    masked[key] = value
            return masked
        return parameters

    def _test_connection(self) -> None:
        """Test database connection."""
        try:
            with self._engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            self.logger.debug("Database connection test successful")
        except Exception as e:
            self.logger.error(
                "Database connection test failed",
                error=str(e),
                error_type=type(e).__name__
            )
            raise ConnectionError(f"Database connection test failed: {e}")

    @property
    def engine(self) -> Engine:
        """Get database engine."""
        if self._engine is None:
            raise ConnectionError("Database engine not initialized")
        return self._engine

    @contextlib.contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Get database session with proper error handling and cleanup.

        Yields:
            SQLModel Session instance

        Raises:
            DatabaseError: If session operations fail
        """
        session = Session(self.engine)
        try:
            self.logger.debug("Database session created")
            yield session
            session.commit()
            self.logger.debug("Database session committed successfully")

        except IntegrityError as e:
            session.rollback()
            self.logger.warning(
                "Database integrity error",
                error=str(e),
                error_type="IntegrityError"
            )
            raise DatabaseError(f"Database integrity error: {e}")

        except SQLAlchemyError as e:
            session.rollback()
            self.logger.error(
                "Database operation failed",
                error=str(e),
                error_type=type(e).__name__
            )
            raise DatabaseError(f"Database operation failed: {e}")

        except Exception as e:
            session.rollback()
            self.logger.error(
                "Unexpected error in database session",
                error=str(e),
                error_type=type(e).__name__
            )
            raise DatabaseError(f"Unexpected database error: {e}")

        finally:
            session.close()
            self.logger.debug("Database session closed")

    def create_tables(self) -> None:
        """Create all database tables."""
        try:
            self.logger.info("Creating database tables")
            SQLModel.metadata.create_all(self.engine)
            self.logger.info("Database tables created successfully")
        except Exception as e:
            self.logger.error(
                "Failed to create database tables",
                error=str(e),
                error_type=type(e).__name__
            )
            raise DatabaseError(f"Failed to create database tables: {e}")

    def get_connection_info(self) -> Dict[str, Any]:
        """Get database connection information for monitoring."""
        if self._engine is None:
            return {"status": "not_initialized"}

        pool = self._engine.pool
        return {
            "status": "connected",
            "database_url": self._mask_database_url(self.config.url),
            "pool_size": getattr(pool, 'size', lambda: 'N/A')(),
            "checked_in": getattr(pool, 'checkedin', lambda: 'N/A')(),
            "checked_out": getattr(pool, 'checkedout', lambda: 'N/A')(),
            "overflow": getattr(pool, 'overflow', lambda: 'N/A')(),
        }
