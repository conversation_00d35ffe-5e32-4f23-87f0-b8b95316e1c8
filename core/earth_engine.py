"""
CarbonLedger Google Earth Engine Integration

This module provides secure, production-ready integration with Google Earth Engine.
Implements proper authentication, error handling, rate limiting, and monitoring.

Architectural Decision: Centralized Earth Engine management
- Single point of authentication and configuration
- Consistent error handling across all satellite operations
- Built-in rate limiting and retry logic
- Comprehensive logging and monitoring
- Secure credential management
"""

import json
import time
import tempfile
from typing import Optional, Dict, Any, Tuple
from pathlib import Path
from contextlib import contextmanager

import ee
from google.auth.exceptions import RefreshError
from google.api_core.exceptions import GoogleAPIError

from .config import GoogleEarthEngineSettings
from .logging import get_logger, log_performance, LoggerMixin


class EarthEngineError(Exception):
    """Base exception for Earth Engine operations."""
    pass


class AuthenticationError(EarthEngineError):
    """Raised when Earth Engine authentication fails."""
    pass


class RateLimitError(EarthEngineError):
    """Raised when Earth Engine rate limits are exceeded."""
    pass


class DataQualityError(EarthEngineError):
    """Raised when satellite data quality is insufficient."""
    pass


class EarthEngineManager(LoggerMixin):
    """
    Manages Google Earth Engine authentication and operations.
    
    This class provides a secure, production-ready interface to Google Earth Engine
    with proper error handling, rate limiting, and monitoring.
    """
    
    def __init__(self, config: GoogleEarthEngineSettings):
        self.config = config
        self._authenticated = False
        self._last_request_time = 0
        self._request_count = 0
        self._request_window_start = time.time()
        
        # Initialize authentication
        self._initialize_authentication()
    
    def _initialize_authentication(self) -> None:
        """Initialize Earth Engine authentication with proper error handling."""
        try:
            self.logger.info("Initializing Google Earth Engine authentication")
            
            if self.config.service_account_key_json:
                self._authenticate_with_json_key()
            elif self.config.service_account_key_path:
                self._authenticate_with_key_file()
            else:
                self._authenticate_default()
            
            # Test authentication
            self._test_authentication()
            self._authenticated = True
            
            self.logger.info(
                "Google Earth Engine authentication successful",
                project_id=self.config.project_id
            )
            
        except Exception as e:
            self.logger.error(
                "Google Earth Engine authentication failed",
                error=str(e),
                error_type=type(e).__name__
            )
            raise AuthenticationError(f"Failed to authenticate with Google Earth Engine: {e}")
    
    def _authenticate_with_json_key(self) -> None:
        """Authenticate using JSON key string (for cloud deployment)."""
        try:
            # Parse JSON key
            key_data = json.loads(self.config.service_account_key_json)
            
            # Create temporary file for the key
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(key_data, f)
                temp_key_path = f.name
            
            try:
                # Initialize with service account
                credentials = ee.ServiceAccountCredentials(
                    email=key_data.get('client_email'),
                    key_file=temp_key_path
                )
                ee.Initialize(credentials, project=self.config.project_id)
            finally:
                # Clean up temporary file
                Path(temp_key_path).unlink(missing_ok=True)
                
        except json.JSONDecodeError as e:
            raise AuthenticationError(f"Invalid service account JSON key: {e}")
        except Exception as e:
            raise AuthenticationError(f"Failed to authenticate with JSON key: {e}")
    
    def _authenticate_with_key_file(self) -> None:
        """Authenticate using service account key file."""
        try:
            key_path = Path(self.config.service_account_key_path)
            
            if not key_path.exists():
                raise AuthenticationError(f"Service account key file not found: {key_path}")
            
            # Read and validate key file
            with open(key_path, 'r') as f:
                key_data = json.load(f)
            
            credentials = ee.ServiceAccountCredentials(
                email=key_data.get('client_email'),
                key_file=str(key_path)
            )
            ee.Initialize(credentials, project=self.config.project_id)
            
        except json.JSONDecodeError as e:
            raise AuthenticationError(f"Invalid service account key file: {e}")
        except Exception as e:
            raise AuthenticationError(f"Failed to authenticate with key file: {e}")
    
    def _authenticate_default(self) -> None:
        """Authenticate using default credentials (for development)."""
        try:
            self.logger.warning(
                "Using default Earth Engine authentication - not recommended for production"
            )
            ee.Initialize(project=self.config.project_id)
        except Exception as e:
            raise AuthenticationError(f"Failed to authenticate with default credentials: {e}")
    
    def _test_authentication(self) -> None:
        """Test Earth Engine authentication by making a simple request."""
        try:
            # Simple test to verify authentication works
            test_image = ee.Image("COPERNICUS/S2_SR_HARMONIZED/20230101T000000_20230101T000000_T01ABC")
            test_image.getInfo()  # This will fail if authentication is invalid
        except ee.EEException as e:
            if "Authentication" in str(e) or "credentials" in str(e).lower():
                raise AuthenticationError(f"Authentication test failed: {e}")
            # Other EE exceptions are acceptable for this test
        except Exception as e:
            self.logger.warning(
                "Authentication test inconclusive",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def _check_rate_limit(self) -> None:
        """Check and enforce rate limiting for Earth Engine requests."""
        current_time = time.time()
        
        # Reset counter if window has passed
        if current_time - self._request_window_start >= 60:  # 1 minute window
            self._request_count = 0
            self._request_window_start = current_time
        
        # Check if we're exceeding rate limits
        if self._request_count >= self.config.max_requests_per_minute:
            wait_time = 60 - (current_time - self._request_window_start)
            self.logger.warning(
                "Rate limit exceeded, waiting",
                wait_seconds=wait_time,
                requests_in_window=self._request_count
            )
            raise RateLimitError(f"Rate limit exceeded. Wait {wait_time:.1f} seconds.")
        
        # Increment request counter
        self._request_count += 1
        self._last_request_time = current_time
    
    @contextmanager
    def _earth_engine_operation(self, operation_name: str):
        """Context manager for Earth Engine operations with error handling."""
        if not self._authenticated:
            raise AuthenticationError("Earth Engine not authenticated")
        
        self._check_rate_limit()
        
        start_time = time.time()
        self.logger.debug(
            "Starting Earth Engine operation",
            operation=operation_name
        )
        
        try:
            yield
            duration = time.time() - start_time
            self.logger.debug(
                "Earth Engine operation completed",
                operation=operation_name,
                duration_seconds=round(duration, 3)
            )
            
        except ee.EEException as e:
            duration = time.time() - start_time
            self.logger.error(
                "Earth Engine operation failed",
                operation=operation_name,
                duration_seconds=round(duration, 3),
                error=str(e),
                error_type="EEException"
            )
            
            # Classify different types of EE errors
            error_str = str(e).lower()
            if "quota" in error_str or "rate" in error_str:
                raise RateLimitError(f"Earth Engine rate limit: {e}")
            elif "authentication" in error_str or "permission" in error_str:
                raise AuthenticationError(f"Earth Engine authentication error: {e}")
            else:
                raise EarthEngineError(f"Earth Engine operation failed: {e}")
        
        except GoogleAPIError as e:
            duration = time.time() - start_time
            self.logger.error(
                "Google API error during Earth Engine operation",
                operation=operation_name,
                duration_seconds=round(duration, 3),
                error=str(e),
                error_type="GoogleAPIError"
            )
            raise EarthEngineError(f"Google API error: {e}")
        
        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(
                "Unexpected error during Earth Engine operation",
                operation=operation_name,
                duration_seconds=round(duration, 3),
                error=str(e),
                error_type=type(e).__name__
            )
            raise EarthEngineError(f"Unexpected error: {e}")
    
    @log_performance("get_image_collection")
    def get_image_collection(
        self,
        collection_id: str,
        geometry: ee.Geometry,
        start_date: str,
        end_date: str,
        cloud_threshold: float = 20.0
    ) -> ee.ImageCollection:
        """
        Get filtered image collection with quality checks.
        
        Args:
            collection_id: Earth Engine collection ID
            geometry: Area of interest geometry
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            cloud_threshold: Maximum cloud cover percentage
        
        Returns:
            Filtered image collection
        
        Raises:
            DataQualityError: If insufficient data is available
        """
        with self._earth_engine_operation("get_image_collection"):
            collection = (ee.ImageCollection(collection_id)
                         .filterBounds(geometry)
                         .filterDate(start_date, end_date)
                         .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', cloud_threshold)))
            
            # Check data availability
            image_count = collection.size().getInfo()
            
            if image_count == 0:
                raise DataQualityError(
                    f"No images found for {collection_id} "
                    f"between {start_date} and {end_date} "
                    f"with cloud cover < {cloud_threshold}%"
                )
            
            self.logger.info(
                "Image collection retrieved",
                collection_id=collection_id,
                image_count=image_count,
                date_range=f"{start_date} to {end_date}",
                cloud_threshold=cloud_threshold
            )
            
            return collection
    
    def is_authenticated(self) -> bool:
        """Check if Earth Engine is properly authenticated."""
        return self._authenticated
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics."""
        return {
            "authenticated": self._authenticated,
            "requests_in_current_window": self._request_count,
            "window_start": self._request_window_start,
            "last_request_time": self._last_request_time,
            "max_requests_per_minute": self.config.max_requests_per_minute
        }
