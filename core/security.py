"""
CarbonLedger API Security and Middleware

This module provides comprehensive security features including rate limiting,
CORS configuration, request validation, and security headers.

Architectural Decision: Layered security approach
- Rate limiting to prevent abuse
- CORS configuration for frontend integration
- Security headers for protection against common attacks
- Request validation and sanitization
- Comprehensive logging for security monitoring
"""

import time
import uuid
from typing import Dict, Any, Optional, List
from fastapi import Request, Response, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.base import BaseHTTPMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import structlog

from .config import SecuritySettings
from .logging import get_logger, RequestContext


class SecurityError(Exception):
    """Base exception for security-related errors."""
    pass


class RateLimitExceededError(SecurityError):
    """Raised when rate limits are exceeded."""
    pass


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to all responses.
    
    Implements OWASP security headers recommendations to protect against
    common web vulnerabilities.
    """
    
    def __init__(self, app, config: SecuritySettings):
        super().__init__(app)
        self.config = config
        self.logger = get_logger(__name__, component="SecurityHeaders")
    
    async def dispatch(self, request: Request, call_next):
        """Add security headers to response."""
        response = await call_next(request)
        
        # Security headers
        security_headers = {
            # Prevent clickjacking
            "X-Frame-Options": "DENY",
            
            # Prevent MIME type sniffing
            "X-Content-Type-Options": "nosniff",
            
            # Enable XSS protection
            "X-XSS-Protection": "1; mode=block",
            
            # Referrer policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Content Security Policy (basic)
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self' https:; "
                "font-src 'self'; "
                "object-src 'none'; "
                "media-src 'self'; "
                "frame-src 'none';"
            ),
            
            # Permissions policy
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
        }
        
        # Add HSTS header for HTTPS
        if request.url.scheme == "https":
            security_headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains; preload"
            )
        
        # Apply headers
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for comprehensive request/response logging.
    
    Logs all HTTP requests and responses with security considerations,
    ensuring sensitive data is not logged.
    """
    
    def __init__(self, app, config: SecuritySettings):
        super().__init__(app)
        self.config = config
        self.logger = get_logger(__name__, component="RequestLogging")
    
    async def dispatch(self, request: Request, call_next):
        """Log request and response details."""
        # Generate request ID for tracing
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        # Extract request details (excluding sensitive data)
        request_details = {
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": self._filter_sensitive_headers(dict(request.headers)),
            "client_ip": get_remote_address(request),
            "user_agent": request.headers.get("user-agent", ""),
        }
        
        # Start request context for logging
        with RequestContext(request_id, **request_details):
            self.logger.info(
                "Request started",
                request_id=request_id,
                **request_details
            )
            
            try:
                # Process request
                response = await call_next(request)
                duration = time.time() - start_time
                
                # Log successful response
                self.logger.info(
                    "Request completed",
                    request_id=request_id,
                    status_code=response.status_code,
                    duration_seconds=round(duration, 3),
                    response_size=response.headers.get("content-length", "unknown")
                )
                
                # Add request ID to response headers for debugging
                response.headers["X-Request-ID"] = request_id
                
                return response
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Log error response
                self.logger.error(
                    "Request failed",
                    request_id=request_id,
                    duration_seconds=round(duration, 3),
                    error=str(e),
                    error_type=type(e).__name__
                )
                
                raise
    
    def _filter_sensitive_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Filter sensitive information from request headers."""
        sensitive_headers = {
            'authorization', 'cookie', 'x-api-key', 'x-auth-token',
            'x-access-token', 'x-csrf-token'
        }
        
        filtered = {}
        for key, value in headers.items():
            if key.lower() in sensitive_headers:
                filtered[key] = "***MASKED***"
            else:
                filtered[key] = value
        
        return filtered


class InputValidationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for input validation and sanitization.
    
    Provides basic protection against common injection attacks
    and malformed requests.
    """
    
    def __init__(self, app, config: SecuritySettings):
        super().__init__(app)
        self.config = config
        self.logger = get_logger(__name__, component="InputValidation")
        
        # Suspicious patterns that might indicate attacks
        self.suspicious_patterns = [
            # SQL injection patterns
            r"(\b(union|select|insert|update|delete|drop|create|alter)\b)",
            r"(--|#|/\*|\*/)",
            r"(\bor\b.*=.*\bor\b)",
            
            # XSS patterns
            r"(<script|</script>|javascript:|vbscript:|onload=|onerror=)",
            r"(<iframe|<object|<embed|<applet)",
            
            # Path traversal
            r"(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c)",
            
            # Command injection
            r"(;|\||&|`|\$\(|\${)",
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Validate and sanitize request input."""
        try:
            # Check request size
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB limit
                self.logger.warning(
                    "Request size too large",
                    content_length=content_length,
                    client_ip=get_remote_address(request)
                )
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail="Request size too large"
                )
            
            # Validate URL path
            self._validate_path(request.url.path, request)
            
            # Validate query parameters
            self._validate_query_params(dict(request.query_params), request)
            
            return await call_next(request)
            
        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(
                "Input validation error",
                error=str(e),
                error_type=type(e).__name__,
                client_ip=get_remote_address(request)
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request"
            )
    
    def _validate_path(self, path: str, request: Request) -> None:
        """Validate URL path for suspicious patterns."""
        import re
        
        for pattern in self.suspicious_patterns:
            if re.search(pattern, path, re.IGNORECASE):
                self.logger.warning(
                    "Suspicious pattern detected in path",
                    path=path,
                    pattern=pattern,
                    client_ip=get_remote_address(request)
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid request path"
                )
    
    def _validate_query_params(self, params: Dict[str, str], request: Request) -> None:
        """Validate query parameters for suspicious patterns."""
        import re
        
        for key, value in params.items():
            # Check parameter names and values
            for item in [key, value]:
                for pattern in self.suspicious_patterns:
                    if re.search(pattern, str(item), re.IGNORECASE):
                        self.logger.warning(
                            "Suspicious pattern detected in query parameters",
                            parameter=key,
                            pattern=pattern,
                            client_ip=get_remote_address(request)
                        )
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Invalid query parameters"
                        )


def setup_rate_limiting(config: SecuritySettings) -> Limiter:
    """
    Setup rate limiting for the API.
    
    Args:
        config: Security configuration
    
    Returns:
        Configured Limiter instance
    """
    limiter = Limiter(
        key_func=get_remote_address,
        default_limits=[f"{config.rate_limit_requests}/minute"]
    )
    
    return limiter


def setup_cors(app, config: SecuritySettings) -> None:
    """
    Setup CORS middleware for the API.
    
    Args:
        app: FastAPI application instance
        config: Security configuration
    """
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.cors_origins,
        allow_credentials=config.cors_allow_credentials,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Request-ID"],
    )


def setup_security_middleware(app, config: SecuritySettings) -> None:
    """
    Setup all security middleware for the API.
    
    Args:
        app: FastAPI application instance
        config: Security configuration
    """
    # Add middleware in reverse order (last added = first executed)
    
    # Input validation (first line of defense)
    app.add_middleware(InputValidationMiddleware, config=config)
    
    # Request logging
    app.add_middleware(RequestLoggingMiddleware, config=config)
    
    # Security headers (last to ensure they're always added)
    app.add_middleware(SecurityHeadersMiddleware, config=config)


def create_security_exception_handlers(app, limiter: Limiter) -> None:
    """
    Create exception handlers for security-related errors.
    
    Args:
        app: FastAPI application instance
        limiter: Rate limiter instance
    """
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
