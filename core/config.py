"""
CarbonLedger Configuration Management

This module provides type-safe, environment-based configuration management
using Pydantic Settings. It follows the principle of "configuration as code"
with proper validation and security considerations.

Architectural Decision: Using Pydantic Settings over simple environment variables
- Provides type safety and validation
- Centralizes configuration management
- Supports multiple environments (dev, staging, prod)
- Enables easy testing with configuration overrides
"""

import os
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration with environment-specific settings."""
    
    # SQLite for development, PostgreSQL for production
    url: str = Field(
        default="sqlite:///carbonledger.db",
        description="Database connection URL"
    )
    echo: bool = Field(
        default=False,
        description="Enable SQL query logging"
    )
    pool_size: int = Field(
        default=5,
        description="Database connection pool size"
    )
    max_overflow: int = Field(
        default=10,
        description="Maximum overflow connections"
    )
    
    class Config:
        env_prefix = "DB_"


class GoogleEarthEngineSettings(BaseSettings):
    """Google Earth Engine configuration with security considerations."""
    
    service_account_email: Optional[str] = Field(
        default=None,
        description="Service account email for Earth Engine authentication"
    )
    service_account_key_path: Optional[str] = Field(
        default=None,
        description="Path to service account key file"
    )
    service_account_key_json: Optional[str] = Field(
        default=None,
        description="Service account key as JSON string (for cloud deployment)"
    )
    project_id: Optional[str] = Field(
        default=None,
        description="Google Cloud Project ID"
    )
    
    # Rate limiting for Earth Engine API
    max_requests_per_minute: int = Field(
        default=50,
        description="Maximum Earth Engine requests per minute"
    )
    request_timeout_seconds: int = Field(
        default=30,
        description="Timeout for Earth Engine requests"
    )
    
    @validator('service_account_key_path')
    def validate_key_path(cls, v):
        """Validate that service account key file exists if path is provided."""
        if v and not os.path.exists(v):
            raise ValueError(f"Service account key file not found: {v}")
        return v
    
    class Config:
        env_prefix = "GEE_"


class SecuritySettings(BaseSettings):
    """Security configuration for API protection."""
    
    # JWT Configuration
    secret_key: str = Field(
        default="dev-secret-key-change-in-production",
        description="Secret key for JWT token signing"
    )
    algorithm: str = Field(
        default="HS256",
        description="JWT signing algorithm"
    )
    access_token_expire_minutes: int = Field(
        default=30,
        description="JWT access token expiration time"
    )
    
    # Rate Limiting
    rate_limit_requests: int = Field(
        default=100,
        description="Requests per minute per IP"
    )
    rate_limit_window: int = Field(
        default=60,
        description="Rate limit window in seconds"
    )
    
    # CORS Configuration
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        description="Allowed CORS origins"
    )
    cors_allow_credentials: bool = Field(
        default=True,
        description="Allow credentials in CORS requests"
    )
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        """Ensure secret key is secure in production."""
        if v == "dev-secret-key-change-in-production":
            import warnings
            warnings.warn(
                "Using default secret key. Change this in production!",
                UserWarning
            )
        if len(v) < 32:
            raise ValueError("Secret key must be at least 32 characters long")
        return v
    
    class Config:
        env_prefix = "SECURITY_"


class LoggingSettings(BaseSettings):
    """Logging configuration for structured logging."""
    
    level: str = Field(
        default="INFO",
        description="Logging level"
    )
    format: str = Field(
        default="json",
        description="Log format: json or console"
    )
    enable_request_logging: bool = Field(
        default=True,
        description="Enable HTTP request logging"
    )
    log_file_path: Optional[str] = Field(
        default=None,
        description="Path to log file (if file logging enabled)"
    )
    
    @validator('level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
        return v.upper()
    
    class Config:
        env_prefix = "LOG_"


class APISettings(BaseSettings):
    """API server configuration."""
    
    host: str = Field(
        default="0.0.0.0",
        description="API server host"
    )
    port: int = Field(
        default=8000,
        description="API server port"
    )
    reload: bool = Field(
        default=False,
        description="Enable auto-reload in development"
    )
    workers: int = Field(
        default=1,
        description="Number of worker processes"
    )
    
    # API Metadata
    title: str = Field(
        default="CarbonLedger API",
        description="API title"
    )
    description: str = Field(
        default="Satellite-verified carbon credit platform",
        description="API description"
    )
    version: str = Field(
        default="1.0.0",
        description="API version"
    )
    
    class Config:
        env_prefix = "API_"


class Settings(BaseSettings):
    """Main application settings combining all configuration sections."""
    
    # Environment
    environment: str = Field(
        default="development",
        description="Application environment: development, staging, production"
    )
    debug: bool = Field(
        default=True,
        description="Enable debug mode"
    )
    
    # Sub-configurations
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    gee: GoogleEarthEngineSettings = Field(default_factory=GoogleEarthEngineSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    api: APISettings = Field(default_factory=APISettings)
    
    @validator('environment')
    def validate_environment(cls, v):
        """Validate environment setting."""
        valid_envs = ["development", "staging", "production"]
        if v not in valid_envs:
            raise ValueError(f"Invalid environment. Must be one of: {valid_envs}")
        return v
    
    @validator('debug')
    def validate_debug_in_production(cls, v, values):
        """Ensure debug is disabled in production."""
        if values.get('environment') == 'production' and v:
            raise ValueError("Debug mode must be disabled in production")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """
    Get application settings.
    
    This function provides a way to override settings for testing
    and enables dependency injection in FastAPI.
    """
    return settings
