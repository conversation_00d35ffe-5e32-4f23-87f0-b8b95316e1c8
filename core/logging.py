"""
CarbonLedger Structured Logging System

This module provides structured logging with security and performance considerations.
Uses structlog for consistent, machine-readable logs that support monitoring and debugging.

Architectural Decision: Structured logging over simple print statements
- Enables log aggregation and analysis
- Provides consistent log format across the application
- Supports different output formats (JSON for production, console for development)
- Includes security considerations (no sensitive data logging)
"""

import sys
import logging
import structlog
from typing import Any, Dict, Optional
from pathlib import Path

from .config import LoggingSettings


class SecurityFilter(logging.Filter):
    """
    Filter to prevent logging of sensitive information.
    
    This filter removes or masks sensitive data from log records to prevent
    accidental exposure of secrets, API keys, or personal information.
    """
    
    SENSITIVE_KEYS = {
        'password', 'secret', 'key', 'token', 'auth', 'credential',
        'private', 'confidential', 'api_key', 'service_account_key'
    }
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter sensitive information from log records."""
        if hasattr(record, 'msg') and isinstance(record.msg, dict):
            record.msg = self._mask_sensitive_data(record.msg)
        
        if hasattr(record, 'args') and record.args:
            record.args = tuple(
                self._mask_sensitive_data(arg) if isinstance(arg, dict) else arg
                for arg in record.args
            )
        
        return True
    
    def _mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Mask sensitive data in dictionaries."""
        if not isinstance(data, dict):
            return data
        
        masked_data = {}
        for key, value in data.items():
            key_lower = key.lower()
            
            # Check if key contains sensitive information
            if any(sensitive in key_lower for sensitive in self.SENSITIVE_KEYS):
                masked_data[key] = "***MASKED***"
            elif isinstance(value, dict):
                masked_data[key] = self._mask_sensitive_data(value)
            else:
                masked_data[key] = value
        
        return masked_data


def add_request_id(logger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add request ID to log entries for request tracing.
    
    This processor adds a request ID to all log entries within a request context,
    enabling distributed tracing and request correlation.
    """
    # In a real application, this would extract request ID from context
    # For now, we'll add a placeholder that can be enhanced later
    if 'request_id' not in event_dict:
        event_dict['request_id'] = getattr(structlog.contextvars, 'request_id', None)
    
    return event_dict


def add_service_context(logger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add service context information to log entries."""
    event_dict.update({
        'service': 'carbonledger-api',
        'component': event_dict.get('component', 'unknown'),
        'version': '1.0.0'  # This should come from config in a real app
    })
    return event_dict


def setup_logging(config: LoggingSettings) -> None:
    """
    Configure structured logging for the application.
    
    Args:
        config: Logging configuration settings
    """
    # Configure structlog processors
    processors = [
        structlog.contextvars.merge_contextvars,
        add_request_id,
        add_service_context,
        structlog.processors.TimeStamper(fmt="ISO"),
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
    ]
    
    # Add appropriate final processor based on format
    if config.format.lower() == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.extend([
            structlog.dev.ConsoleRenderer(colors=True),
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, config.level)
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, config.level),
    )
    
    # Add security filter to all handlers
    security_filter = SecurityFilter()
    for handler in logging.root.handlers:
        handler.addFilter(security_filter)
    
    # Configure file logging if specified
    if config.log_file_path:
        _setup_file_logging(config, security_filter)
    
    # Configure request logging
    if config.enable_request_logging:
        _setup_request_logging(config)


def _setup_file_logging(config: LoggingSettings, security_filter: SecurityFilter) -> None:
    """Setup file-based logging with rotation."""
    from logging.handlers import RotatingFileHandler
    
    log_path = Path(config.log_file_path)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    file_handler = RotatingFileHandler(
        filename=log_path,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    
    file_handler.setLevel(getattr(logging, config.level))
    file_handler.addFilter(security_filter)
    
    # Use JSON format for file logs
    formatter = logging.Formatter('%(message)s')
    file_handler.setFormatter(formatter)
    
    logging.root.addHandler(file_handler)


def _setup_request_logging(config: LoggingSettings) -> None:
    """Setup HTTP request logging."""
    # Configure uvicorn access logging
    uvicorn_access = logging.getLogger("uvicorn.access")
    uvicorn_access.setLevel(getattr(logging, config.level))
    
    # Configure FastAPI request logging
    # This will be enhanced when we add the FastAPI middleware


def get_logger(name: str, **context) -> structlog.BoundLogger:
    """
    Get a logger instance with optional context.
    
    Args:
        name: Logger name (typically __name__)
        **context: Additional context to bind to the logger
    
    Returns:
        Configured structlog logger instance
    """
    logger = structlog.get_logger(name)
    
    if context:
        logger = logger.bind(**context)
    
    return logger


class LoggerMixin:
    """
    Mixin class to add logging capabilities to any class.
    
    This mixin provides a consistent way to add logging to classes
    with automatic component naming and context binding.
    """
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get a logger instance for this class."""
        if not hasattr(self, '_logger'):
            component_name = self.__class__.__name__
            self._logger = get_logger(
                self.__class__.__module__,
                component=component_name
            )
        return self._logger


# Performance monitoring decorator
def log_performance(operation_name: str):
    """
    Decorator to log operation performance metrics.
    
    Args:
        operation_name: Name of the operation being measured
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            
            logger = get_logger(__name__, operation=operation_name)
            start_time = time.time()
            
            try:
                logger.info("Operation started", operation=operation_name)
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                logger.info(
                    "Operation completed successfully",
                    operation=operation_name,
                    duration_seconds=round(duration, 3)
                )
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(
                    "Operation failed",
                    operation=operation_name,
                    duration_seconds=round(duration, 3),
                    error=str(e),
                    error_type=type(e).__name__
                )
                raise
        
        return wrapper
    return decorator


# Context manager for request tracing
class RequestContext:
    """Context manager for request-scoped logging context."""
    
    def __init__(self, request_id: str, **context):
        self.request_id = request_id
        self.context = context
    
    def __enter__(self):
        structlog.contextvars.clear_contextvars()
        structlog.contextvars.bind_contextvars(
            request_id=self.request_id,
            **self.context
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        structlog.contextvars.clear_contextvars()
