"""
Blockchain Integration Module for CarbonLedger

This module provides blockchain integration for carbon credit utility tokens
using Polygon network and ERC-1155 smart contracts.

Architectural Decision: Polygon for low-cost, Ethereum-compatible blockchain
Regulatory Positioning: Utility tokens for carbon retirement services
Security Focus: Multi-signature operations and gas management
"""

import json
import os
from typing import Dict, Any, Optional, List
from decimal import Decimal
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from web3 import Web3
from web3.middleware.proof_of_authority import ExtraDataToPOAMiddleware
from eth_account import Account
from eth_typing import Address, HexStr

from .config import BlockchainSettings
from .logging import get_logger, LoggerMixin
from .exceptions import BlockchainError, ConfigurationError


class NetworkType(str, Enum):
    """Supported blockchain networks."""
    MUMBAI_TESTNET = "mumbai"
    POLYGON_MAINNET = "polygon"
    LOCAL_HARDHAT = "hardhat"


@dataclass
class TokenMetadata:
    """Carbon credit token metadata structure."""
    project_name: str
    project_description: str
    verification_id: str
    satellite_data: str
    total_supply: int
    project_developer: str
    verification_timestamp: int


@dataclass
class RetirementRecord:
    """Carbon credit retirement record."""
    token_id: int
    amount: int
    retiree: str
    timestamp: int
    reason: str
    beneficiary: str
    transaction_hash: str


class BlockchainManager(LoggerMixin):
    """
    Blockchain manager for carbon credit utility tokens.
    
    Handles smart contract interactions, token minting, retirement,
    and verification updates on Polygon network.
    """
    
    def __init__(self, settings: BlockchainSettings):
        self.settings = settings
        self.w3: Optional[Web3] = None
        self.contract = None
        self.account = None
        
        # Contract ABI (simplified for key functions)
        self.contract_abi = self._load_contract_abi()
        
        # Initialize connection
        self._initialize_connection()
    
    def _initialize_connection(self) -> None:
        """Initialize Web3 connection and contract instance."""
        try:
            # Connect to blockchain network
            if self.settings.network_type == NetworkType.MUMBAI_TESTNET:
                rpc_url = self.settings.mumbai_rpc_url
            elif self.settings.network_type == NetworkType.POLYGON_MAINNET:
                rpc_url = self.settings.polygon_rpc_url
            else:
                rpc_url = self.settings.local_rpc_url
            
            self.w3 = Web3(Web3.HTTPProvider(rpc_url))
            
            # Add PoA middleware for Polygon networks
            if self.settings.network_type in [NetworkType.MUMBAI_TESTNET, NetworkType.POLYGON_MAINNET]:
                self.w3.middleware_onion.inject(ExtraDataToPOAMiddleware, layer=0)
            
            # Verify connection
            if not self.w3.is_connected():
                raise BlockchainError(f"Failed to connect to {self.settings.network_type} network")
            
            # Load account if private key provided
            if self.settings.private_key:
                self.account = Account.from_key(self.settings.private_key)
                self.logger.info(
                    "Blockchain account loaded",
                    address=self.account.address,
                    network=self.settings.network_type.value
                )
            
            # Initialize contract if address provided
            if self.settings.contract_address:
                self.contract = self.w3.eth.contract(
                    address=self.settings.contract_address,
                    abi=self.contract_abi
                )
                self.logger.info(
                    "Smart contract initialized",
                    contract_address=self.settings.contract_address,
                    network=self.settings.network_type.value
                )
            
            self.logger.info(
                "Blockchain connection initialized",
                network=self.settings.network_type.value,
                block_number=self.w3.eth.block_number
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to initialize blockchain connection",
                error=str(e),
                error_type=type(e).__name__
            )
            raise BlockchainError(f"Blockchain initialization failed: {e}")
    
    def _load_contract_abi(self) -> List[Dict[str, Any]]:
        """Load smart contract ABI."""
        # Simplified ABI for key functions
        return [
            {
                "inputs": [
                    {"name": "to", "type": "address"},
                    {"name": "amount", "type": "uint256"},
                    {"name": "projectName", "type": "string"},
                    {"name": "projectDescription", "type": "string"},
                    {"name": "verificationId", "type": "string"},
                    {"name": "satelliteData", "type": "string"}
                ],
                "name": "mintCarbonCredit",
                "outputs": [{"name": "", "type": "uint256"}],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {"name": "tokenId", "type": "uint256"},
                    {"name": "amount", "type": "uint256"},
                    {"name": "reason", "type": "string"},
                    {"name": "beneficiary", "type": "string"}
                ],
                "name": "retireCarbonCredit",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {"name": "tokenId", "type": "uint256"}
                ],
                "name": "getCarbonCredit",
                "outputs": [
                    {
                        "components": [
                            {"name": "projectName", "type": "string"},
                            {"name": "projectDescription", "type": "string"},
                            {"name": "totalSupply", "type": "uint256"},
                            {"name": "retiredAmount", "type": "uint256"},
                            {"name": "verificationTimestamp", "type": "uint256"},
                            {"name": "verificationId", "type": "string"},
                            {"name": "satelliteData", "type": "string"},
                            {"name": "isActive", "type": "bool"},
                            {"name": "projectDeveloper", "type": "address"}
                        ],
                        "name": "",
                        "type": "tuple"
                    }
                ],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [
                    {"name": "account", "type": "address"},
                    {"name": "id", "type": "uint256"}
                ],
                "name": "balanceOf",
                "outputs": [{"name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [
                    {"name": "user", "type": "address"},
                    {"name": "tokenId", "type": "uint256"}
                ],
                "name": "getRetiredBalance",
                "outputs": [{"name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]
    
    def mint_carbon_credit(
        self,
        to_address: str,
        amount: int,
        project_name: str,
        project_description: str,
        verification_id: str,
        satellite_data: str
    ) -> Dict[str, Any]:
        """
        Mint new carbon credit tokens.
        
        Args:
            to_address: Address to mint tokens to
            amount: Number of carbon credits to mint
            project_name: Name of the carbon project
            project_description: Description of the project
            verification_id: Unique verification ID
            satellite_data: IPFS hash or verification details
        
        Returns:
            Transaction result with token ID
        """
        if not self.contract or not self.account:
            raise BlockchainError("Contract or account not initialized")
        
        try:
            self.logger.info(
                "Minting carbon credits",
                to_address=to_address,
                amount=amount,
                project_name=project_name,
                verification_id=verification_id
            )
            
            # Prepare transaction
            function = self.contract.functions.mintCarbonCredit(
                to_address,
                amount,
                project_name,
                project_description,
                verification_id,
                satellite_data
            )
            
            # Estimate gas
            gas_estimate = function.estimate_gas({'from': self.account.address})
            gas_limit = int(gas_estimate * 1.2)  # Add 20% buffer
            
            # Get current gas price
            gas_price = self.w3.eth.gas_price
            
            # Build transaction
            transaction = function.build_transaction({
                'from': self.account.address,
                'gas': gas_limit,
                'gasPrice': gas_price,
                'nonce': self.w3.eth.get_transaction_count(self.account.address),
            })
            
            # Sign and send transaction
            signed_txn = self.account.sign_transaction(transaction)
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            # Wait for confirmation
            receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash)
            
            # Extract token ID from logs (simplified)
            token_id = None
            if receipt.logs:
                # In a real implementation, we'd parse the event logs properly
                token_id = 1  # Placeholder
            
            result = {
                "success": True,
                "transaction_hash": receipt.transactionHash.hex(),
                "token_id": token_id,
                "gas_used": receipt.gasUsed,
                "gas_price": gas_price,
                "cost_wei": receipt.gasUsed * gas_price,
                "cost_matic": self.w3.from_wei(receipt.gasUsed * gas_price, 'ether'),
                "block_number": receipt.blockNumber
            }
            
            self.logger.info(
                "Carbon credits minted successfully",
                transaction_hash=result["transaction_hash"],
                token_id=token_id,
                gas_used=receipt.gasUsed,
                cost_matic=float(result["cost_matic"])
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Failed to mint carbon credits",
                error=str(e),
                error_type=type(e).__name__,
                to_address=to_address,
                amount=amount
            )
            raise BlockchainError(f"Minting failed: {e}")
    
    def retire_carbon_credit(
        self,
        token_id: int,
        amount: int,
        reason: str,
        beneficiary: str = ""
    ) -> Dict[str, Any]:
        """
        Retire carbon credit tokens (permanent, non-transferable).
        
        Args:
            token_id: Token ID to retire
            amount: Amount to retire
            reason: Reason for retirement
            beneficiary: On behalf of whom (optional)
        
        Returns:
            Transaction result
        """
        if not self.contract or not self.account:
            raise BlockchainError("Contract or account not initialized")
        
        try:
            self.logger.info(
                "Retiring carbon credits",
                token_id=token_id,
                amount=amount,
                reason=reason,
                beneficiary=beneficiary
            )
            
            # Prepare transaction
            function = self.contract.functions.retireCarbonCredit(
                token_id,
                amount,
                reason,
                beneficiary
            )
            
            # Estimate gas and build transaction
            gas_estimate = function.estimate_gas({'from': self.account.address})
            gas_limit = int(gas_estimate * 1.2)
            gas_price = self.w3.eth.gas_price
            
            transaction = function.build_transaction({
                'from': self.account.address,
                'gas': gas_limit,
                'gasPrice': gas_price,
                'nonce': self.w3.eth.get_transaction_count(self.account.address),
            })
            
            # Sign and send
            signed_txn = self.account.sign_transaction(transaction)
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash)
            
            result = {
                "success": True,
                "transaction_hash": receipt.transactionHash.hex(),
                "gas_used": receipt.gasUsed,
                "cost_matic": self.w3.from_wei(receipt.gasUsed * gas_price, 'ether'),
                "block_number": receipt.blockNumber
            }
            
            self.logger.info(
                "Carbon credits retired successfully",
                transaction_hash=result["transaction_hash"],
                token_id=token_id,
                amount=amount
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Failed to retire carbon credits",
                error=str(e),
                token_id=token_id,
                amount=amount
            )
            raise BlockchainError(f"Retirement failed: {e}")
    
    def get_token_metadata(self, token_id: int) -> Optional[TokenMetadata]:
        """Get carbon credit token metadata."""
        if not self.contract:
            raise BlockchainError("Contract not initialized")
        
        try:
            result = self.contract.functions.getCarbonCredit(token_id).call()
            
            return TokenMetadata(
                project_name=result[0],
                project_description=result[1],
                verification_id=result[5],
                satellite_data=result[6],
                total_supply=result[2],
                project_developer=result[8],
                verification_timestamp=result[4]
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to get token metadata",
                error=str(e),
                token_id=token_id
            )
            return None
    
    def get_balance(self, address: str, token_id: int) -> int:
        """Get token balance for an address."""
        if not self.contract:
            raise BlockchainError("Contract not initialized")
        
        try:
            return self.contract.functions.balanceOf(address, token_id).call()
        except Exception as e:
            self.logger.error(
                "Failed to get balance",
                error=str(e),
                address=address,
                token_id=token_id
            )
            return 0
    
    def get_retired_balance(self, address: str, token_id: int) -> int:
        """Get retired token balance for an address."""
        if not self.contract:
            raise BlockchainError("Contract not initialized")
        
        try:
            return self.contract.functions.getRetiredBalance(address, token_id).call()
        except Exception as e:
            self.logger.error(
                "Failed to get retired balance",
                error=str(e),
                address=address,
                token_id=token_id
            )
            return 0
    
    def estimate_gas_cost(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Estimate gas cost for blockchain operations."""
        if not self.w3:
            raise BlockchainError("Web3 not initialized")
        
        try:
            gas_price = self.w3.eth.gas_price
            
            # Estimated gas limits for different operations
            gas_estimates = {
                "mint": 150000,
                "retire": 100000,
                "transfer": 50000
            }
            
            gas_limit = gas_estimates.get(operation, 100000)
            cost_wei = gas_limit * gas_price
            cost_matic = self.w3.from_wei(cost_wei, 'ether')
            
            return {
                "operation": operation,
                "gas_limit": gas_limit,
                "gas_price_gwei": self.w3.from_wei(gas_price, 'gwei'),
                "cost_wei": cost_wei,
                "cost_matic": float(cost_matic),
                "cost_usd": float(cost_matic) * self.settings.matic_usd_price
            }
            
        except Exception as e:
            self.logger.error(
                "Failed to estimate gas cost",
                error=str(e),
                operation=operation
            )
            raise BlockchainError(f"Gas estimation failed: {e}")
    
    def health_check(self) -> Dict[str, Any]:
        """Check blockchain connection health."""
        try:
            if not self.w3:
                return {"status": "unhealthy", "error": "Web3 not initialized"}
            
            if not self.w3.is_connected():
                return {"status": "unhealthy", "error": "Not connected to network"}
            
            block_number = self.w3.eth.block_number
            
            result = {
                "status": "healthy",
                "network": self.settings.network_type.value,
                "block_number": block_number,
                "account_address": self.account.address if self.account else None,
                "contract_address": self.settings.contract_address
            }
            
            # Check account balance if available
            if self.account:
                balance_wei = self.w3.eth.get_balance(self.account.address)
                result["account_balance_matic"] = float(self.w3.from_wei(balance_wei, 'ether'))
            
            return result
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
