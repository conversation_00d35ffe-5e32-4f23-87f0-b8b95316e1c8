# CarbonLedger Frontend

A modern, responsive React dashboard for the CarbonLedger satellite verification platform.

## 🌟 Features

### Core Functionality
- **Dashboard Overview**: Real-time system metrics and verification statistics
- **Project Verification**: Interactive form for submitting carbon credit projects
- **Results Analysis**: Comprehensive verification results with confidence scoring
- **System Health**: Live monitoring of all system components
- **Analytics**: Advanced charts and trends analysis
- **Settings**: Configurable system preferences

### Technical Features
- **Modern React**: Built with Next.js 14 and TypeScript
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Real-time Updates**: Live data fetching and auto-refresh capabilities
- **Interactive Charts**: Rich data visualization with Recharts
- **Smooth Animations**: Framer Motion for enhanced UX
- **Error Handling**: Comprehensive error boundaries and user feedback
- **API Integration**: Seamless backend connectivity with fallback mock data

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- CarbonLedger backend API (optional - mock data available)

### Installation

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   # Create .env.local file
   echo "NEXT_PUBLIC_API_URL=http://127.0.0.1:8001" > .env.local
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open in browser**
   ```
   http://localhost:3000
   ```

## 📱 Pages & Components

### Dashboard (`/`)
- **Quick Actions**: Fast access to common tasks
- **System Statistics**: Key performance metrics
- **Recent Verifications**: Latest project results
- **Verification Trends**: Historical data visualization
- **System Status**: Real-time health monitoring

### Verification (`/verify`)
- **Project Form**: Comprehensive project details input
- **Coordinate Selection**: Geographic boundary definition with presets
- **Real-time Processing**: Live verification status updates
- **Results Display**: Detailed analysis with confidence scores
- **Export Options**: Download reports and share results

### Analytics (`/analytics`)
- **Trend Analysis**: Time-series verification data
- **Regional Distribution**: Geographic verification patterns
- **Confidence Metrics**: Quality score distributions
- **Deforestation Tracking**: Environmental impact monitoring

### Health Monitoring (`/health`)
- **Component Status**: Individual service health checks
- **Performance Metrics**: Response times and availability
- **Auto-refresh**: Configurable live updates
- **System Information**: Version and environment details

### Settings (`/settings`)
- **Notifications**: Email and push notification preferences
- **API Configuration**: Rate limits and timeout settings
- **Verification Settings**: Confidence thresholds and retry options
- **Display Options**: Theme, language, and timezone preferences

## 🛠 Technology Stack

### Frontend Framework
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Framer Motion**: Animation library

### UI Components
- **Headless UI**: Accessible component primitives
- **Heroicons**: Beautiful SVG icons
- **React Hook Form**: Form state management
- **Zod**: Schema validation

### Data Visualization
- **Recharts**: React charting library
- **Custom Charts**: Line, bar, area, and pie charts
- **Interactive Tooltips**: Rich data exploration

### Development Tools
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **TypeScript**: Static type checking

## 🔧 Configuration

### Environment Variables
```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://127.0.0.1:8001

# Optional: Enable debug mode
NODE_ENV=development
```

### API Integration
The frontend automatically handles:
- **Live API**: Connects to running backend
- **Mock Data**: Fallback when API unavailable
- **Error Handling**: Graceful degradation
- **Loading States**: User feedback during requests

### Customization
- **Themes**: Light/dark mode support
- **Colors**: Tailwind color palette customization
- **Layout**: Responsive breakpoints
- **Components**: Modular and reusable design

## 📊 Data Flow

### Verification Process
1. **Form Submission**: User enters project details
2. **API Request**: Data sent to backend verification service
3. **Real-time Updates**: Progress indicators during processing
4. **Results Display**: Comprehensive analysis presentation
5. **Export Options**: Download and sharing capabilities

### Health Monitoring
1. **Auto-refresh**: Periodic health checks (30s intervals)
2. **Component Status**: Individual service monitoring
3. **Performance Metrics**: Response time tracking
4. **Alert System**: Visual status indicators

## 🎨 Design System

### Color Palette
- **Primary**: Green (#22c55e) - Environmental theme
- **Secondary**: Gray (#64748b) - Neutral elements
- **Accent**: Yellow (#eab308) - Warnings and highlights
- **Success**: Green (#10b981) - Positive states
- **Warning**: Yellow (#f59e0b) - Caution states
- **Error**: Red (#ef4444) - Error states

### Typography
- **Font Family**: Inter (system fallback)
- **Headings**: Bold weights for hierarchy
- **Body Text**: Regular weight for readability
- **Code**: JetBrains Mono for technical content

### Layout
- **Container**: Max-width 7xl (1280px)
- **Spacing**: Consistent 8px grid system
- **Breakpoints**: Mobile-first responsive design
- **Cards**: Elevated surfaces with subtle shadows

## 🧪 Testing

### Manual Testing Checklist
- [ ] Dashboard loads with mock data
- [ ] Verification form accepts valid coordinates
- [ ] Results display properly formatted data
- [ ] Health page shows system status
- [ ] Analytics charts render correctly
- [ ] Settings save and persist
- [ ] Mobile responsive design works
- [ ] Error states handle gracefully

### Browser Compatibility
- **Chrome**: 90+ ✅
- **Firefox**: 88+ ✅
- **Safari**: 14+ ✅
- **Edge**: 90+ ✅

## 🚀 Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Setup
```bash
# Production API URL
NEXT_PUBLIC_API_URL=https://api.carbonledger.com

# Disable debug features
NODE_ENV=production
```

### Performance Optimization
- **Code Splitting**: Automatic route-based splitting
- **Image Optimization**: Next.js built-in optimization
- **Bundle Analysis**: `npm run analyze` (if configured)
- **Caching**: Static asset caching strategies

## 📈 Performance Metrics

### Core Web Vitals
- **LCP**: < 2.5s (Largest Contentful Paint)
- **FID**: < 100ms (First Input Delay)
- **CLS**: < 0.1 (Cumulative Layout Shift)

### Bundle Size
- **Initial Load**: ~200KB gzipped
- **Route Chunks**: ~50KB average
- **Total Assets**: ~500KB including images

## 🔒 Security

### Data Protection
- **Input Validation**: Client-side form validation
- **XSS Prevention**: React built-in protection
- **CSRF Protection**: SameSite cookie policies
- **Content Security**: Strict CSP headers

### API Security
- **HTTPS Only**: Secure communication
- **Rate Limiting**: Client-side request throttling
- **Error Handling**: No sensitive data exposure
- **Authentication**: Ready for auth integration

## 🤝 Contributing

### Development Workflow
1. **Fork Repository**: Create personal fork
2. **Feature Branch**: Create feature-specific branch
3. **Development**: Implement changes with tests
4. **Pull Request**: Submit for review
5. **Code Review**: Address feedback
6. **Merge**: Deploy to production

### Code Standards
- **TypeScript**: Strict type checking
- **ESLint**: Consistent code style
- **Prettier**: Automatic formatting
- **Conventional Commits**: Structured commit messages

## 📞 Support

### Documentation
- **API Docs**: `/docs` endpoint on backend
- **Component Docs**: Inline TypeScript documentation
- **README**: This comprehensive guide

### Troubleshooting
- **API Connection**: Check backend server status
- **Build Issues**: Verify Node.js version compatibility
- **Performance**: Use React DevTools for profiling
- **Styling**: Check Tailwind CSS compilation

---

**CarbonLedger Frontend** - Built with ❤️ for environmental sustainability
