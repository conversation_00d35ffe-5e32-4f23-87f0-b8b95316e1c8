
# This file was generated by 'versioneer.py' (0.18) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-10-04T11:15:31-0500",
 "dirty": false,
 "error": null,
 "full-revisionid": "07f30a9c75e07a4b7095d0ec93ba4e963504f259",
 "version": "1.0.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
