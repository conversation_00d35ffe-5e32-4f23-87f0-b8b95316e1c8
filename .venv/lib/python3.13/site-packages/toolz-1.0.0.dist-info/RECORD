tlz/__init__.py,sha256=KH1pMvZOKkVJ_WAPX6vZUsC0KGKxGQBzxumWCzgvKSU,338
tlz/__pycache__/__init__.cpython-313.pyc,,
tlz/__pycache__/_build_tlz.cpython-313.pyc,,
tlz/_build_tlz.py,sha256=E5k4PUdwkW4j9dv94LbYrRRKXh4QSXQ-bB3HZz9H98o,3143
toolz-1.0.0.dist-info/AUTHORS.md,sha256=0aTPbfxkAVlbcP3TN0e8PM_5gTQeeaviWCkhFZBOU0c,1561
toolz-1.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
toolz-1.0.0.dist-info/LICENSE.txt,sha256=BTZkBXspWy8MEzIpGncQLwGgmdh5JuRJ4IoRfuqWYL8,1492
toolz-1.0.0.dist-info/METADATA,sha256=ZOudDI99GhK710umCBFVakKeDAufq8g002_5CtmUUHs,5078
toolz-1.0.0.dist-info/RECORD,,
toolz-1.0.0.dist-info/WHEEL,sha256=eOLhNAGa2EW3wWl_TU484h7q1UNgy0JXjjoqKoxAAQc,92
toolz-1.0.0.dist-info/top_level.txt,sha256=oesCzopYB9UXPR4nDzhYaf-TbpkQoR6gYSsXvZDgk3s,10
toolz/__init__.py,sha256=9c9B1vNRz_0LO4-t3tTDEnZGNBTYsLevrc9ELtaxPpw,381
toolz/__pycache__/__init__.cpython-313.pyc,,
toolz/__pycache__/_signatures.cpython-313.pyc,,
toolz/__pycache__/_version.cpython-313.pyc,,
toolz/__pycache__/compatibility.cpython-313.pyc,,
toolz/__pycache__/dicttoolz.cpython-313.pyc,,
toolz/__pycache__/functoolz.cpython-313.pyc,,
toolz/__pycache__/itertoolz.cpython-313.pyc,,
toolz/__pycache__/recipes.cpython-313.pyc,,
toolz/__pycache__/utils.cpython-313.pyc,,
toolz/_signatures.py,sha256=RI2GtVNSyYyXfn5vfXOqyHwXiblHF1L5pPjAHpbCU5I,20555
toolz/_version.py,sha256=_q8ML3hSZVD37bmLQ-R-Vaj4uh4phTUYOU3s5qkNaAE,497
toolz/compatibility.py,sha256=giOYcwv1TaOWDfB-C2JP2pFIJ5YZX9aP1s4UPzCQnw4,997
toolz/curried/__init__.py,sha256=U_d0nFlOWYN1DJc5n5J2rToauHU7QEe45CME1jRLT1Y,2700
toolz/curried/__pycache__/__init__.cpython-313.pyc,,
toolz/curried/__pycache__/exceptions.cpython-313.pyc,,
toolz/curried/__pycache__/operator.cpython-313.pyc,,
toolz/curried/exceptions.py,sha256=31Q9I-Ro-IMA3eRuDC5wSQXQBYlpdxonnJr4Fp0hquI,337
toolz/curried/operator.py,sha256=miFisWLO3TeVFS_pwnaZdSw4ZTHhNrLz-vGSc--7iRo,529
toolz/dicttoolz.py,sha256=sE8wlGNLezhdmkRqB2gQcxSbwbO6-c-4SVbY-yFjuoE,8955
toolz/functoolz.py,sha256=ecggVgwdndIqXdHDd28mgmBwkIDsGUM6YYR6ZML8wzY,29821
toolz/itertoolz.py,sha256=xnYy4W0kPp0gIP4bLbJRdvfxZMreSEtwNSxU0orBjmY,27618
toolz/recipes.py,sha256=r_j701Ug2_oO4bHunoy1xizk0N-m9QBwObyCITJuF0I,1256
toolz/sandbox/__init__.py,sha256=ysAYIaGROpbNy2-lYEeiVflJCqEOX9MWIHAIR9Bc6AA,68
toolz/sandbox/__pycache__/__init__.cpython-313.pyc,,
toolz/sandbox/__pycache__/core.cpython-313.pyc,,
toolz/sandbox/__pycache__/parallel.cpython-313.pyc,,
toolz/sandbox/core.py,sha256=aM_rF0S87FSvzU9qLdfmBeskyRwN985hCbnAbFOtzTw,4335
toolz/sandbox/parallel.py,sha256=BFteeYudRx1lQEAoDxsWlySCPryJHdcldnHpxCqrbjc,2966
toolz/tests/__pycache__/test_compatibility.cpython-313.pyc,,
toolz/tests/__pycache__/test_curried.cpython-313.pyc,,
toolz/tests/__pycache__/test_curried_doctests.cpython-313.pyc,,
toolz/tests/__pycache__/test_dicttoolz.cpython-313.pyc,,
toolz/tests/__pycache__/test_functoolz.cpython-313.pyc,,
toolz/tests/__pycache__/test_inspect_args.cpython-313.pyc,,
toolz/tests/__pycache__/test_itertoolz.cpython-313.pyc,,
toolz/tests/__pycache__/test_recipes.cpython-313.pyc,,
toolz/tests/__pycache__/test_serialization.cpython-313.pyc,,
toolz/tests/__pycache__/test_signatures.cpython-313.pyc,,
toolz/tests/__pycache__/test_tlz.cpython-313.pyc,,
toolz/tests/__pycache__/test_utils.cpython-313.pyc,,
toolz/tests/test_compatibility.py,sha256=Xbgk60ow92Oqbpmhspwy72T9YpUad_tu55Hj-4o9le4,261
toolz/tests/test_curried.py,sha256=LF2PkbaGVlN4J0c58T7ekTuWNlh0r7eRpV2oWzuf4jM,3724
toolz/tests/test_curried_doctests.py,sha256=9p_RwDKeG_8EXUoqKFAJ-zyp4KHp2w3lLFxOB5XXqww,274
toolz/tests/test_dicttoolz.py,sha256=7RVKgjun-OY9-FeQR9T1xJwM03wfedVYu6SHCsWwMZ0,9070
toolz/tests/test_functoolz.py,sha256=x6UyDY3_EPHn37WlJ0pYRQtynHYy9E_v55ri6_PHdog,20384
toolz/tests/test_inspect_args.py,sha256=S1HNvaukYlWQYFILtfzDMqGlt-HYOp-1m62POl9tGeo,16560
toolz/tests/test_itertoolz.py,sha256=HrfoHyUaehFw7Hy9CXYmOOkJIeIyySgkl9DBBXSpRUQ,19055
toolz/tests/test_recipes.py,sha256=hZ_nuGAOIafJrJwnnj9-JZnaRq9srIXyPQyzBdNS1FQ,820
toolz/tests/test_serialization.py,sha256=wsvAClD4eOxQdt_CWx2et_dD0Zy24iQypzDfTZiwnf0,5791
toolz/tests/test_signatures.py,sha256=X8K_rXS1OXI22yscj3zUhWxWWTVII5Cf2RJyXfh_2HA,2873
toolz/tests/test_tlz.py,sha256=LS5ICqieRLkjAUP-C5TjdVeld6S9OLXTYGgH85fYkWw,1593
toolz/tests/test_utils.py,sha256=2LIhS_9xXeAE1_onN868gZeAN8E3jTXtpnYF0W-L2OE,156
toolz/utils.py,sha256=JLlXt8x_JqSVevmLZPnt5bZJsdKMBJgJb5IwlcfOnsc,139
