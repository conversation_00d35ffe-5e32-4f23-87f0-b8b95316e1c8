from typing import Dict, Tuple, Callable, Union, Optional

__all__ = ['generate', 'construct', 'DsaKey', 'import_key' ]

RNG = Callable[[int], bytes]

class Dsa<PERSON>ey(object):
    def __init__(self, key_dict: Dict[str, int]) -> None: ...
    def has_private(self) -> bool: ...
    def can_encrypt(self) -> bool: ...  # legacy
    def can_sign(self) -> bool: ...     # legacy
    def public_key(self) -> DsaKey: ...
    def __eq__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __getstate__(self) -> None: ...
    def domain(self) -> Tuple[int, int, int]: ...
    def __repr__(self) -> str: ...
    def __getattr__(self, item: str) -> int: ...
    def export_key(self, format: Optional[str]="PEM", pkcs8: Optional[bool]=None, passphrase: Optional[str]=None,
                  protection: Optional[str]=None, randfunc: Optional[RNG]=None) -> bytes: ...
    # Backward-compatibility
    exportKey = export_key
    publickey = public_key

def generate(bits: int, randfunc: Optional[RNG]=None, domain: Optional[Tuple[int, int, int]]=None) -> DsaKey: ...
def construct(tup: Union[Tuple[int, int, int, int], Tuple[int, int, int, int, int]], consistency_check: Optional[bool]=True) -> DsaKey: ...
def import_key(extern_key: Union[str, bytes], passphrase: Optional[str]=None) -> DsaKey: ...
# Backward compatibility
importKey = import_key

oid: str
