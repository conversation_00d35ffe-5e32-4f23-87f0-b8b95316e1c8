{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/spanner.admin": {"description": "Administer your Spanner databases"}, "https://www.googleapis.com/auth/spanner.data": {"description": "View and manage the contents of your Spanner databases"}}}}, "basePath": "", "baseUrl": "https://spanner.googleapis.com/", "batchPath": "batch", "canonicalName": "<PERSON>nner", "description": "Cloud Spanner is a managed, mission-critical, globally consistent and scalable relational database service.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/spanner/", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://spanner.europe-west8.rep.googleapis.com/", "location": "europe-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.me-central2.rep.googleapis.com/", "location": "me-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-central2.rep.googleapis.com/", "location": "us-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-east4.rep.googleapis.com/", "location": "us-east4"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-east5.rep.googleapis.com/", "location": "us-east5"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-south1.rep.googleapis.com/", "location": "us-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-west1.rep.googleapis.com/", "location": "us-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-west2.rep.googleapis.com/", "location": "us-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-west3.rep.googleapis.com/", "location": "us-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-west4.rep.googleapis.com/", "location": "us-west4"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-west8.rep.googleapis.com/", "location": "us-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://spanner.us-east7.rep.googleapis.com/", "location": "us-east7"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "spanner:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://spanner.mtls.googleapis.com/", "name": "spanner", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"instanceConfigOperations": {"methods": {"list": {"description": "Lists the user-managed instance configuration long-running operations in the given project. An instance configuration operation has a name of the form `projects//instanceConfigs//operations/`. The long-running operation metadata field type `metadata.type_url` describes the type of the metadata. Operations returned include those that have completed/failed/canceled within the last 7 days, and pending operations. Operations returned are ordered by `operation.metadata.value.start_time` in descending order starting from the most recently started operation.", "flatPath": "v1/projects/{projectsId}/instanceConfigOperations", "httpMethod": "GET", "id": "spanner.projects.instanceConfigOperations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of returned operations. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string, a number, or a boolean. The comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`. Colon `:` is the contains operator. Filter rules are not case sensitive. The following fields in the Operation are eligible for filtering: * `name` - The name of the long-running operation * `done` - False if the operation is in progress, else true. * `metadata.@type` - the type of metadata. For example, the type string for CreateInstanceConfigMetadata is `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstanceConfigMetadata`. * `metadata.` - any field in metadata.value. `metadata.@type` must be specified first, if filtering on metadata fields. * `error` - Error associated with the long-running operation. * `response.@type` - the type of response. * `response.` - any field in response.value. You can combine multiple expressions by enclosing each expression in parentheses. By default, expressions are combined with AND logic. However, you can specify AND, OR, and NOT logic explicitly. Here are a few examples: * `done:true` - The operation is complete. * `(metadata.@type=` \\ `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstanceConfigMetadata) AND` \\ `(metadata.instance_config.name:custom-config) AND` \\ `(metadata.progress.start_time < \\\"2021-03-28T14:50:00Z\\\") AND` \\ `(error:*)` - Return operations where: * The operation's metadata type is CreateInstanceConfigMetadata. * The instance configuration name contains \"custom-config\". * The operation started before 2021-03-28T14:50:00Z. * The operation resulted in an error.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of operations to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListInstanceConfigOperationsResponse to the same `parent` and with the same `filter`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project of the instance configuration operations. Values are of the form `projects/`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instanceConfigOperations", "response": {"$ref": "ListInstanceConfigOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "instanceConfigs": {"methods": {"create": {"description": "Creates an instance configuration and begins preparing it to be used. The returned long-running operation can be used to track the progress of preparing the new instance configuration. The instance configuration name is assigned by the caller. If the named instance configuration already exists, `CreateInstanceConfig` returns `ALREADY_EXISTS`. Immediately after the request returns: * The instance configuration is readable via the API, with all requested attributes. The instance configuration's reconciling field is set to true. Its state is `CREATING`. While the operation is pending: * Cancelling the operation renders the instance configuration immediately unreadable via the API. * Except for deleting the creating resource, all other attempts to modify the instance configuration are rejected. Upon completion of the returned operation: * Instances can be created using the instance configuration. * The instance configuration's reconciling field becomes false. Its state becomes `READY`. The returned long-running operation will have a name of the format `/operations/` and can be used to track creation of the instance configuration. The metadata field type is CreateInstanceConfigMetadata. The response field type is InstanceConfig, if successful. Authorization requires `spanner.instanceConfigs.create` permission on the resource parent.", "flatPath": "v1/projects/{projectsId}/instanceConfigs", "httpMethod": "POST", "id": "spanner.projects.instanceConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in which to create the instance configuration. Values are of the form `projects/`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instanceConfigs", "request": {"$ref": "CreateInstanceConfigRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes the instance configuration. Deletion is only allowed when no instances are using the configuration. If any instances are using the configuration, returns `FAILED_PRECONDITION`. Only user-managed configurations can be deleted. Authorization requires `spanner.instanceConfigs.delete` permission on the resource name.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}", "httpMethod": "DELETE", "id": "spanner.projects.instanceConfigs.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Used for optimistic concurrency control as a way to help prevent simultaneous deletes of an instance configuration from overwriting each other. If not empty, the API only deletes the instance configuration when the etag provided matches the current status of the requested instance configuration. Otherwise, deletes the instance configuration without checking the current status of the requested instance configuration.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the instance configuration to be deleted. Values are of the form `projects//instanceConfigs/`", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "An option to validate, but not actually execute, a request, and provide the same response.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets information about a particular instance configuration.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}", "httpMethod": "GET", "id": "spanner.projects.instanceConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the requested instance configuration. Values are of the form `projects//instanceConfigs/`.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InstanceConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists the supported instance configurations for a given project. Returns both Google-managed configurations and user-managed configurations.", "flatPath": "v1/projects/{projectsId}/instanceConfigs", "httpMethod": "GET", "id": "spanner.projects.instanceConfigs.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Number of instance configurations to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListInstanceConfigsResponse.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project for which a list of supported instance configurations is requested. Values are of the form `projects/`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instanceConfigs", "response": {"$ref": "ListInstanceConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "patch": {"description": "Updates an instance configuration. The returned long-running operation can be used to track the progress of updating the instance. If the named instance configuration does not exist, returns `NOT_FOUND`. Only user-managed configurations can be updated. Immediately after the request returns: * The instance configuration's reconciling field is set to true. While the operation is pending: * Cancelling the operation sets its metadata's cancel_time. The operation is guaranteed to succeed at undoing all changes, after which point it terminates with a `CANCELLED` status. * All other attempts to modify the instance configuration are rejected. * Reading the instance configuration via the API continues to give the pre-request values. Upon completion of the returned operation: * Creating instances using the instance configuration uses the new values. * The new values of the instance configuration are readable via the API. * The instance configuration's reconciling field becomes false. The returned long-running operation will have a name of the format `/operations/` and can be used to track the instance configuration modification. The metadata field type is UpdateInstanceConfigMetadata. The response field type is InstanceConfig, if successful. Authorization requires `spanner.instanceConfigs.update` permission on the resource name.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}", "httpMethod": "PATCH", "id": "spanner.projects.instanceConfigs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "A unique identifier for the instance configuration. Values are of the form `projects//instanceConfigs/a-z*`. User instance configuration must start with `custom-`.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "UpdateInstanceConfigRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}, "resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "spanner.projects.instanceConfigs.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "spanner.projects.instanceConfigs.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/operations/{operationsId}", "httpMethod": "GET", "id": "spanner.projects.instanceConfigs.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/operations", "httpMethod": "GET", "id": "spanner.projects.instanceConfigs.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "ssdCaches": {"resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/ssdCaches/{ssdCachesId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "spanner.projects.instanceConfigs.ssdCaches.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/ssdCaches/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/ssdCaches/{ssdCachesId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "spanner.projects.instanceConfigs.ssdCaches.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/ssdCaches/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/ssdCaches/{ssdCachesId}/operations/{operationsId}", "httpMethod": "GET", "id": "spanner.projects.instanceConfigs.ssdCaches.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/ssdCaches/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instanceConfigs/{instanceConfigsId}/ssdCaches/{ssdCachesId}/operations", "httpMethod": "GET", "id": "spanner.projects.instanceConfigs.ssdCaches.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/instanceConfigs/[^/]+/ssdCaches/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}}}}}, "instances": {"methods": {"create": {"description": "Creates an instance and begins preparing it to begin serving. The returned long-running operation can be used to track the progress of preparing the new instance. The instance name is assigned by the caller. If the named instance already exists, `CreateInstance` returns `ALREADY_EXISTS`. Immediately upon completion of this request: * The instance is readable via the API, with all requested attributes but no allocated resources. Its state is `CREATING`. Until completion of the returned operation: * Cancelling the operation renders the instance immediately unreadable via the API. * The instance can be deleted. * All other attempts to modify the instance are rejected. Upon completion of the returned operation: * Billing for all successfully-allocated resources begins (some types may have lower than the requested levels). * Databases can be created in the instance. * The instance's allocated resource levels are readable via the API. * The instance's state becomes `READY`. The returned long-running operation will have a name of the format `/operations/` and can be used to track creation of the instance. The metadata field type is CreateInstanceMetadata. The response field type is Instance, if successful.", "flatPath": "v1/projects/{projectsId}/instances", "httpMethod": "POST", "id": "spanner.projects.instances.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in which to create the instance. Values are of the form `projects/`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instances", "request": {"$ref": "CreateInstanceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes an instance. Immediately upon completion of the request: * Billing ceases for all of the instance's reserved resources. Soon afterward: * The instance and *all of its databases* immediately and irrevocably disappear from the API. All data in the databases is permanently deleted.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the instance to be deleted. Values are of the form `projects//instances/`", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets information about a particular instance.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}", "httpMethod": "GET", "id": "spanner.projects.instances.get", "parameterOrder": ["name"], "parameters": {"fieldMask": {"description": "If field_mask is present, specifies the subset of Instance fields that should be returned. If absent, all Instance fields are returned.", "format": "google-fieldmask", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the requested instance. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Instance"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "getIamPolicy": {"description": "Gets the access control policy for an instance resource. Returns an empty policy if an instance exists but does not have a policy set. Authorization requires `spanner.instances.getIamPolicy` on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}:getIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being retrieved. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists all instances in the given project.", "flatPath": "v1/projects/{projectsId}/instances", "httpMethod": "GET", "id": "spanner.projects.instances.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression for filtering the results of the request. Filter rules are case insensitive. The fields eligible for filtering are: * `name` * `display_name` * `labels.key` where key is the name of a label Some examples of using filters are: * `name:*` --> The instance has a name. * `name:Howl` --> The instance's name contains the string \"howl\". * `name:HOWL` --> Equivalent to above. * `NAME:howl` --> Equivalent to above. * `labels.env:*` --> The instance has the label \"env\". * `labels.env:dev` --> The instance has the label \"env\" and the value of the label contains the string \"dev\". * `name:howl labels.env:dev` --> The instance's name contains \"howl\" and it has the label \"env\" with its value containing \"dev\".", "location": "query", "type": "string"}, "instanceDeadline": {"description": "Deadline used while retrieving metadata for instances. Instances whose metadata cannot be retrieved within this deadline will be added to unreachable in ListInstancesResponse.", "format": "google-datetime", "location": "query", "type": "string"}, "pageSize": {"description": "Number of instances to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListInstancesResponse.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project for which a list of instances is requested. Values are of the form `projects/`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instances", "response": {"$ref": "ListInstancesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "move": {"description": "Moves an instance to the target instance configuration. You can use the returned long-running operation to track the progress of moving the instance. `MoveInstance` returns `FAILED_PRECONDITION` if the instance meets any of the following criteria: * Is undergoing a move to a different instance configuration * Has backups * Has an ongoing update * Contains any CMEK-enabled databases * Is a free trial instance While the operation is pending: * All other attempts to modify the instance, including changes to its compute capacity, are rejected. * The following database and backup admin operations are rejected: * `DatabaseAdmin.CreateDatabase` * `DatabaseAdmin.UpdateDatabaseDdl` (disabled if default_leader is specified in the request.) * `DatabaseAdmin.RestoreDatabase` * `DatabaseAdmin.CreateBackup` * `DatabaseAdmin.CopyBackup` * Both the source and target instance configurations are subject to hourly compute and storage charges. * The instance might experience higher read-write latencies and a higher transaction abort rate. However, moving an instance doesn't cause any downtime. The returned long-running operation has a name of the format `/operations/` and can be used to track the move instance operation. The metadata field type is MoveInstanceMetadata. The response field type is Instance, if successful. Cancelling the operation sets its metadata's cancel_time. Cancellation is not immediate because it involves moving any data previously moved to the target instance configuration back to the original instance configuration. You can use this operation to track the progress of the cancellation. Upon successful completion of the cancellation, the operation terminates with `CANCELLED` status. If not cancelled, upon completion of the returned operation: * The instance successfully moves to the target instance configuration. * You are billed for compute and storage in target instance configuration. Authorization requires the `spanner.instances.update` permission on the resource instance. For more details, see [Move an instance](https://cloud.google.com/spanner/docs/move-instance).", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}:move", "httpMethod": "POST", "id": "spanner.projects.instances.move", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The instance to move. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:move", "request": {"$ref": "MoveInstanceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "patch": {"description": "Updates an instance, and begins allocating or releasing resources as requested. The returned long-running operation can be used to track the progress of updating the instance. If the named instance does not exist, returns `NOT_FOUND`. Immediately upon completion of this request: * For resource types for which a decrease in the instance's allocation has been requested, billing is based on the newly-requested level. Until completion of the returned operation: * Cancelling the operation sets its metadata's cancel_time, and begins restoring resources to their pre-request values. The operation is guaranteed to succeed at undoing all resource changes, after which point it terminates with a `CANCELLED` status. * All other attempts to modify the instance are rejected. * Reading the instance via the API continues to give the pre-request resource levels. Upon completion of the returned operation: * Billing begins for all successfully-allocated resources (some types may have lower than the requested levels). * All newly-reserved resources are available for serving the instance's tables. * The instance's new resource levels are readable via the API. The returned long-running operation will have a name of the format `/operations/` and can be used to track the instance modification. The metadata field type is UpdateInstanceMetadata. The response field type is Instance, if successful. Authorization requires `spanner.instances.update` permission on the resource name.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}", "httpMethod": "PATCH", "id": "spanner.projects.instances.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A unique identifier for the instance, which cannot be changed after the instance is created. Values are of the form `projects//instances/a-z*[a-z0-9]`. The final segment of the name must be between 2 and 64 characters in length.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "UpdateInstanceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "setIamPolicy": {"description": "Sets the access control policy on an instance resource. Replaces any existing policy. Authorization requires `spanner.instances.setIamPolicy` on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}:setIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being set. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for databases resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "testIamPermissions": {"description": "Returns permissions that the caller has on the specified instance resource. Attempting this RPC on a non-existent Cloud Spanner instance resource will result in a NOT_FOUND error if the user has `spanner.instances.list` permission on the containing Google Cloud Project. Otherwise returns an empty set of permissions.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}:testIamPermissions", "httpMethod": "POST", "id": "spanner.projects.instances.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which permissions are being tested. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}, "resources": {"backupOperations": {"methods": {"list": {"description": "Lists the backup long-running operations in the given instance. A backup operation has a name of the form `projects//instances//backups//operations/`. The long-running operation metadata field type `metadata.type_url` describes the type of the metadata. Operations returned include those that have completed/failed/canceled within the last 7 days, and pending operations. Operations returned are ordered by `operation.metadata.value.progress.start_time` in descending order starting from the most recently started operation.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backupOperations", "httpMethod": "GET", "id": "spanner.projects.instances.backupOperations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of returned backup operations. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string, a number, or a boolean. The comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`. Colon `:` is the contains operator. Filter rules are not case sensitive. The following fields in the operation are eligible for filtering: * `name` - The name of the long-running operation * `done` - False if the operation is in progress, else true. * `metadata.@type` - the type of metadata. For example, the type string for CreateBackupMetadata is `type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata`. * `metadata.` - any field in metadata.value. `metadata.@type` must be specified first if filtering on metadata fields. * `error` - Error associated with the long-running operation. * `response.@type` - the type of response. * `response.` - any field in response.value. You can combine multiple expressions by enclosing each expression in parentheses. By default, expressions are combined with AND logic, but you can specify AND, OR, and NOT logic explicitly. Here are a few examples: * `done:true` - The operation is complete. * `(metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata) AND` \\ `metadata.database:prod` - Returns operations where: * The operation's metadata type is CreateBackupMetadata. * The source database name of backup contains the string \"prod\". * `(metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata) AND` \\ `(metadata.name:howl) AND` \\ `(metadata.progress.start_time < \\\"2018-03-28T14:50:00Z\\\") AND` \\ `(error:*)` - Returns operations where: * The operation's metadata type is CreateBackupMetadata. * The backup name contains the string \"howl\". * The operation started before 2018-03-28T14:50:00Z. * The operation resulted in an error. * `(metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CopyBackupMetadata) AND` \\ `(metadata.source_backup:test) AND` \\ `(metadata.progress.start_time < \\\"2022-01-18T14:50:00Z\\\") AND` \\ `(error:*)` - Returns operations where: * The operation's metadata type is CopyBackupMetadata. * The source backup name contains the string \"test\". * The operation started before 2022-01-18T14:50:00Z. * The operation resulted in an error. * `((metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CreateBackupMetadata) AND` \\ `(metadata.database:test_db)) OR` \\ `((metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.CopyBackupMetadata) AND` \\ `(metadata.source_backup:test_bkp)) AND` \\ `(error:*)` - Returns operations where: * The operation's metadata matches either of criteria: * The operation's metadata type is CreateBackupMetadata AND the source database name of the backup contains the string \"test_db\" * The operation's metadata type is CopyBackupMetadata AND the source backup name contains the string \"test_bkp\" * The operation resulted in an error.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of operations to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListBackupOperationsResponse to the same `parent` and with the same `filter`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The instance of the backup operations. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backupOperations", "response": {"$ref": "ListBackupOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "backups": {"methods": {"copy": {"description": "Starts copying a Cloud Spanner Backup. The returned backup long-running operation will have a name of the format `projects//instances//backups//operations/` and can be used to track copying of the backup. The operation is associated with the destination backup. The metadata field type is CopyBackupMetadata. The response field type is Backup, if successful. Cancelling the returned operation will stop the copying and delete the destination backup. Concurrent CopyBackup requests can run on the same source backup.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups:copy", "httpMethod": "POST", "id": "spanner.projects.instances.backups.copy", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the destination instance that will contain the backup copy. Values are of the form: `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backups:copy", "request": {"$ref": "CopyBackupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "create": {"description": "Starts creating a new Cloud Spanner Backup. The returned backup long-running operation will have a name of the format `projects//instances//backups//operations/` and can be used to track creation of the backup. The metadata field type is CreateBackupMetadata. The response field type is Backup, if successful. Cancelling the returned operation will stop the creation and delete the backup. There can be only one pending backup creation per database. Backup creation of different databases can run concurrently.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups", "httpMethod": "POST", "id": "spanner.projects.instances.backups.create", "parameterOrder": ["parent"], "parameters": {"backupId": {"description": "Required. The id of the backup to be created. The `backup_id` appended to `parent` forms the full backup name of the form `projects//instances//backups/`.", "location": "query", "type": "string"}, "encryptionConfig.encryptionType": {"description": "Required. The encryption type of the backup.", "enum": ["ENCRYPTION_TYPE_UNSPECIFIED", "USE_DATABASE_ENCRYPTION", "GOOGLE_DEFAULT_ENCRYPTION", "CUSTOMER_MANAGED_ENCRYPTION"], "enumDescriptions": ["Unspecified. Do not use.", "Use the same encryption configuration as the database. This is the default option when encryption_config is empty. For example, if the database is using `Customer_Managed_Encryption`, the backup will be using the same Cloud KMS key as the database.", "Use Google default encryption.", "Use customer managed encryption. If specified, `kms_key_name` must contain a valid Cloud KMS key."], "location": "query", "type": "string"}, "encryptionConfig.kmsKeyName": {"description": "Optional. The Cloud KMS key that will be used to protect the backup. This field should be set only when encryption_type is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form `projects//locations//keyRings//cryptoKeys/`.", "location": "query", "type": "string"}, "encryptionConfig.kmsKeyNames": {"description": "Optional. Specifies the KMS configuration for the one or more keys used to protect the backup. Values are of the form `projects//locations//keyRings//cryptoKeys/`. The keys referenced by `kms_key_names` must fully cover all regions of the backup's instance configuration. Some examples: * For regional (single-region) instance configurations, specify a regional location KMS key. * For multi-region instance configurations of type `GOOGLE_MANAGED`, either specify a multi-region location KMS key or multiple regional location KMS keys that cover all regions in the instance configuration. * For an instance configuration of type `USER_MANAGED`, specify only regional location KMS keys to cover each region in the instance configuration. Multi-region location KMS keys aren't supported for `USER_MANAGED` type instance configurations.", "location": "query", "repeated": true, "type": "string"}, "parent": {"description": "Required. The name of the instance in which the backup will be created. This must be the same instance that contains the database the backup will be created from. The backup will be stored in the location(s) specified in the instance configuration of this instance. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backups", "request": {"$ref": "Backup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a pending or completed Backup.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.backups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the backup to delete. Values are of the form `projects//instances//backups/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets metadata on a pending or completed Backup.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}", "httpMethod": "GET", "id": "spanner.projects.instances.backups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the backup. Values are of the form `projects//instances//backups/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Backup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "getIamPolicy": {"description": "Gets the access control policy for a database or backup resource. Returns an empty policy if a database or backup exists but does not have a policy set. Authorization requires `spanner.databases.getIamPolicy` permission on resource. For backups, authorization requires `spanner.backups.getIamPolicy` permission on resource. For backup schedules, authorization requires `spanner.backupSchedules.getIamPolicy` permission on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}:getIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.backups.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being retrieved. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists completed and pending backups. Backups returned are ordered by `create_time` in descending order, starting from the most recent `create_time`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups", "httpMethod": "GET", "id": "spanner.projects.instances.backups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of returned backups. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string, a number, or a boolean. The comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`. Colon `:` is the contains operator. Filter rules are not case sensitive. The following fields in the Backup are eligible for filtering: * `name` * `database` * `state` * `create_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) * `expire_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) * `version_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) * `size_bytes` * `backup_schedules` You can combine multiple expressions by enclosing each expression in parentheses. By default, expressions are combined with AND logic, but you can specify AND, OR, and NOT logic explicitly. Here are a few examples: * `name:Howl` - The backup's name contains the string \"howl\". * `database:prod` - The database's name contains the string \"prod\". * `state:CREATING` - The backup is pending creation. * `state:READY` - The backup is fully created and ready for use. * `(name:howl) AND (create_time < \\\"2018-03-28T14:50:00Z\\\")` - The backup name contains the string \"howl\" and `create_time` of the backup is before 2018-03-28T14:50:00Z. * `expire_time < \\\"2018-03-28T14:50:00Z\\\"` - The backup `expire_time` is before 2018-03-28T14:50:00Z. * `size_bytes > 10000000000` - The backup's size is greater than 10GB * `backup_schedules:daily` - The backup is created from a schedule with \"daily\" in its name.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of backups to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListBackupsResponse to the same `parent` and with the same `filter`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The instance to list backups from. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backups", "response": {"$ref": "ListBackupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "patch": {"description": "Updates a pending or completed Backup.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}", "httpMethod": "PATCH", "id": "spanner.projects.instances.backups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only for the CreateBackup operation. Required for the UpdateBackup operation. A globally unique identifier for the backup which cannot be changed. Values are of the form `projects//instances//backups/a-z*[a-z0-9]` The final segment of the name must be between 2 and 60 characters in length. The backup is stored in the location(s) specified in the instance configuration of the instance containing the backup, identified by the prefix of the backup name of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. A mask specifying which fields (e.g. `expire_time`) in the Backup resource should be updated. This mask is relative to the Backup resource, not to the request message. The field mask must always be specified; this prevents any future fields from being erased accidentally by clients that do not know about them.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Backup"}, "response": {"$ref": "Backup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "setIamPolicy": {"description": "Sets the access control policy on a database or backup resource. Replaces any existing policy. Authorization requires `spanner.databases.setIamPolicy` permission on resource. For backups, authorization requires `spanner.backups.setIamPolicy` permission on resource. For backup schedules, authorization requires `spanner.backupSchedules.setIamPolicy` permission on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}:setIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.backups.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being set. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for databases resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "testIamPermissions": {"description": "Returns permissions that the caller has on the specified database or backup resource. Attempting this RPC on a non-existent Cloud Spanner database will result in a NOT_FOUND error if the user has `spanner.databases.list` permission on the containing Cloud Spanner instance. Otherwise returns an empty set of permissions. Calling this method on a backup that does not exist will result in a NOT_FOUND error if the user has `spanner.backups.list` permission on the containing instance. Calling this method on a backup schedule that does not exist will result in a NOT_FOUND error if the user has `spanner.backupSchedules.list` permission on the containing database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}:testIamPermissions", "httpMethod": "POST", "id": "spanner.projects.instances.backups.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which permissions are being tested. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}, "resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "spanner.projects.instances.backups.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.backups.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}/operations/{operationsId}", "httpMethod": "GET", "id": "spanner.projects.instances.backups.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/backups/{backupsId}/operations", "httpMethod": "GET", "id": "spanner.projects.instances.backups.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/backups/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}}}, "databaseOperations": {"methods": {"list": {"description": "Lists database longrunning-operations. A database operation has a name of the form `projects//instances//databases//operations/`. The long-running operation metadata field type `metadata.type_url` describes the type of the metadata. Operations returned include those that have completed/failed/canceled within the last 7 days, and pending operations.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databaseOperations", "httpMethod": "GET", "id": "spanner.projects.instances.databaseOperations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "An expression that filters the list of returned operations. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string, a number, or a boolean. The comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`. Colon `:` is the contains operator. Filter rules are not case sensitive. The following fields in the operation are eligible for filtering: * `name` - The name of the long-running operation * `done` - False if the operation is in progress, else true. * `metadata.@type` - the type of metadata. For example, the type string for RestoreDatabaseMetadata is `type.googleapis.com/google.spanner.admin.database.v1.RestoreDatabaseMetadata`. * `metadata.` - any field in metadata.value. `metadata.@type` must be specified first, if filtering on metadata fields. * `error` - Error associated with the long-running operation. * `response.@type` - the type of response. * `response.` - any field in response.value. You can combine multiple expressions by enclosing each expression in parentheses. By default, expressions are combined with AND logic. However, you can specify AND, OR, and NOT logic explicitly. Here are a few examples: * `done:true` - The operation is complete. * `(metadata.@type=type.googleapis.com/google.spanner.admin.database.v1.RestoreDatabaseMetadata) AND` \\ `(metadata.source_type:BACKUP) AND` \\ `(metadata.backup_info.backup:backup_howl) AND` \\ `(metadata.name:restored_howl) AND` \\ `(metadata.progress.start_time < \\\"2018-03-28T14:50:00Z\\\") AND` \\ `(error:*)` - Return operations where: * The operation's metadata type is RestoreDatabaseMetadata. * The database is restored from a backup. * The backup name contains \"backup_howl\". * The restored database's name contains \"restored_howl\". * The operation started before 2018-03-28T14:50:00Z. * The operation resulted in an error.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of operations to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListDatabaseOperationsResponse to the same `parent` and with the same `filter`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The instance of the database operations. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/databaseOperations", "response": {"$ref": "ListDatabaseOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "databases": {"methods": {"addSplitPoints": {"description": "Adds split points to specified tables and indexes of a database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}:addSplitPoints", "httpMethod": "POST", "id": "spanner.projects.instances.databases.addSplitPoints", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database on whose tables or indexes the split points are to be added. Values are of the form `projects//instances//databases/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+database}:addSplitPoints", "request": {"$ref": "AddSplitPointsRequest"}, "response": {"$ref": "AddSplitPointsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "changequorum": {"description": "`ChangeQuorum` is strictly restricted to databases that use dual-region instance configurations. Initiates a background operation to change the quorum of a database from dual-region mode to single-region mode or vice versa. The returned long-running operation has a name of the format `projects//instances//databases//operations/` and can be used to track execution of the `ChangeQuorum`. The metadata field type is ChangeQuorumMetadata. Authorization requires `spanner.databases.changequorum` permission on the resource database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}:changequorum", "httpMethod": "POST", "id": "spanner.projects.instances.databases.changequorum", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the database in which to apply `ChangeQuorum`. Values are of the form `projects//instances//databases/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:changequorum", "request": {"$ref": "ChangeQuorumRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "create": {"description": "Creates a new Spanner database and starts to prepare it for serving. The returned long-running operation will have a name of the format `/operations/` and can be used to track preparation of the database. The metadata field type is CreateDatabaseMetadata. The response field type is Database, if successful.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases", "httpMethod": "POST", "id": "spanner.projects.instances.databases.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the instance that will serve the new database. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/databases", "request": {"$ref": "CreateDatabaseRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "dropDatabase": {"description": "Drops (aka deletes) a Cloud Spanner database. Completed backups for the database will be retained according to their `expire_time`. Note: Cloud Spanner might continue to accept requests for a few seconds after the database has been deleted.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.databases.dropDatabase", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database to be dropped.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+database}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets the state of a Cloud Spanner database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}", "httpMethod": "GET", "id": "spanner.projects.instances.databases.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the requested database. Values are of the form `projects//instances//databases/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Database"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "getDdl": {"description": "Returns the schema of a Cloud Spanner database as a list of formatted DDL statements. This method does not show pending schema updates, those may be queried using the Operations API.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/ddl", "httpMethod": "GET", "id": "spanner.projects.instances.databases.getDdl", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database whose schema we wish to get. Values are of the form `projects//instances//databases/`", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+database}/ddl", "response": {"$ref": "GetDatabaseDdlResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "getIamPolicy": {"description": "Gets the access control policy for a database or backup resource. Returns an empty policy if a database or backup exists but does not have a policy set. Authorization requires `spanner.databases.getIamPolicy` permission on resource. For backups, authorization requires `spanner.backups.getIamPolicy` permission on resource. For backup schedules, authorization requires `spanner.backupSchedules.getIamPolicy` permission on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}:getIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.databases.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being retrieved. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "getScans": {"description": "Request a specific scan with Database-specific data for Cloud Key Visualizer.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/scans", "httpMethod": "GET", "id": "spanner.projects.instances.databases.getScans", "parameterOrder": ["name"], "parameters": {"endTime": {"description": "The upper bound for the time range to retrieve Scan data for.", "format": "google-datetime", "location": "query", "type": "string"}, "name": {"description": "Required. The unique name of the scan containing the requested information, specific to the Database service implementing this interface.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}, "startTime": {"description": "These fields restrict the Database-specific information returned in the `Scan.data` field. If a `View` is provided that does not include the `Scan.data` field, these are ignored. This range of time must be entirely contained within the defined time range of the targeted scan. The lower bound for the time range to retrieve Scan data for.", "format": "google-datetime", "location": "query", "type": "string"}, "view": {"description": "Specifies which parts of the Scan should be returned in the response. Note, if left unspecified, the FULL view is assumed.", "enum": ["VIEW_UNSPECIFIED", "SUMMARY", "FULL"], "enumDescriptions": ["Not specified, equivalent to SUMMARY.", "Server responses only include `name`, `details`, `start_time` and `end_time`. The default value. Note, the ListScans method may only use this view type, others view types are not supported.", "Full representation of the scan is returned in the server response, including `data`."], "location": "query", "type": "string"}}, "path": "v1/{+name}/scans", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "list": {"description": "Lists Cloud Spanner databases.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases", "httpMethod": "GET", "id": "spanner.projects.instances.databases.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Number of databases to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListDatabasesResponse.", "location": "query", "type": "string"}, "parent": {"description": "Required. The instance whose databases should be listed. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/databases", "response": {"$ref": "ListDatabasesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "patch": {"description": "Updates a Cloud Spanner database. The returned long-running operation can be used to track the progress of updating the database. If the named database does not exist, returns `NOT_FOUND`. While the operation is pending: * The database's reconciling field is set to true. * Cancelling the operation is best-effort. If the cancellation succeeds, the operation metadata's cancel_time is set, the updates are reverted, and the operation terminates with a `CANCELLED` status. * New UpdateDatabase requests will return a `FAILED_PRECONDITION` error until the pending operation is done (returns successfully or with error). * Reading the database via the API continues to give the pre-request values. Upon completion of the returned operation: * The new values are in effect and readable via the API. * The database's reconciling field becomes false. The returned long-running operation will have a name of the format `projects//instances//databases//operations/` and can be used to track the database modification. The metadata field type is UpdateDatabaseMetadata. The response field type is Database, if successful.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}", "httpMethod": "PATCH", "id": "spanner.projects.instances.databases.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the database. Values are of the form `projects//instances//databases/`, where `` is as specified in the `CREATE DATABASE` statement. This name can be passed to other API methods to identify the database.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to update. Currently, only `enable_drop_protection` field can be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Database"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "restore": {"description": "Create a new database by restoring from a completed backup. The new database must be in the same project and in an instance with the same instance configuration as the instance containing the backup. The returned database long-running operation has a name of the format `projects//instances//databases//operations/`, and can be used to track the progress of the operation, and to cancel it. The metadata field type is RestoreDatabaseMetadata. The response type is Database, if successful. Cancelling the returned operation will stop the restore and delete the database. There can be only one database being restored into an instance at a time. Once the restore operation completes, a new restore operation can be initiated, without waiting for the optimize operation associated with the first restore to complete.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases:restore", "httpMethod": "POST", "id": "spanner.projects.instances.databases.restore", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the instance in which to create the restored database. This instance must be in the same project and have the same instance configuration as the instance containing the source backup. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/databases:restore", "request": {"$ref": "RestoreDatabaseRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "setIamPolicy": {"description": "Sets the access control policy on a database or backup resource. Replaces any existing policy. Authorization requires `spanner.databases.setIamPolicy` permission on resource. For backups, authorization requires `spanner.backups.setIamPolicy` permission on resource. For backup schedules, authorization requires `spanner.backupSchedules.setIamPolicy` permission on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}:setIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.databases.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being set. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for databases resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "testIamPermissions": {"description": "Returns permissions that the caller has on the specified database or backup resource. Attempting this RPC on a non-existent Cloud Spanner database will result in a NOT_FOUND error if the user has `spanner.databases.list` permission on the containing Cloud Spanner instance. Otherwise returns an empty set of permissions. Calling this method on a backup that does not exist will result in a NOT_FOUND error if the user has `spanner.backups.list` permission on the containing instance. Calling this method on a backup schedule that does not exist will result in a NOT_FOUND error if the user has `spanner.backupSchedules.list` permission on the containing database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}:testIamPermissions", "httpMethod": "POST", "id": "spanner.projects.instances.databases.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which permissions are being tested. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "updateDdl": {"description": "Updates the schema of a Cloud Spanner database by creating/altering/dropping tables, columns, indexes, etc. The returned long-running operation will have a name of the format `/operations/` and can be used to track execution of the schema change(s). The metadata field type is UpdateDatabaseDdlMetadata. The operation has no response.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/ddl", "httpMethod": "PATCH", "id": "spanner.projects.instances.databases.updateDdl", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database to update.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+database}/ddl", "request": {"$ref": "UpdateDatabaseDdlRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}, "resources": {"backupSchedules": {"methods": {"create": {"description": "Creates a new backup schedule.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules", "httpMethod": "POST", "id": "spanner.projects.instances.databases.backupSchedules.create", "parameterOrder": ["parent"], "parameters": {"backupScheduleId": {"description": "Required. The Id to use for the backup schedule. The `backup_schedule_id` appended to `parent` forms the full backup schedule name of the form `projects//instances//databases//backupSchedules/`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the database that this backup schedule applies to.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backupSchedules", "request": {"$ref": "BackupSchedule"}, "response": {"$ref": "BackupSchedule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a backup schedule.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.databases.backupSchedules.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the schedule to delete. Values are of the form `projects//instances//databases//backupSchedules/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/backupSchedules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets backup schedule for the input schedule name.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}", "httpMethod": "GET", "id": "spanner.projects.instances.databases.backupSchedules.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the schedule to retrieve. Values are of the form `projects//instances//databases//backupSchedules/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/backupSchedules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BackupSchedule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "getIamPolicy": {"description": "Gets the access control policy for a database or backup resource. Returns an empty policy if a database or backup exists but does not have a policy set. Authorization requires `spanner.databases.getIamPolicy` permission on resource. For backups, authorization requires `spanner.backups.getIamPolicy` permission on resource. For backup schedules, authorization requires `spanner.backupSchedules.getIamPolicy` permission on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}:getIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.databases.backupSchedules.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being retrieved. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/backupSchedules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists all the backup schedules for the database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules", "httpMethod": "GET", "id": "spanner.projects.instances.databases.backupSchedules.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. Number of backup schedules to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. If non-empty, `page_token` should contain a next_page_token from a previous ListBackupSchedulesResponse to the same `parent`.", "location": "query", "type": "string"}, "parent": {"description": "Required. Database is the parent resource whose backup schedules should be listed. Values are of the form projects//instances//databases/", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backupSchedules", "response": {"$ref": "ListBackupSchedulesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "patch": {"description": "Updates a backup schedule.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}", "httpMethod": "PATCH", "id": "spanner.projects.instances.databases.backupSchedules.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Output only for the CreateBackupSchedule operation. Required for the UpdateBackupSchedule operation. A globally unique identifier for the backup schedule which cannot be changed. Values are of the form `projects//instances//databases//backupSchedules/a-z*[a-z0-9]` The final segment of the name must be between 2 and 60 characters in length.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/backupSchedules/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. A mask specifying which fields in the BackupSchedule resource should be updated. This mask is relative to the BackupSchedule resource, not to the request message. The field mask must always be specified; this prevents any future fields from being erased accidentally.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "BackupSchedule"}, "response": {"$ref": "BackupSchedule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "setIamPolicy": {"description": "Sets the access control policy on a database or backup resource. Replaces any existing policy. Authorization requires `spanner.databases.setIamPolicy` permission on resource. For backups, authorization requires `spanner.backups.setIamPolicy` permission on resource. For backup schedules, authorization requires `spanner.backupSchedules.setIamPolicy` permission on resource.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}:setIamPolicy", "httpMethod": "POST", "id": "spanner.projects.instances.databases.backupSchedules.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which the policy is being set. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for databases resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/backupSchedules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "testIamPermissions": {"description": "Returns permissions that the caller has on the specified database or backup resource. Attempting this RPC on a non-existent Cloud Spanner database will result in a NOT_FOUND error if the user has `spanner.databases.list` permission on the containing Cloud Spanner instance. Otherwise returns an empty set of permissions. Calling this method on a backup that does not exist will result in a NOT_FOUND error if the user has `spanner.backups.list` permission on the containing instance. Calling this method on a backup schedule that does not exist will result in a NOT_FOUND error if the user has `spanner.backupSchedules.list` permission on the containing database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}:testIamPermissions", "httpMethod": "POST", "id": "spanner.projects.instances.databases.backupSchedules.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which permissions are being tested. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/backupSchedules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "databaseRoles": {"methods": {"list": {"description": "Lists Cloud Spanner database roles.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/databaseRoles", "httpMethod": "GET", "id": "spanner.projects.instances.databases.databaseRoles.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Number of database roles to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListDatabaseRolesResponse.", "location": "query", "type": "string"}, "parent": {"description": "Required. The database whose roles should be listed. Values are of the form `projects//instances//databases/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/databaseRoles", "response": {"$ref": "ListDatabaseRolesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "testIamPermissions": {"description": "Returns permissions that the caller has on the specified database or backup resource. Attempting this RPC on a non-existent Cloud Spanner database will result in a NOT_FOUND error if the user has `spanner.databases.list` permission on the containing Cloud Spanner instance. Otherwise returns an empty set of permissions. Calling this method on a backup that does not exist will result in a NOT_FOUND error if the user has `spanner.backups.list` permission on the containing instance. Calling this method on a backup schedule that does not exist will result in a NOT_FOUND error if the user has `spanner.backupSchedules.list` permission on the containing database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/databaseRoles/{databaseRolesId}:testIamPermissions", "httpMethod": "POST", "id": "spanner.projects.instances.databases.databaseRoles.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The Cloud Spanner resource for which permissions are being tested. The format is `projects//instances/` for instance resources and `projects//instances//databases/` for database resources.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/databaseRoles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "spanner.projects.instances.databases.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.databases.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/operations/{operationsId}", "httpMethod": "GET", "id": "spanner.projects.instances.databases.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/operations", "httpMethod": "GET", "id": "spanner.projects.instances.databases.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "sessions": {"methods": {"adaptMessage": {"description": "Handles a single message from the client and returns the result as a stream. The server will interpret the message frame and respond with message frames to the client.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:adaptMessage", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.adaptMessage", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The database session in which the adapter request is processed.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:adaptMessage", "request": {"$ref": "AdaptMessageRequest"}, "response": {"$ref": "AdaptMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "adapter": {"description": "Creates a new session to be used for requests made by the adapter. A session identifies a specific incarnation of a database resource and is meant to be reused across many `AdaptMessage` calls.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions:adapter", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.adapter", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The database in which the new session is created.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/sessions:adapter", "request": {"$ref": "AdapterSession"}, "response": {"$ref": "AdapterSession"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "batchCreate": {"description": "Creates multiple new sessions. This API can be used to initialize a session cache on the clients. See https://goo.gl/TgSFN2 for best practices on session cache management.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions:batchCreate", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.batchCreate", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database in which the new sessions are created.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+database}/sessions:batchCreate", "request": {"$ref": "BatchCreateSessionsRequest"}, "response": {"$ref": "BatchCreateSessionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "batchWrite": {"description": "Batches the supplied mutation groups in a collection of efficient transactions. All mutations in a group are committed atomically. However, mutations across groups can be committed non-atomically in an unspecified order and thus, they must be independent of each other. Partial failure is possible, that is, some groups might have been committed successfully, while some might have failed. The results of individual batches are streamed into the response as the batches are applied. `BatchWrite` requests are not replay protected, meaning that each mutation group can be applied more than once. Replays of non-idempotent mutations can have undesirable effects. For example, replays of an insert mutation can produce an already exists error or if you use generated or commit timestamp-based keys, it can result in additional rows being added to the mutation's table. We recommend structuring your mutation groups to be idempotent to avoid this issue.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:batchWrite", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.batchWrite", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the batch request is to be run.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:batchWrite", "request": {"$ref": "BatchWriteRequest"}, "response": {"$ref": "BatchWriteResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "beginTransaction": {"description": "Begins a new transaction. This step can often be skipped: Read, ExecuteSql and Commit can begin a new transaction as a side-effect.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:beginTransaction", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.beginTransaction", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the transaction runs.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:beginTransaction", "request": {"$ref": "BeginTransactionRequest"}, "response": {"$ref": "Transaction"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "commit": {"description": "Commits a transaction. The request includes the mutations to be applied to rows in the database. `<PERSON>mmit` might return an `ABORTED` error. This can occur at any time; commonly, the cause is conflicts with concurrent transactions. However, it can also happen for a variety of other reasons. If `Commit` returns `ABORTED`, the caller should retry the transaction from the beginning, reusing the same session. On very rare occasions, `<PERSON>mmit` might return `UNKNOWN`. This can happen, for example, if the client job experiences a 1+ hour networking failure. At that point, Cloud Spanner has lost track of the transaction outcome and we recommend that you perform another read from the database to see the state of things as they are now.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:commit", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.commit", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the transaction to be committed is running.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:commit", "request": {"$ref": "CommitRequest"}, "response": {"$ref": "CommitResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "create": {"description": "Creates a new session. A session can be used to perform transactions that read and/or modify data in a Cloud Spanner database. Sessions are meant to be reused for many consecutive transactions. Sessions can only execute one transaction at a time. To execute multiple concurrent read-write/write-only transactions, create multiple sessions. Note that standalone reads and queries use a transaction internally, and count toward the one transaction limit. Active sessions use additional server resources, so it's a good idea to delete idle and unneeded sessions. Aside from explicit deletes, Cloud Spanner can delete sessions when no operations are sent for more than an hour. If a session is deleted, requests to it return `NOT_FOUND`. Idle sessions can be kept alive by sending a trivial SQL query periodically, for example, `\"SELECT 1\"`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.create", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database in which the new session is created.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+database}/sessions", "request": {"$ref": "CreateSessionRequest"}, "response": {"$ref": "Session"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "delete": {"description": "Ends a session, releasing server resources associated with it. This asynchronously triggers the cancellation of any operations that are running with this session.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.databases.sessions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the session to delete.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "executeBatchDml": {"description": "Executes a batch of SQL DML statements. This method allows many statements to be run with lower latency than submitting them sequentially with ExecuteSql. Statements are executed in sequential order. A request can succeed even if a statement fails. The ExecuteBatchDmlResponse.status field in the response provides information about the statement that failed. Clients must inspect this field to determine whether an error occurred. Execution stops after the first failed statement; the remaining statements are not executed.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:executeBatchDml", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.executeBatchDml", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the DML statements should be performed.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:executeBatchDml", "request": {"$ref": "ExecuteBatchDmlRequest"}, "response": {"$ref": "ExecuteBatchDmlResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "executeSql": {"description": "Executes an SQL statement, returning all results in a single reply. This method can't be used to return a result set larger than 10 MiB; if the query yields more data than that, the query fails with a `FAILED_PRECONDITION` error. Operations inside read-write transactions might return `ABORTED`. If this occurs, the application should restart the transaction from the beginning. See Transaction for more details. Larger result sets can be fetched in streaming fashion by calling ExecuteStreamingSql instead. The query string can be SQL or [Graph Query Language (GQL)](https://cloud.google.com/spanner/docs/reference/standard-sql/graph-intro).", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:executeSql", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.executeSql", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the SQL query should be performed.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:executeSql", "request": {"$ref": "ExecuteSqlRequest"}, "response": {"$ref": "ResultSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "executeStreamingSql": {"description": "Like ExecuteSql, except returns the result set as a stream. Unlike ExecuteSql, there is no limit on the size of the returned result set. However, no individual row in the result set can exceed 100 MiB, and no column value can exceed 10 MiB. The query string can be SQL or [Graph Query Language (GQL)](https://cloud.google.com/spanner/docs/reference/standard-sql/graph-intro).", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:executeStreamingSql", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.executeStreamingSql", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the SQL query should be performed.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:executeStreamingSql", "request": {"$ref": "ExecuteSqlRequest"}, "response": {"$ref": "PartialResultSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "get": {"description": "Gets a session. Returns `NOT_FOUND` if the session doesn't exist. This is mainly useful for determining whether a session is still alive.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}", "httpMethod": "GET", "id": "spanner.projects.instances.databases.sessions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the session to retrieve.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Session"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "list": {"description": "Lists all sessions in a given database.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions", "httpMethod": "GET", "id": "spanner.projects.instances.databases.sessions.list", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database in which to list sessions.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "An expression for filtering the results of the request. Filter rules are case insensitive. The fields eligible for filtering are: * `labels.key` where key is the name of a label Some examples of using filters are: * `labels.env:*` --> The session has the label \"env\". * `labels.env:dev` --> The session has the label \"env\" and the value of the label contains the string \"dev\".", "location": "query", "type": "string"}, "pageSize": {"description": "Number of sessions to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListSessionsResponse.", "location": "query", "type": "string"}}, "path": "v1/{+database}/sessions", "response": {"$ref": "ListSessionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "partitionQuery": {"description": "Creates a set of partition tokens that can be used to execute a query operation in parallel. Each of the returned partition tokens can be used by ExecuteStreamingSql to specify a subset of the query result to read. The same session and read-only transaction must be used by the `PartitionQueryRequest` used to create the partition tokens and the `ExecuteSqlRequests` that use the partition tokens. Partition tokens become invalid when the session used to create them is deleted, is idle for too long, begins a new transaction, or becomes too old. When any of these happen, it isn't possible to resume the query, and the whole operation must be restarted from the beginning.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:partitionQuery", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.partitionQuery", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session used to create the partitions.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:partitionQuery", "request": {"$ref": "PartitionQueryRequest"}, "response": {"$ref": "PartitionResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "partitionRead": {"description": "Creates a set of partition tokens that can be used to execute a read operation in parallel. Each of the returned partition tokens can be used by StreamingRead to specify a subset of the read result to read. The same session and read-only transaction must be used by the `PartitionReadRequest` used to create the partition tokens and the `ReadRequests` that use the partition tokens. There are no ordering guarantees on rows returned among the returned partition tokens, or even within each individual `StreamingRead` call issued with a `partition_token`. Partition tokens become invalid when the session used to create them is deleted, is idle for too long, begins a new transaction, or becomes too old. When any of these happen, it isn't possible to resume the read, and the whole operation must be restarted from the beginning.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:partitionRead", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.partitionRead", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session used to create the partitions.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:partitionRead", "request": {"$ref": "PartitionReadRequest"}, "response": {"$ref": "PartitionResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "read": {"description": "Reads rows from the database using key lookups and scans, as a simple key/value style alternative to ExecuteSql. This method can't be used to return a result set larger than 10 MiB; if the read matches more data than that, the read fails with a `FAILED_PRECONDITION` error. Reads inside read-write transactions might return `ABORTED`. If this occurs, the application should restart the transaction from the beginning. See Transaction for more details. Larger result sets can be yielded in streaming fashion by calling StreamingRead instead.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:read", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.read", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the read should be performed.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:read", "request": {"$ref": "ReadRequest"}, "response": {"$ref": "ResultSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "rollback": {"description": "Rolls back a transaction, releasing any locks it holds. It's a good idea to call this for any transaction that includes one or more Read or ExecuteSql requests and ultimately decides not to commit. `Rollback` returns `OK` if it successfully aborts the transaction, the transaction was already aborted, or the transaction isn't found. `Rollback` never returns `ABORTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:rollback", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.rollback", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the transaction to roll back is running.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:rollback", "request": {"$ref": "RollbackRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}, "streamingRead": {"description": "Like Read, except returns the result set as a stream. Unlike Read, there is no limit on the size of the returned result set. However, no individual row in the result set can exceed 100 MiB, and no column value can exceed 10 MiB.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/databases/{databasesId}/sessions/{sessionsId}:streamingRead", "httpMethod": "POST", "id": "spanner.projects.instances.databases.sessions.streamingRead", "parameterOrder": ["session"], "parameters": {"session": {"description": "Required. The session in which the read should be performed.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/databases/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+session}:streamingRead", "request": {"$ref": "ReadRequest"}, "response": {"$ref": "PartialResultSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}}}}}, "instancePartitionOperations": {"methods": {"list": {"description": "Lists instance partition long-running operations in the given instance. An instance partition operation has a name of the form `projects//instances//instancePartitions//operations/`. The long-running operation metadata field type `metadata.type_url` describes the type of the metadata. Operations returned include those that have completed/failed/canceled within the last 7 days, and pending operations. Operations returned are ordered by `operation.metadata.value.start_time` in descending order starting from the most recently started operation. Authorization requires `spanner.instancePartitionOperations.list` permission on the resource parent.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitionOperations", "httpMethod": "GET", "id": "spanner.projects.instances.instancePartitionOperations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. An expression that filters the list of returned operations. A filter expression consists of a field name, a comparison operator, and a value for filtering. The value must be a string, a number, or a boolean. The comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or `:`. Colon `:` is the contains operator. Filter rules are not case sensitive. The following fields in the Operation are eligible for filtering: * `name` - The name of the long-running operation * `done` - False if the operation is in progress, else true. * `metadata.@type` - the type of metadata. For example, the type string for CreateInstancePartitionMetadata is `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstancePartitionMetadata`. * `metadata.` - any field in metadata.value. `metadata.@type` must be specified first, if filtering on metadata fields. * `error` - Error associated with the long-running operation. * `response.@type` - the type of response. * `response.` - any field in response.value. You can combine multiple expressions by enclosing each expression in parentheses. By default, expressions are combined with AND logic. However, you can specify AND, OR, and NOT logic explicitly. Here are a few examples: * `done:true` - The operation is complete. * `(metadata.@type=` \\ `type.googleapis.com/google.spanner.admin.instance.v1.CreateInstancePartitionMetadata) AND` \\ `(metadata.instance_partition.name:custom-instance-partition) AND` \\ `(metadata.start_time < \\\"2021-03-28T14:50:00Z\\\") AND` \\ `(error:*)` - Return operations where: * The operation's metadata type is CreateInstancePartitionMetadata. * The instance partition name contains \"custom-instance-partition\". * The operation started before 2021-03-28T14:50:00Z. * The operation resulted in an error.", "location": "query", "type": "string"}, "instancePartitionDeadline": {"description": "Optional. Deadline used while retrieving metadata for instance partition operations. Instance partitions whose operation metadata cannot be retrieved within this deadline will be added to unreachable_instance_partitions in ListInstancePartitionOperationsResponse.", "format": "google-datetime", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Number of operations to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. If non-empty, `page_token` should contain a next_page_token from a previous ListInstancePartitionOperationsResponse to the same `parent` and with the same `filter`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent instance of the instance partition operations. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instancePartitionOperations", "response": {"$ref": "ListInstancePartitionOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}, "instancePartitions": {"methods": {"create": {"description": "Creates an instance partition and begins preparing it to be used. The returned long-running operation can be used to track the progress of preparing the new instance partition. The instance partition name is assigned by the caller. If the named instance partition already exists, `CreateInstancePartition` returns `ALREADY_EXISTS`. Immediately upon completion of this request: * The instance partition is readable via the API, with all requested attributes but no allocated resources. Its state is `CREATING`. Until completion of the returned operation: * Cancelling the operation renders the instance partition immediately unreadable via the API. * The instance partition can be deleted. * All other attempts to modify the instance partition are rejected. Upon completion of the returned operation: * Billing for all successfully-allocated resources begins (some types may have lower than the requested levels). * Databases can start using this instance partition. * The instance partition's allocated resource levels are readable via the API. * The instance partition's state becomes `READY`. The returned long-running operation will have a name of the format `/operations/` and can be used to track creation of the instance partition. The metadata field type is CreateInstancePartitionMetadata. The response field type is InstancePartition, if successful.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions", "httpMethod": "POST", "id": "spanner.projects.instances.instancePartitions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the instance in which to create the instance partition. Values are of the form `projects//instances/`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instancePartitions", "request": {"$ref": "CreateInstancePartitionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes an existing instance partition. Requires that the instance partition is not used by any database or backup and is not the default instance partition of an instance. Authorization requires `spanner.instancePartitions.delete` permission on the resource name.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions/{instancePartitionsId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.instancePartitions.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If not empty, the API only deletes the instance partition when the etag provided matches the current status of the requested instance partition. Otherwise, deletes the instance partition without checking the current status of the requested instance partition.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the instance partition to be deleted. Values are of the form `projects/{project}/instances/{instance}/instancePartitions/{instance_partition}`", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/instancePartitions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets information about a particular instance partition.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions/{instancePartitionsId}", "httpMethod": "GET", "id": "spanner.projects.instances.instancePartitions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the requested instance partition. Values are of the form `projects/{project}/instances/{instance}/instancePartitions/{instance_partition}`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/instancePartitions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "InstancePartition"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists all instance partitions for the given instance.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions", "httpMethod": "GET", "id": "spanner.projects.instances.instancePartitions.list", "parameterOrder": ["parent"], "parameters": {"instancePartitionDeadline": {"description": "Optional. Deadline used while retrieving metadata for instance partitions. Instance partitions whose metadata cannot be retrieved within this deadline will be added to unreachable in ListInstancePartitionsResponse.", "format": "google-datetime", "location": "query", "type": "string"}, "pageSize": {"description": "Number of instance partitions to be returned in the response. If 0 or less, defaults to the server's maximum allowed page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "If non-empty, `page_token` should contain a next_page_token from a previous ListInstancePartitionsResponse.", "location": "query", "type": "string"}, "parent": {"description": "Required. The instance whose instance partitions should be listed. Values are of the form `projects//instances/`. Use `{instance} = '-'` to list instance partitions for all Instances in a project, e.g., `projects/myproject/instances/-`.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/instancePartitions", "response": {"$ref": "ListInstancePartitionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "patch": {"description": "Updates an instance partition, and begins allocating or releasing resources as requested. The returned long-running operation can be used to track the progress of updating the instance partition. If the named instance partition does not exist, returns `NOT_FOUND`. Immediately upon completion of this request: * For resource types for which a decrease in the instance partition's allocation has been requested, billing is based on the newly-requested level. Until completion of the returned operation: * Cancelling the operation sets its metadata's cancel_time, and begins restoring resources to their pre-request values. The operation is guaranteed to succeed at undoing all resource changes, after which point it terminates with a `CANCELLED` status. * All other attempts to modify the instance partition are rejected. * Reading the instance partition via the API continues to give the pre-request resource levels. Upon completion of the returned operation: * Billing begins for all successfully-allocated resources (some types may have lower than the requested levels). * All newly-reserved resources are available for serving the instance partition's tables. * The instance partition's new resource levels are readable via the API. The returned long-running operation will have a name of the format `/operations/` and can be used to track the instance partition modification. The metadata field type is UpdateInstancePartitionMetadata. The response field type is InstancePartition, if successful. Authorization requires `spanner.instancePartitions.update` permission on the resource name.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions/{instancePartitionsId}", "httpMethod": "PATCH", "id": "spanner.projects.instances.instancePartitions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A unique identifier for the instance partition. Values are of the form `projects//instances//instancePartitions/a-z*[a-z0-9]`. The final segment of the name must be between 2 and 64 characters in length. An instance partition's name cannot be changed after the instance partition is created.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/instancePartitions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "UpdateInstancePartitionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}, "resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions/{instancePartitionsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "spanner.projects.instances.instancePartitions.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/instancePartitions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions/{instancePartitionsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.instancePartitions.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/instancePartitions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions/{instancePartitionsId}/operations/{operationsId}", "httpMethod": "GET", "id": "spanner.projects.instances.instancePartitions.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/instancePartitions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/instancePartitions/{instancePartitionsId}/operations", "httpMethod": "GET", "id": "spanner.projects.instances.instancePartitions.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/instancePartitions/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "spanner.projects.instances.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "spanner.projects.instances.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/operations/{operationsId}", "httpMethod": "GET", "id": "spanner.projects.instances.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/instances/{instancesId}/operations", "httpMethod": "GET", "id": "spanner.projects.instances.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/instances/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.admin"]}}}}}}}, "scans": {"methods": {"list": {"description": "Return available scans given a Database-specific resource name.", "flatPath": "v1/scans", "httpMethod": "GET", "id": "spanner.scans.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression to restrict the results based on information present in the available Scan collection. The filter applies to all fields within the Scan message except for `data`.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of items to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The unique name of the parent resource, specific to the Database service implementing this interface.", "location": "path", "pattern": "^scans$", "required": true, "type": "string"}, "view": {"description": "Specifies which parts of the Scan should be returned in the response. Note, only the SUMMARY view (the default) is currently supported for ListScans.", "enum": ["VIEW_UNSPECIFIED", "SUMMARY", "FULL"], "enumDescriptions": ["Not specified, equivalent to SUMMARY.", "Server responses only include `name`, `details`, `start_time` and `end_time`. The default value. Note, the ListScans method may only use this view type, others view types are not supported.", "Full representation of the scan is returned in the server response, including `data`."], "location": "query", "type": "string"}}, "path": "v1/{+parent}", "response": {"$ref": "ListScansResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/spanner.data"]}}}}, "revision": "20250523", "rootUrl": "https://spanner.googleapis.com/", "schemas": {"AdaptMessageRequest": {"description": "Message sent by the client to the adapter.", "id": "AdaptMessageRequest", "properties": {"attachments": {"additionalProperties": {"type": "string"}, "description": "Optional. Opaque request state passed by the client to the server.", "type": "object"}, "payload": {"description": "Optional. Uninterpreted bytes from the underlying wire protocol.", "format": "byte", "type": "string"}, "protocol": {"description": "Required. Identifier for the underlying wire protocol.", "type": "string"}}, "type": "object"}, "AdaptMessageResponse": {"description": "Message sent by the adapter to the client.", "id": "AdaptMessageResponse", "properties": {"payload": {"description": "Optional. Uninterpreted bytes from the underlying wire protocol.", "format": "byte", "type": "string"}, "stateUpdates": {"additionalProperties": {"type": "string"}, "description": "Optional. Opaque state updates to be applied by the client.", "type": "object"}}, "type": "object"}, "AdapterSession": {"description": "A session in the Cloud Spanner Adapter API.", "id": "AdapterSession", "properties": {"name": {"description": "Identifier. The name of the session. This is always system-assigned.", "type": "string"}}, "type": "object"}, "AddSplitPointsRequest": {"description": "The request for AddSplitPoints.", "id": "AddSplitPointsRequest", "properties": {"initiator": {"description": "Optional. A user-supplied tag associated with the split points. For example, \"initial_data_load\", \"special_event_1\". Defaults to \"CloudAddSplitPointsAPI\" if not specified. The length of the tag must not exceed 50 characters, or else it is trimmed. Only valid UTF8 characters are allowed.", "type": "string"}, "splitPoints": {"description": "Required. The split points to add.", "items": {"$ref": "SplitPoints"}, "type": "array"}}, "type": "object"}, "AddSplitPointsResponse": {"description": "The response for AddSplitPoints.", "id": "AddSplitPointsResponse", "properties": {}, "type": "object"}, "AsymmetricAutoscalingOption": {"description": "AsymmetricAutoscalingOption specifies the scaling of replicas identified by the given selection.", "id": "AsymmetricAutoscalingOption", "properties": {"overrides": {"$ref": "AutoscalingConfigOverrides", "description": "Optional. Overrides applied to the top-level autoscaling configuration for the selected replicas."}, "replicaSelection": {"$ref": "InstanceReplicaSelection", "description": "Required. Selects the replicas to which this AsymmetricAutoscalingOption applies. Only read-only replicas are supported."}}, "type": "object"}, "AutoscalingConfig": {"description": "Autoscaling configuration for an instance.", "id": "AutoscalingConfig", "properties": {"asymmetricAutoscalingOptions": {"description": "Optional. Optional asymmetric autoscaling options. Replicas matching the replica selection criteria will be autoscaled independently from other replicas. The autoscaler will scale the replicas based on the utilization of replicas identified by the replica selection. Replica selections should not overlap with each other. Other replicas (those do not match any replica selection) will be autoscaled together and will have the same compute capacity allocated to them.", "items": {"$ref": "AsymmetricAutoscalingOption"}, "type": "array"}, "autoscalingLimits": {"$ref": "AutoscalingLimits", "description": "Required. Autoscaling limits for an instance."}, "autoscalingTargets": {"$ref": "AutoscalingTargets", "description": "Required. The autoscaling targets for an instance."}}, "type": "object"}, "AutoscalingConfigOverrides": {"description": "Overrides the top-level autoscaling configuration for the replicas identified by `replica_selection`. All fields in this message are optional. Any unspecified fields will use the corresponding values from the top-level autoscaling configuration.", "id": "AutoscalingConfigOverrides", "properties": {"autoscalingLimits": {"$ref": "AutoscalingLimits", "description": "Optional. If specified, overrides the min/max limit in the top-level autoscaling configuration for the selected replicas."}, "autoscalingTargetHighPriorityCpuUtilizationPercent": {"description": "Optional. If specified, overrides the autoscaling target high_priority_cpu_utilization_percent in the top-level autoscaling configuration for the selected replicas.", "format": "int32", "type": "integer"}}, "type": "object"}, "AutoscalingLimits": {"description": "The autoscaling limits for the instance. Users can define the minimum and maximum compute capacity allocated to the instance, and the autoscaler will only scale within that range. Users can either use nodes or processing units to specify the limits, but should use the same unit to set both the min_limit and max_limit.", "id": "AutoscalingLimits", "properties": {"maxNodes": {"description": "Maximum number of nodes allocated to the instance. If set, this number should be greater than or equal to min_nodes.", "format": "int32", "type": "integer"}, "maxProcessingUnits": {"description": "Maximum number of processing units allocated to the instance. If set, this number should be multiples of 1000 and be greater than or equal to min_processing_units.", "format": "int32", "type": "integer"}, "minNodes": {"description": "Minimum number of nodes allocated to the instance. If set, this number should be greater than or equal to 1.", "format": "int32", "type": "integer"}, "minProcessingUnits": {"description": "Minimum number of processing units allocated to the instance. If set, this number should be multiples of 1000.", "format": "int32", "type": "integer"}}, "type": "object"}, "AutoscalingTargets": {"description": "The autoscaling targets for an instance.", "id": "AutoscalingTargets", "properties": {"highPriorityCpuUtilizationPercent": {"description": "Required. The target high priority cpu utilization percentage that the autoscaler should be trying to achieve for the instance. This number is on a scale from 0 (no utilization) to 100 (full utilization). The valid range is [10, 90] inclusive.", "format": "int32", "type": "integer"}, "storageUtilizationPercent": {"description": "Required. The target storage utilization percentage that the autoscaler should be trying to achieve for the instance. This number is on a scale from 0 (no utilization) to 100 (full utilization). The valid range is [10, 99] inclusive.", "format": "int32", "type": "integer"}}, "type": "object"}, "Backup": {"description": "A backup of a Cloud Spanner database.", "id": "Backup", "properties": {"backupSchedules": {"description": "Output only. List of backup schedule URIs that are associated with creating this backup. This is only applicable for scheduled backups, and is empty for on-demand backups. To optimize for storage, whenever possible, multiple schedules are collapsed together to create one backup. In such cases, this field captures the list of all backup schedule URIs that are associated with creating this backup. If collapsing is not done, then this field captures the single backup schedule URI associated with creating this backup.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The time the CreateBackup request is received. If the request does not specify `version_time`, the `version_time` of the backup will be equivalent to the `create_time`.", "format": "google-datetime", "readOnly": true, "type": "string"}, "database": {"description": "Required for the CreateBackup operation. Name of the database from which this backup was created. This needs to be in the same instance as the backup. Values are of the form `projects//instances//databases/`.", "type": "string"}, "databaseDialect": {"description": "Output only. The database dialect information for the backup.", "enum": ["DATABASE_DIALECT_UNSPECIFIED", "GOOGLE_STANDARD_SQL", "POSTGRESQL"], "enumDescriptions": ["Default value. This value will create a database with the GOOGLE_STANDARD_SQL dialect.", "GoogleSQL supported SQL.", "PostgreSQL supported SQL."], "readOnly": true, "type": "string"}, "encryptionInfo": {"$ref": "EncryptionInfo", "description": "Output only. The encryption information for the backup.", "readOnly": true}, "encryptionInformation": {"description": "Output only. The encryption information for the backup, whether it is protected by one or more KMS keys. The information includes all Cloud KMS key versions used to encrypt the backup. The `encryption_status` field inside of each `EncryptionInfo` is not populated. At least one of the key versions must be available for the backup to be restored. If a key version is revoked in the middle of a restore, the restore behavior is undefined.", "items": {"$ref": "EncryptionInfo"}, "readOnly": true, "type": "array"}, "exclusiveSizeBytes": {"description": "Output only. For a backup in an incremental backup chain, this is the storage space needed to keep the data that has changed since the previous backup. For all other backups, this is always the size of the backup. This value may change if backups on the same chain get deleted or expired. This field can be used to calculate the total storage space used by a set of backups. For example, the total space used by all backups of a database can be computed by summing up this field.", "format": "int64", "readOnly": true, "type": "string"}, "expireTime": {"description": "Required for the CreateBackup operation. The expiration time of the backup, with microseconds granularity that must be at least 6 hours and at most 366 days from the time the CreateBackup request is processed. Once the `expire_time` has passed, the backup is eligible to be automatically deleted by Cloud Spanner to free the resources used by the backup.", "format": "google-datetime", "type": "string"}, "freeableSizeBytes": {"description": "Output only. The number of bytes that will be freed by deleting this backup. This value will be zero if, for example, this backup is part of an incremental backup chain and younger backups in the chain require that we keep its data. For backups not in an incremental backup chain, this is always the size of the backup. This value may change if backups on the same chain get created, deleted or expired.", "format": "int64", "readOnly": true, "type": "string"}, "incrementalBackupChainId": {"description": "Output only. Populated only for backups in an incremental backup chain. Backups share the same chain id if and only if they belong to the same incremental backup chain. Use this field to determine which backups are part of the same incremental backup chain. The ordering of backups in the chain can be determined by ordering the backup `version_time`.", "readOnly": true, "type": "string"}, "instancePartitions": {"description": "Output only. The instance partition(s) storing the backup. This is the same as the list of the instance partition(s) that the database had footprint in at the backup's `version_time`.", "items": {"$ref": "BackupInstancePartition"}, "readOnly": true, "type": "array"}, "maxExpireTime": {"description": "Output only. The max allowed expiration time of the backup, with microseconds granularity. A backup's expiration time can be configured in multiple APIs: CreateBackup, UpdateBackup, CopyBackup. When updating or copying an existing backup, the expiration time specified must be less than `Backup.max_expire_time`.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only for the CreateBackup operation. Required for the UpdateBackup operation. A globally unique identifier for the backup which cannot be changed. Values are of the form `projects//instances//backups/a-z*[a-z0-9]` The final segment of the name must be between 2 and 60 characters in length. The backup is stored in the location(s) specified in the instance configuration of the instance containing the backup, identified by the prefix of the backup name of the form `projects//instances/`.", "type": "string"}, "oldestVersionTime": {"description": "Output only. Data deleted at a time older than this is guaranteed not to be retained in order to support this backup. For a backup in an incremental backup chain, this is the version time of the oldest backup that exists or ever existed in the chain. For all other backups, this is the version time of the backup. This field can be used to understand what data is being retained by the backup system.", "format": "google-datetime", "readOnly": true, "type": "string"}, "referencingBackups": {"description": "Output only. The names of the destination backups being created by copying this source backup. The backup names are of the form `projects//instances//backups/`. Referencing backups may exist in different instances. The existence of any referencing backup prevents the backup from being deleted. When the copy operation is done (either successfully completed or cancelled or the destination backup is deleted), the reference to the backup is removed.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "referencingDatabases": {"description": "Output only. The names of the restored databases that reference the backup. The database names are of the form `projects//instances//databases/`. Referencing databases may exist in different instances. The existence of any referencing database prevents the backup from being deleted. When a restored database from the backup enters the `READY` state, the reference to the backup is removed.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "sizeBytes": {"description": "Output only. Size of the backup in bytes. For a backup in an incremental backup chain, this is the sum of the `exclusive_size_bytes` of itself and all older backups in the chain.", "format": "int64", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the backup.", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY"], "enumDescriptions": ["Not specified.", "The pending backup is still being created. Operations on the backup may fail with `FAILED_PRECONDITION` in this state.", "The backup is complete and ready for use."], "readOnly": true, "type": "string"}, "versionTime": {"description": "The backup will contain an externally consistent copy of the database at the timestamp specified by `version_time`. If `version_time` is not specified, the system will set `version_time` to the `create_time` of the backup.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "BackupInfo": {"description": "Information about a backup.", "id": "BackupInfo", "properties": {"backup": {"description": "Name of the backup.", "type": "string"}, "createTime": {"description": "The time the CreateBackup request was received.", "format": "google-datetime", "type": "string"}, "sourceDatabase": {"description": "Name of the database the backup was created from.", "type": "string"}, "versionTime": {"description": "The backup contains an externally consistent copy of `source_database` at the timestamp specified by `version_time`. If the CreateBackup request did not specify `version_time`, the `version_time` of the backup is equivalent to the `create_time`.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "BackupInstancePartition": {"description": "Instance partition information for the backup.", "id": "BackupInstancePartition", "properties": {"instancePartition": {"description": "A unique identifier for the instance partition. Values are of the form `projects//instances//instancePartitions/`", "type": "string"}}, "type": "object"}, "BackupSchedule": {"description": "BackupSchedule expresses the automated backup creation specification for a Spanner database.", "id": "BackupSchedule", "properties": {"encryptionConfig": {"$ref": "CreateBackupEncryptionConfig", "description": "Optional. The encryption configuration that is used to encrypt the backup. If this field is not specified, the backup uses the same encryption configuration as the database."}, "fullBackupSpec": {"$ref": "FullBackupSpec", "description": "The schedule creates only full backups."}, "incrementalBackupSpec": {"$ref": "IncrementalBackupSpec", "description": "The schedule creates incremental backup chains."}, "name": {"description": "Identifier. Output only for the CreateBackupSchedule operation. Required for the UpdateBackupSchedule operation. A globally unique identifier for the backup schedule which cannot be changed. Values are of the form `projects//instances//databases//backupSchedules/a-z*[a-z0-9]` The final segment of the name must be between 2 and 60 characters in length.", "type": "string"}, "retentionDuration": {"description": "Optional. The retention duration of a backup that must be at least 6 hours and at most 366 days. The backup is eligible to be automatically deleted once the retention period has elapsed.", "format": "google-duration", "type": "string"}, "spec": {"$ref": "BackupScheduleSpec", "description": "Optional. The schedule specification based on which the backup creations are triggered."}, "updateTime": {"description": "Output only. The timestamp at which the schedule was last updated. If the schedule has never been updated, this field contains the timestamp when the schedule was first created.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupScheduleSpec": {"description": "Defines specifications of the backup schedule.", "id": "BackupScheduleSpec", "properties": {"cronSpec": {"$ref": "CrontabSpec", "description": "Cron style schedule specification."}}, "type": "object"}, "BatchCreateSessionsRequest": {"description": "The request for BatchCreateSessions.", "id": "BatchCreateSessionsRequest", "properties": {"sessionCount": {"description": "Required. The number of sessions to be created in this batch call. The API can return fewer than the requested number of sessions. If a specific number of sessions are desired, the client can make additional calls to `BatchCreateSessions` (adjusting session_count as necessary).", "format": "int32", "type": "integer"}, "sessionTemplate": {"$ref": "Session", "description": "Parameters to apply to each created session."}}, "type": "object"}, "BatchCreateSessionsResponse": {"description": "The response for BatchCreateSessions.", "id": "BatchCreateSessionsResponse", "properties": {"session": {"description": "The freshly created sessions.", "items": {"$ref": "Session"}, "type": "array"}}, "type": "object"}, "BatchWriteRequest": {"description": "The request for BatchWrite.", "id": "BatchWriteRequest", "properties": {"excludeTxnFromChangeStreams": {"description": "Optional. When `exclude_txn_from_change_streams` is set to `true`: * Modifications from all transactions in this batch write operation are not be recorded in change streams with DDL option `allow_txn_exclusion=true` that are tracking columns modified by these transactions. * Modifications from all transactions in this batch write operation are recorded in change streams with DDL option `allow_txn_exclusion=false or not set` that are tracking columns modified by these transactions. When `exclude_txn_from_change_streams` is set to `false` or not set, Modifications from all transactions in this batch write operation are recorded in all change streams that are tracking columns modified by these transactions.", "type": "boolean"}, "mutationGroups": {"description": "Required. The groups of mutations to be applied.", "items": {"$ref": "MutationGroup"}, "type": "array"}, "requestOptions": {"$ref": "RequestOptions", "description": "Common options for this request."}}, "type": "object"}, "BatchWriteResponse": {"description": "The result of applying a batch of mutations.", "id": "BatchWriteResponse", "properties": {"commitTimestamp": {"description": "The commit timestamp of the transaction that applied this batch. Present if `status` is `OK`, absent otherwise.", "format": "google-datetime", "type": "string"}, "indexes": {"description": "The mutation groups applied in this batch. The values index into the `mutation_groups` field in the corresponding `BatchWriteRequest`.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "status": {"$ref": "Status", "description": "An `OK` status indicates success. Any other status indicates a failure."}}, "type": "object"}, "BeginTransactionRequest": {"description": "The request for BeginTransaction.", "id": "BeginTransactionRequest", "properties": {"mutationKey": {"$ref": "Mutation", "description": "Optional. Required for read-write transactions on a multiplexed session that commit mutations but don't perform any reads or queries. You must randomly select one of the mutations from the mutation set and send it as a part of this request."}, "options": {"$ref": "TransactionOptions", "description": "Required. Options for the new transaction."}, "requestOptions": {"$ref": "RequestOptions", "description": "Common options for this request. Priority is ignored for this request. Setting the priority in this `request_options` struct doesn't do anything. To set the priority for a transaction, set it on the reads and writes that are part of this transaction instead."}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "ChangeQuorumMetadata": {"description": "Metadata type for the long-running operation returned by ChangeQuorum.", "id": "ChangeQuorumMetadata", "properties": {"endTime": {"description": "If set, the time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "request": {"$ref": "ChangeQuorumRequest", "description": "The request for ChangeQuorum."}, "startTime": {"description": "Time the request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ChangeQuorumRequest": {"description": "The request for ChangeQuorum.", "id": "ChangeQuorumRequest", "properties": {"etag": {"description": "Optional. The etag is the hash of the `QuorumInfo`. The `ChangeQuorum` operation is only performed if the etag matches that of the `QuorumInfo` in the current database resource. Otherwise the API returns an `ABORTED` error. The etag is used for optimistic concurrency control as a way to help prevent simultaneous change quorum requests that could create a race condition.", "type": "string"}, "name": {"description": "Required. Name of the database in which to apply `ChangeQuorum`. Values are of the form `projects//instances//databases/`.", "type": "string"}, "quorumType": {"$ref": "QuorumType", "description": "Required. The type of this quorum."}}, "type": "object"}, "ChildLink": {"description": "<PERSON><PERSON><PERSON> associated with a parent-child relationship appearing in a PlanNode.", "id": "ChildLink", "properties": {"childIndex": {"description": "The node to which the link points.", "format": "int32", "type": "integer"}, "type": {"description": "The type of the link. For example, in Hash Joins this could be used to distinguish between the build child and the probe child, or in the case of the child being an output variable, to represent the tag associated with the output variable.", "type": "string"}, "variable": {"description": "Only present if the child node is SCALAR and corresponds to an output variable of the parent node. The field carries the name of the output variable. For example, a `TableScan` operator that reads rows from a table will have child links to the `SCALAR` nodes representing the output variables created for each column that is read by the operator. The corresponding `variable` fields will be set to the variable names assigned to the columns.", "type": "string"}}, "type": "object"}, "CommitRequest": {"description": "The request for Commit.", "id": "CommitRequest", "properties": {"maxCommitDelay": {"description": "Optional. The amount of latency this request is configured to incur in order to improve throughput. If this field isn't set, Spanner assumes requests are relatively latency sensitive and automatically determines an appropriate delay time. You can specify a commit delay value between 0 and 500 ms.", "format": "google-duration", "type": "string"}, "mutations": {"description": "The mutations to be executed when this transaction commits. All mutations are applied atomically, in the order they appear in this list.", "items": {"$ref": "Mutation"}, "type": "array"}, "precommitToken": {"$ref": "MultiplexedSessionPrecommitToken", "description": "Optional. If the read-write transaction was executed on a multiplexed session, then you must include the precommit token with the highest sequence number received in this transaction attempt. Failing to do so results in a `FailedPrecondition` error."}, "requestOptions": {"$ref": "RequestOptions", "description": "Common options for this request."}, "returnCommitStats": {"description": "If `true`, then statistics related to the transaction is included in the CommitResponse. Default value is `false`.", "type": "boolean"}, "singleUseTransaction": {"$ref": "TransactionOptions", "description": "Execute mutations in a temporary transaction. Note that unlike commit of a previously-started transaction, commit with a temporary transaction is non-idempotent. That is, if the `CommitRequest` is sent to Cloud Spanner more than once (for instance, due to retries in the application, or in the transport library), it's possible that the mutations are executed more than once. If this is undesirable, use BeginTransaction and Commit instead."}, "transactionId": {"description": "Commit a previously-started transaction.", "format": "byte", "type": "string"}}, "type": "object"}, "CommitResponse": {"description": "The response for Commit.", "id": "CommitResponse", "properties": {"commitStats": {"$ref": "CommitStats", "description": "The statistics about this `Commit`. Not returned by default. For more information, see CommitRequest.return_commit_stats."}, "commitTimestamp": {"description": "The Cloud Spanner timestamp at which the transaction committed.", "format": "google-datetime", "type": "string"}, "precommitToken": {"$ref": "MultiplexedSessionPrecommitToken", "description": "If specified, transaction has not committed yet. You must retry the commit with the new precommit token."}}, "type": "object"}, "CommitStats": {"description": "Additional statistics about a commit.", "id": "CommitStats", "properties": {"mutationCount": {"description": "The total number of mutations for the transaction. Knowing the `mutation_count` value can help you maximize the number of mutations in a transaction and minimize the number of API round trips. You can also monitor this value to prevent transactions from exceeding the system [limit](https://cloud.google.com/spanner/quotas#limits_for_creating_reading_updating_and_deleting_data). If the number of mutations exceeds the limit, the server returns [INVALID_ARGUMENT](https://cloud.google.com/spanner/docs/reference/rest/v1/Code#ENUM_VALUES.INVALID_ARGUMENT).", "format": "int64", "type": "string"}}, "type": "object"}, "ContextValue": {"description": "A message representing context for a KeyRangeInfo, including a label, value, unit, and severity.", "id": "ContextValue", "properties": {"label": {"$ref": "LocalizedString", "description": "The label for the context value. e.g. \"latency\"."}, "severity": {"description": "The severity of this context.", "enum": ["SEVERITY_UNSPECIFIED", "INFO", "WARNING", "ERROR", "FATAL"], "enumDescriptions": ["Required default value.", "Lowest severity level \"Info\".", "Middle severity level \"Warning\".", "Severity level signaling an error \"Error\"", "Severity level signaling a non recoverable error \"Fatal\""], "type": "string"}, "unit": {"description": "The unit of the context value.", "type": "string"}, "value": {"description": "The value for the context.", "format": "float", "type": "number"}}, "type": "object"}, "CopyBackupEncryptionConfig": {"description": "Encryption configuration for the copied backup.", "id": "CopyBackupEncryptionConfig", "properties": {"encryptionType": {"description": "Required. The encryption type of the backup.", "enum": ["ENCRYPTION_TYPE_UNSPECIFIED", "USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION", "GOOGLE_DEFAULT_ENCRYPTION", "CUSTOMER_MANAGED_ENCRYPTION"], "enumDescriptions": ["Unspecified. Do not use.", "This is the default option for CopyBackup when encryption_config is not specified. For example, if the source backup is using `Customer_Managed_Encryption`, the backup will be using the same Cloud KMS key as the source backup.", "Use Google default encryption.", "Use customer managed encryption. If specified, either `kms_key_name` or `kms_key_names` must contain valid Cloud KMS key(s)."], "type": "string"}, "kmsKeyName": {"description": "Optional. The Cloud KMS key that will be used to protect the backup. This field should be set only when encryption_type is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form `projects//locations//keyRings//cryptoKeys/`.", "type": "string"}, "kmsKeyNames": {"description": "Optional. Specifies the KMS configuration for the one or more keys used to protect the backup. Values are of the form `projects//locations//keyRings//cryptoKeys/`. KMS keys specified can be in any order. The keys referenced by `kms_key_names` must fully cover all regions of the backup's instance configuration. Some examples: * For regional (single-region) instance configurations, specify a regional location KMS key. * For multi-region instance configurations of type `GOOGLE_MANAGED`, either specify a multi-region location KMS key or multiple regional location KMS keys that cover all regions in the instance configuration. * For an instance configuration of type `USER_MANAGED`, specify only regional location KMS keys to cover each region in the instance configuration. Multi-region location KMS keys aren't supported for `USER_MANAGED` type instance configurations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CopyBackupMetadata": {"description": "Metadata type for the operation returned by CopyBackup.", "id": "CopyBackupMetadata", "properties": {"cancelTime": {"description": "The time at which cancellation of CopyBackup operation was received. Operations.CancelOperation starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "format": "google-datetime", "type": "string"}, "name": {"description": "The name of the backup being created through the copy operation. Values are of the form `projects//instances//backups/`.", "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "The progress of the CopyBackup operation."}, "sourceBackup": {"description": "The name of the source backup that is being copied. Values are of the form `projects//instances//backups/`.", "type": "string"}}, "type": "object"}, "CopyBackupRequest": {"description": "The request for CopyBackup.", "id": "CopyBackupRequest", "properties": {"backupId": {"description": "Required. The id of the backup copy. The `backup_id` appended to `parent` forms the full backup_uri of the form `projects//instances//backups/`.", "type": "string"}, "encryptionConfig": {"$ref": "CopyBackupEncryptionConfig", "description": "Optional. The encryption configuration used to encrypt the backup. If this field is not specified, the backup will use the same encryption configuration as the source backup by default, namely encryption_type = `USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION`."}, "expireTime": {"description": "Required. The expiration time of the backup in microsecond granularity. The expiration time must be at least 6 hours and at most 366 days from the `create_time` of the source backup. Once the `expire_time` has passed, the backup is eligible to be automatically deleted by Cloud Spanner to free the resources used by the backup.", "format": "google-datetime", "type": "string"}, "sourceBackup": {"description": "Required. The source backup to be copied. The source backup needs to be in READY state for it to be copied. Once CopyBackup is in progress, the source backup cannot be deleted or cleaned up on expiration until CopyBackup is finished. Values are of the form: `projects//instances//backups/`.", "type": "string"}}, "type": "object"}, "CreateBackupEncryptionConfig": {"description": "Encryption configuration for the backup to create.", "id": "CreateBackupEncryptionConfig", "properties": {"encryptionType": {"description": "Required. The encryption type of the backup.", "enum": ["ENCRYPTION_TYPE_UNSPECIFIED", "USE_DATABASE_ENCRYPTION", "GOOGLE_DEFAULT_ENCRYPTION", "CUSTOMER_MANAGED_ENCRYPTION"], "enumDescriptions": ["Unspecified. Do not use.", "Use the same encryption configuration as the database. This is the default option when encryption_config is empty. For example, if the database is using `Customer_Managed_Encryption`, the backup will be using the same Cloud KMS key as the database.", "Use Google default encryption.", "Use customer managed encryption. If specified, `kms_key_name` must contain a valid Cloud KMS key."], "type": "string"}, "kmsKeyName": {"description": "Optional. The Cloud KMS key that will be used to protect the backup. This field should be set only when encryption_type is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form `projects//locations//keyRings//cryptoKeys/`.", "type": "string"}, "kmsKeyNames": {"description": "Optional. Specifies the KMS configuration for the one or more keys used to protect the backup. Values are of the form `projects//locations//keyRings//cryptoKeys/`. The keys referenced by `kms_key_names` must fully cover all regions of the backup's instance configuration. Some examples: * For regional (single-region) instance configurations, specify a regional location KMS key. * For multi-region instance configurations of type `GOOGLE_MANAGED`, either specify a multi-region location KMS key or multiple regional location KMS keys that cover all regions in the instance configuration. * For an instance configuration of type `USER_MANAGED`, specify only regional location KMS keys to cover each region in the instance configuration. Multi-region location KMS keys aren't supported for `USER_MANAGED` type instance configurations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CreateBackupMetadata": {"description": "Metadata type for the operation returned by CreateBackup.", "id": "CreateBackupMetadata", "properties": {"cancelTime": {"description": "The time at which cancellation of this operation was received. Operations.CancelOperation starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "format": "google-datetime", "type": "string"}, "database": {"description": "The name of the database the backup is created from.", "type": "string"}, "name": {"description": "The name of the backup being created.", "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "The progress of the CreateBackup operation."}}, "type": "object"}, "CreateDatabaseMetadata": {"description": "Metadata type for the operation returned by CreateDatabase.", "id": "CreateDatabaseMetadata", "properties": {"database": {"description": "The database being created.", "type": "string"}}, "type": "object"}, "CreateDatabaseRequest": {"description": "The request for CreateDatabase.", "id": "CreateDatabaseRequest", "properties": {"createStatement": {"description": "Required. A `CREATE DATABASE` statement, which specifies the ID of the new database. The database ID must conform to the regular expression `a-z*[a-z0-9]` and be between 2 and 30 characters in length. If the database ID is a reserved word or if it contains a hyphen, the database ID must be enclosed in backticks (`` ` ``).", "type": "string"}, "databaseDialect": {"description": "Optional. The dialect of the Cloud Spanner Database.", "enum": ["DATABASE_DIALECT_UNSPECIFIED", "GOOGLE_STANDARD_SQL", "POSTGRESQL"], "enumDescriptions": ["Default value. This value will create a database with the GOOGLE_STANDARD_SQL dialect.", "GoogleSQL supported SQL.", "PostgreSQL supported SQL."], "type": "string"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Optional. The encryption configuration for the database. If this field is not specified, Cloud Spanner will encrypt/decrypt all data at rest using Google default encryption."}, "extraStatements": {"description": "Optional. A list of DDL statements to run inside the newly created database. Statements can create tables, indexes, etc. These statements execute atomically with the creation of the database: if there is an error in any statement, the database is not created.", "items": {"type": "string"}, "type": "array"}, "protoDescriptors": {"description": "Optional. Proto descriptors used by `CREATE/ALTER PROTO BUNDLE` statements in 'extra_statements'. Contains a protobuf-serialized [`google.protobuf.FileDescriptorSet`](https://github.com/protocolbuffers/protobuf/blob/main/src/google/protobuf/descriptor.proto) descriptor set. To generate it, [install](https://grpc.io/docs/protoc-installation/) and run `protoc` with --include_imports and --descriptor_set_out. For example, to generate for moon/shot/app.proto, run ``` $protoc --proto_path=/app_path --proto_path=/lib_path \\ --include_imports \\ --descriptor_set_out=descriptors.data \\ moon/shot/app.proto ``` For more details, see protobuffer [self description](https://developers.google.com/protocol-buffers/docs/techniques#self-description).", "format": "byte", "type": "string"}}, "type": "object"}, "CreateInstanceConfigMetadata": {"description": "Metadata type for the operation returned by CreateInstanceConfig.", "id": "CreateInstanceConfigMetadata", "properties": {"cancelTime": {"description": "The time at which this operation was cancelled.", "format": "google-datetime", "type": "string"}, "instanceConfig": {"$ref": "InstanceConfig", "description": "The target instance configuration end state."}, "progress": {"$ref": "InstanceOperationProgress", "description": "The progress of the CreateInstanceConfig operation."}}, "type": "object"}, "CreateInstanceConfigRequest": {"description": "The request for CreateInstanceConfig.", "id": "CreateInstanceConfigRequest", "properties": {"instanceConfig": {"$ref": "InstanceConfig", "description": "Required. The `InstanceConfig` proto of the configuration to create. `instance_config.name` must be `/instanceConfigs/`. `instance_config.base_config` must be a Google-managed configuration name, e.g. /instanceConfigs/us-east1, /instanceConfigs/nam3."}, "instanceConfigId": {"description": "Required. The ID of the instance configuration to create. Valid identifiers are of the form `custom-[-a-z0-9]*[a-z0-9]` and must be between 2 and 64 characters in length. The `custom-` prefix is required to avoid name conflicts with Google-managed configurations.", "type": "string"}, "validateOnly": {"description": "An option to validate, but not actually execute, a request, and provide the same response.", "type": "boolean"}}, "type": "object"}, "CreateInstanceMetadata": {"description": "Metadata type for the operation returned by CreateInstance.", "id": "CreateInstanceMetadata", "properties": {"cancelTime": {"description": "The time at which this operation was cancelled. If set, this operation is in the process of undoing itself (which is guaranteed to succeed) and cannot be cancelled again.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "expectedFulfillmentPeriod": {"description": "The expected fulfillment period of this create operation.", "enum": ["FULFILLMENT_PERIOD_UNSPECIFIED", "FULFILLMENT_PERIOD_NORMAL", "FULFILLMENT_PERIOD_EXTENDED"], "enumDescriptions": ["Not specified.", "Normal fulfillment period. The operation is expected to complete within minutes.", "Extended fulfillment period. It can take up to an hour for the operation to complete."], "type": "string"}, "instance": {"$ref": "Instance", "description": "The instance being created."}, "startTime": {"description": "The time at which the CreateInstance request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CreateInstancePartitionMetadata": {"description": "Metadata type for the operation returned by CreateInstancePartition.", "id": "CreateInstancePartitionMetadata", "properties": {"cancelTime": {"description": "The time at which this operation was cancelled. If set, this operation is in the process of undoing itself (which is guaranteed to succeed) and cannot be cancelled again.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "instancePartition": {"$ref": "InstancePartition", "description": "The instance partition being created."}, "startTime": {"description": "The time at which the CreateInstancePartition request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CreateInstancePartitionRequest": {"description": "The request for CreateInstancePartition.", "id": "CreateInstancePartitionRequest", "properties": {"instancePartition": {"$ref": "InstancePartition", "description": "Required. The instance partition to create. The instance_partition.name may be omitted, but if specified must be `/instancePartitions/`."}, "instancePartitionId": {"description": "Required. The ID of the instance partition to create. Valid identifiers are of the form `a-z*[a-z0-9]` and must be between 2 and 64 characters in length.", "type": "string"}}, "type": "object"}, "CreateInstanceRequest": {"description": "The request for CreateInstance.", "id": "CreateInstanceRequest", "properties": {"instance": {"$ref": "Instance", "description": "Required. The instance to create. The name may be omitted, but if specified must be `/instances/`."}, "instanceId": {"description": "Required. The ID of the instance to create. Valid identifiers are of the form `a-z*[a-z0-9]` and must be between 2 and 64 characters in length.", "type": "string"}}, "type": "object"}, "CreateSessionRequest": {"description": "The request for CreateSession.", "id": "CreateSessionRequest", "properties": {"session": {"$ref": "Session", "description": "Required. The session to create."}}, "type": "object"}, "CrontabSpec": {"description": "CrontabSpec can be used to specify the version time and frequency at which the backup is created.", "id": "CrontabSpec", "properties": {"creationWindow": {"description": "Output only. Scheduled backups contain an externally consistent copy of the database at the version time specified in `schedule_spec.cron_spec`. However, <PERSON><PERSON> might not initiate the creation of the scheduled backups at that version time. <PERSON><PERSON> initiates the creation of scheduled backups within the time window bounded by the version_time specified in `schedule_spec.cron_spec` and version_time + `creation_window`.", "format": "google-duration", "readOnly": true, "type": "string"}, "text": {"description": "Required. Textual representation of the crontab. User can customize the backup frequency and the backup version time using the cron expression. The version time must be in UTC timezone. The backup will contain an externally consistent copy of the database at the version time. Full backups must be scheduled a minimum of 12 hours apart and incremental backups must be scheduled a minimum of 4 hours apart. Examples of valid cron specifications: * `0 2/12 * * *` : every 12 hours at (2, 14) hours past midnight in UTC. * `0 2,14 * * *` : every 12 hours at (2, 14) hours past midnight in UTC. * `0 */4 * * *` : (incremental backups only) every 4 hours at (0, 4, 8, 12, 16, 20) hours past midnight in UTC. * `0 2 * * *` : once a day at 2 past midnight in UTC. * `0 2 * * 0` : once a week every Sunday at 2 past midnight in UTC. * `0 2 8 * *` : once a month on 8th day at 2 past midnight in UTC.", "type": "string"}, "timeZone": {"description": "Output only. The time zone of the times in `CrontabSpec.text`. Currently, only UTC is supported.", "readOnly": true, "type": "string"}}, "type": "object"}, "Database": {"description": "A Cloud Spanner database.", "id": "Database", "properties": {"createTime": {"description": "Output only. If exists, the time at which the database creation started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "databaseDialect": {"description": "Output only. The dialect of the Cloud Spanner Database.", "enum": ["DATABASE_DIALECT_UNSPECIFIED", "GOOGLE_STANDARD_SQL", "POSTGRESQL"], "enumDescriptions": ["Default value. This value will create a database with the GOOGLE_STANDARD_SQL dialect.", "GoogleSQL supported SQL.", "PostgreSQL supported SQL."], "readOnly": true, "type": "string"}, "defaultLeader": {"description": "Output only. The read-write region which contains the database's leader replicas. This is the same as the value of default_leader database option set using DatabaseAdmin.CreateDatabase or DatabaseAdmin.UpdateDatabaseDdl. If not explicitly set, this is empty.", "readOnly": true, "type": "string"}, "earliestVersionTime": {"description": "Output only. Earliest timestamp at which older versions of the data can be read. This value is continuously updated by Cloud Spanner and becomes stale the moment it is queried. If you are using this value to recover data, make sure to account for the time from the moment when the value is queried to the moment when you initiate the recovery.", "format": "google-datetime", "readOnly": true, "type": "string"}, "enableDropProtection": {"description": "Optional. Whether drop protection is enabled for this database. Defaults to false, if not set. For more details, please see how to [prevent accidental database deletion](https://cloud.google.com/spanner/docs/prevent-database-deletion).", "type": "boolean"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Output only. For databases that are using customer managed encryption, this field contains the encryption configuration for the database. For databases that are using Google default or other types of encryption, this field is empty.", "readOnly": true}, "encryptionInfo": {"description": "Output only. For databases that are using customer managed encryption, this field contains the encryption information for the database, such as all Cloud KMS key versions that are in use. The `encryption_status` field inside of each `EncryptionInfo` is not populated. For databases that are using Google default or other types of encryption, this field is empty. This field is propagated lazily from the backend. There might be a delay from when a key version is being used and when it appears in this field.", "items": {"$ref": "EncryptionInfo"}, "readOnly": true, "type": "array"}, "name": {"description": "Required. The name of the database. Values are of the form `projects//instances//databases/`, where `` is as specified in the `CREATE DATABASE` statement. This name can be passed to other API methods to identify the database.", "type": "string"}, "quorumInfo": {"$ref": "QuorumInfo", "description": "Output only. Applicable only for databases that use dual-region instance configurations. Contains information about the quorum.", "readOnly": true}, "reconciling": {"description": "Output only. If true, the database is being updated. If false, there are no ongoing update operations for the database.", "readOnly": true, "type": "boolean"}, "restoreInfo": {"$ref": "RestoreInfo", "description": "Output only. Applicable only for restored databases. Contains information about the restore source.", "readOnly": true}, "state": {"description": "Output only. The current database state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY", "READY_OPTIMIZING"], "enumDescriptions": ["Not specified.", "The database is still being created. Operations on the database may fail with `FAILED_PRECONDITION` in this state.", "The database is fully created and ready for use.", "The database is fully created and ready for use, but is still being optimized for performance and cannot handle full load. In this state, the database still references the backup it was restore from, preventing the backup from being deleted. When optimizations are complete, the full performance of the database will be restored, and the database will transition to `READY` state."], "readOnly": true, "type": "string"}, "versionRetentionPeriod": {"description": "Output only. The period in which Cloud Spanner retains all versions of data for the database. This is the same as the value of version_retention_period database option set using UpdateDatabaseDdl. Defaults to 1 hour, if not set.", "readOnly": true, "type": "string"}}, "type": "object"}, "DatabaseMoveConfig": {"description": "The configuration for each database in the target instance configuration.", "id": "DatabaseMoveConfig", "properties": {"databaseId": {"description": "Required. The unique identifier of the database resource in the Instance. For example if the database uri is projects/foo/instances/bar/databases/baz, the id to supply here is baz.", "type": "string"}, "encryptionConfig": {"$ref": "InstanceEncryptionConfig", "description": "Optional. Encryption configuration to be used for the database in target configuration. Should be specified for every database which currently uses CMEK encryption. If a database currently uses GOOGLE_MANAGED encryption and a target encryption config is not specified, it defaults to GOOGLE_MANAGED. If a database currently uses Google-managed encryption and a target encryption config is specified, the request is rejected. If a database currently uses CMEK encryption, a target encryption config must be specified. You cannot move a CMEK database to a Google-managed encryption database by MoveInstance."}}, "type": "object"}, "DatabaseRole": {"description": "A Cloud Spanner database role.", "id": "DatabaseRole", "properties": {"name": {"description": "Required. The name of the database role. Values are of the form `projects//instances//databases//databaseRoles/` where `` is as specified in the `CREATE ROLE` DDL statement.", "type": "string"}}, "type": "object"}, "DdlStatementActionInfo": {"description": "Action information extracted from a DDL statement. This proto is used to display the brief info of the DDL statement for the operation UpdateDatabaseDdl.", "id": "DdlStatementActionInfo", "properties": {"action": {"description": "The action for the DDL statement, e.g. CREATE, ALTER, DROP, GRANT, etc. This field is a non-empty string.", "type": "string"}, "entityNames": {"description": "The entity name(s) being operated on the DDL statement. E.g. 1. For statement \"CREATE TABLE t1(...)\", `entity_names` = [\"t1\"]. 2. For statement \"GRANT ROLE r1, r2 ...\", `entity_names` = [\"r1\", \"r2\"]. 3. For statement \"ANALYZE\", `entity_names` = [].", "items": {"type": "string"}, "type": "array"}, "entityType": {"description": "The entity type for the DDL statement, e.g. TABLE, INDEX, VIEW, etc. This field can be empty string for some DDL statement, e.g. for statement \"ANALYZE\", `entity_type` = \"\".", "type": "string"}}, "type": "object"}, "Delete": {"description": "Arguments to delete operations.", "id": "Delete", "properties": {"keySet": {"$ref": "KeySet", "description": "Required. The primary keys of the rows within table to delete. The primary keys must be specified in the order in which they appear in the `PRIMARY KEY()` clause of the table's equivalent DDL statement (the DDL statement used to create the table). Delete is idempotent. The transaction will succeed even if some or all rows do not exist."}, "table": {"description": "Required. The table whose rows will be deleted.", "type": "string"}}, "type": "object"}, "DerivedMetric": {"description": "A message representing a derived metric.", "id": "DerivedMetric", "properties": {"denominator": {"$ref": "LocalizedString", "description": "The name of the denominator metric. e.g. \"rows\"."}, "numerator": {"$ref": "LocalizedString", "description": "The name of the numerator metric. e.g. \"latency\"."}}, "type": "object"}, "DiagnosticMessage": {"description": "A message representing the key visualizer diagnostic messages.", "id": "DiagnosticMessage", "properties": {"info": {"$ref": "LocalizedString", "description": "Information about this diagnostic information."}, "metric": {"$ref": "LocalizedString", "description": "The metric."}, "metricSpecific": {"description": "Whether this message is specific only for the current metric. By default Diagnostics are shown for all metrics, regardless which metric is the currently selected metric in the UI. However occasionally a metric will generate so many messages that the resulting visual clutter becomes overwhelming. In this case setting this to true, will show the diagnostic messages for that metric only if it is the currently selected metric.", "type": "boolean"}, "severity": {"description": "The severity of the diagnostic message.", "enum": ["SEVERITY_UNSPECIFIED", "INFO", "WARNING", "ERROR", "FATAL"], "enumDescriptions": ["Required default value.", "Lowest severity level \"Info\".", "Middle severity level \"Warning\".", "Severity level signaling an error \"Error\"", "Severity level signaling a non recoverable error \"Fatal\""], "type": "string"}, "shortMessage": {"$ref": "LocalizedString", "description": "The short message."}}, "type": "object"}, "DirectedReadOptions": {"description": "The `DirectedReadOptions` can be used to indicate which replicas or regions should be used for non-transactional reads or queries. `DirectedReadOptions` can only be specified for a read-only transaction, otherwise the API returns an `INVALID_ARGUMENT` error.", "id": "DirectedReadOptions", "properties": {"excludeReplicas": {"$ref": "ExcludeReplicas", "description": "`Exclude_replicas` indicates that specified replicas should be excluded from serving requests. Spanner doesn't route requests to the replicas in this list."}, "includeReplicas": {"$ref": "IncludeReplicas", "description": "`Include_replicas` indicates the order of replicas (as they appear in this list) to process the request. If `auto_failover_disabled` is set to `true` and all replicas are exhausted without finding a healthy replica, <PERSON><PERSON> waits for a replica in the list to become available, requests might fail due to `DEADLINE_EXCEEDED` errors."}}, "type": "object"}, "DualRegionQuorum": {"description": "Message type for a dual-region quorum. Currently this type has no options.", "id": "DualRegionQuorum", "properties": {}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EncryptionConfig": {"description": "Encryption configuration for a Cloud Spanner database.", "id": "EncryptionConfig", "properties": {"kmsKeyName": {"description": "The Cloud KMS key to be used for encrypting and decrypting the database. Values are of the form `projects//locations//keyRings//cryptoKeys/`.", "type": "string"}, "kmsKeyNames": {"description": "Specifies the KMS configuration for one or more keys used to encrypt the database. Values are of the form `projects//locations//keyRings//cryptoKeys/`. The keys referenced by `kms_key_names` must fully cover all regions of the database's instance configuration. Some examples: * For regional (single-region) instance configurations, specify a regional location KMS key. * For multi-region instance configurations of type `GOOGLE_MANAGED`, either specify a multi-region location KMS key or multiple regional location KMS keys that cover all regions in the instance configuration. * For an instance configuration of type `USER_MANAGED`, specify only regional location KMS keys to cover each region in the instance configuration. Multi-region location KMS keys aren't supported for `USER_MANAGED` type instance configurations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "EncryptionInfo": {"description": "Encryption information for a Cloud Spanner database or backup.", "id": "EncryptionInfo", "properties": {"encryptionStatus": {"$ref": "Status", "description": "Output only. If present, the status of a recent encrypt/decrypt call on underlying data for this database or backup. Regardless of status, data is always encrypted at rest.", "readOnly": true}, "encryptionType": {"description": "Output only. The type of encryption.", "enum": ["TYPE_UNSPECIFIED", "GOOGLE_DEFAULT_ENCRYPTION", "CUSTOMER_MANAGED_ENCRYPTION"], "enumDescriptions": ["Encryption type was not specified, though data at rest remains encrypted.", "The data is encrypted at rest with a key that is fully managed by Google. No key version or status will be populated. This is the default state.", "The data is encrypted at rest with a key that is managed by the customer. The active version of the key. `kms_key_version` will be populated, and `encryption_status` may be populated."], "readOnly": true, "type": "string"}, "kmsKeyVersion": {"description": "Output only. A Cloud KMS key version that is being used to protect the database or backup.", "readOnly": true, "type": "string"}}, "type": "object"}, "ExcludeReplicas": {"description": "An ExcludeReplicas contains a repeated set of ReplicaSelection that should be excluded from serving requests.", "id": "ExcludeReplicas", "properties": {"replicaSelections": {"description": "The directed read replica selector.", "items": {"$ref": "ReplicaSelection"}, "type": "array"}}, "type": "object"}, "ExecuteBatchDmlRequest": {"description": "The request for ExecuteBatchDml.", "id": "ExecuteBatchDmlRequest", "properties": {"lastStatements": {"description": "Optional. If set to `true`, this request marks the end of the transaction. After these statements execute, you must commit or abort the transaction. Attempts to execute any other requests against this transaction (including reads and queries) are rejected. Setting this option might cause some error reporting to be deferred until commit time (for example, validation of unique constraints). Given this, successful execution of statements shouldn't be assumed until a subsequent `Commit` call completes successfully.", "type": "boolean"}, "requestOptions": {"$ref": "RequestOptions", "description": "Common options for this request."}, "seqno": {"description": "Required. A per-transaction sequence number used to identify this request. This field makes each request idempotent such that if the request is received multiple times, at most one succeeds. The sequence number must be monotonically increasing within the transaction. If a request arrives for the first time with an out-of-order sequence number, the transaction might be aborted. Replays of previously handled requests yield the same response as the first execution.", "format": "int64", "type": "string"}, "statements": {"description": "Required. The list of statements to execute in this batch. Statements are executed serially, such that the effects of statement `i` are visible to statement `i+1`. Each statement must be a DML statement. Execution stops at the first failed statement; the remaining statements are not executed. Callers must provide at least one statement.", "items": {"$ref": "Statement"}, "type": "array"}, "transaction": {"$ref": "TransactionSelector", "description": "Required. The transaction to use. Must be a read-write transaction. To protect against replays, single-use transactions are not supported. The caller must either supply an existing transaction ID or begin a new transaction."}}, "type": "object"}, "ExecuteBatchDmlResponse": {"description": "The response for ExecuteBatchDml. Contains a list of ResultSet messages, one for each DML statement that has successfully executed, in the same order as the statements in the request. If a statement fails, the status in the response body identifies the cause of the failure. To check for DML statements that failed, use the following approach: 1. Check the status in the response message. The google.rpc.Code enum value `OK` indicates that all statements were executed successfully. 2. If the status was not `OK`, check the number of result sets in the response. If the response contains `N` ResultSet messages, then statement `N+1` in the request failed. Example 1: * Request: 5 DML statements, all executed successfully. * Response: 5 ResultSet messages, with the status `OK`. Example 2: * Request: 5 DML statements. The third statement has a syntax error. * Response: 2 ResultSet messages, and a syntax error (`INVALID_ARGUMENT`) status. The number of ResultSet messages indicates that the third statement failed, and the fourth and fifth statements were not executed.", "id": "ExecuteBatchDmlResponse", "properties": {"precommitToken": {"$ref": "MultiplexedSessionPrecommitToken", "description": "Optional. A precommit token is included if the read-write transaction is on a multiplexed session. Pass the precommit token with the highest sequence number from this transaction attempt should be passed to the Commit request for this transaction."}, "resultSets": {"description": "One ResultSet for each statement in the request that ran successfully, in the same order as the statements in the request. Each ResultSet does not contain any rows. The ResultSetStats in each ResultSet contain the number of rows modified by the statement. Only the first ResultSet in the response contains valid ResultSetMetadata.", "items": {"$ref": "ResultSet"}, "type": "array"}, "status": {"$ref": "Status", "description": "If all DML statements are executed successfully, the status is `OK`. Otherwise, the error status of the first failed statement."}}, "type": "object"}, "ExecuteSqlRequest": {"description": "The request for ExecuteSql and ExecuteStreamingSql.", "id": "ExecuteSqlRequest", "properties": {"dataBoostEnabled": {"description": "If this is for a partitioned query and this field is set to `true`, the request is executed with Spanner Data Boost independent compute resources. If the field is set to `true` but the request doesn't set `partition_token`, the API returns an `INVALID_ARGUMENT` error.", "type": "boolean"}, "directedReadOptions": {"$ref": "DirectedReadOptions", "description": "Directed read options for this request."}, "lastStatement": {"description": "Optional. If set to `true`, this statement marks the end of the transaction. After this statement executes, you must commit or abort the transaction. Attempts to execute any other requests against this transaction (including reads and queries) are rejected. For DML statements, setting this option might cause some error reporting to be deferred until commit time (for example, validation of unique constraints). Given this, successful execution of a DML statement shouldn't be assumed until a subsequent `Commit` call completes successfully.", "type": "boolean"}, "paramTypes": {"additionalProperties": {"$ref": "Type"}, "description": "It isn't always possible for Cloud Spanner to infer the right SQL type from a JSON value. For example, values of type `BYTES` and values of type `STRING` both appear in params as JSON strings. In these cases, you can use `param_types` to specify the exact SQL type for some or all of the SQL statement parameters. See the definition of Type for more information about SQL types.", "type": "object"}, "params": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Parameter names and values that bind to placeholders in the SQL string. A parameter placeholder consists of the `@` character followed by the parameter name (for example, `@firstName`). Parameter names must conform to the naming requirements of identifiers as specified at https://cloud.google.com/spanner/docs/lexical#identifiers. Parameters can appear anywhere that a literal value is expected. The same parameter name can be used more than once, for example: `\"WHERE id > @msg_id AND id < @msg_id + 100\"` It's an error to execute a SQL statement with unbound parameters.", "type": "object"}, "partitionToken": {"description": "If present, results are restricted to the specified partition previously created using `PartitionQuery`. There must be an exact match for the values of fields common to this message and the `PartitionQueryRequest` message used to create this `partition_token`.", "format": "byte", "type": "string"}, "queryMode": {"description": "Used to control the amount of debugging information returned in ResultSetStats. If partition_token is set, query_mode can only be set to QueryMode.NORMAL.", "enum": ["NORMAL", "PLAN", "PROFILE", "WITH_STATS", "WITH_PLAN_AND_STATS"], "enumDescriptions": ["The default mode. Only the statement results are returned.", "This mode returns only the query plan, without any results or execution statistics information.", "This mode returns the query plan, overall execution statistics, operator level execution statistics along with the results. This has a performance overhead compared to the other modes. It isn't recommended to use this mode for production traffic.", "This mode returns the overall (but not operator-level) execution statistics along with the results.", "This mode returns the query plan, overall (but not operator-level) execution statistics along with the results."], "type": "string"}, "queryOptions": {"$ref": "QueryOptions", "description": "Query optimizer configuration to use for the given query."}, "requestOptions": {"$ref": "RequestOptions", "description": "Common options for this request."}, "resumeToken": {"description": "If this request is resuming a previously interrupted SQL statement execution, `resume_token` should be copied from the last PartialResultSet yielded before the interruption. Doing this enables the new SQL statement execution to resume where the last one left off. The rest of the request parameters must exactly match the request that yielded this token.", "format": "byte", "type": "string"}, "seqno": {"description": "A per-transaction sequence number used to identify this request. This field makes each request idempotent such that if the request is received multiple times, at most one succeeds. The sequence number must be monotonically increasing within the transaction. If a request arrives for the first time with an out-of-order sequence number, the transaction can be aborted. Replays of previously handled requests yield the same response as the first execution. Required for DML statements. Ignored for queries.", "format": "int64", "type": "string"}, "sql": {"description": "Required. The SQL string.", "type": "string"}, "transaction": {"$ref": "TransactionSelector", "description": "The transaction to use. For queries, if none is provided, the default is a temporary read-only transaction with strong concurrency. Standard DML statements require a read-write transaction. To protect against replays, single-use transactions are not supported. The caller must either supply an existing transaction ID or begin a new transaction. Partitioned DML requires an existing Partitioned DML transaction ID."}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "Field": {"description": "Message representing a single field of a struct.", "id": "Field", "properties": {"name": {"description": "The name of the field. For reads, this is the column name. For SQL queries, it is the column alias (e.g., `\"Word\"` in the query `\"SELECT 'hello' AS Word\"`), or the column name (e.g., `\"ColName\"` in the query `\"SELECT ColName FROM Table\"`). Some columns might have an empty name (e.g., `\"SELECT UPPER(ColName)\"`). Note that a query result can contain multiple fields with the same name.", "type": "string"}, "type": {"$ref": "Type", "description": "The type of the field."}}, "type": "object"}, "FreeInstanceMetadata": {"description": "Free instance specific metadata that is kept even after an instance has been upgraded for tracking purposes.", "id": "FreeInstanceMetadata", "properties": {"expireBehavior": {"description": "Specifies the expiration behavior of a free instance. The default of ExpireBehavior is `REMOVE_AFTER_GRACE_PERIOD`. This can be modified during or after creation, and before expiration.", "enum": ["EXPIRE_BEHAVIOR_UNSPECIFIED", "FREE_TO_PROVISIONED", "REMOVE_AFTER_GRACE_PERIOD"], "enumDescriptions": ["Not specified.", "When the free instance expires, upgrade the instance to a provisioned instance.", "When the free instance expires, disable the instance, and delete it after the grace period passes if it has not been upgraded."], "type": "string"}, "expireTime": {"description": "Output only. Timestamp after which the instance will either be upgraded or scheduled for deletion after a grace period. ExpireBehavior is used to choose between upgrading or scheduling the free instance for deletion. This timestamp is set during the creation of a free instance.", "format": "google-datetime", "readOnly": true, "type": "string"}, "upgradeTime": {"description": "Output only. If present, the timestamp at which the free instance was upgraded to a provisioned instance.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "FullBackupSpec": {"description": "The specification for full backups. A full backup stores the entire contents of the database at a given version time.", "id": "FullBackupSpec", "properties": {}, "type": "object"}, "GetDatabaseDdlResponse": {"description": "The response for GetDatabaseDdl.", "id": "GetDatabaseDdlResponse", "properties": {"protoDescriptors": {"description": "Proto descriptors stored in the database. Contains a protobuf-serialized [google.protobuf.FileDescriptorSet](https://github.com/protocolbuffers/protobuf/blob/main/src/google/protobuf/descriptor.proto). For more details, see protobuffer [self description](https://developers.google.com/protocol-buffers/docs/techniques#self-description).", "format": "byte", "type": "string"}, "statements": {"description": "A list of formatted DDL statements defining the schema of the database specified in the request.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "IncludeReplicas": {"description": "An `IncludeReplicas` contains a repeated set of `ReplicaSelection` which indicates the order in which replicas should be considered.", "id": "IncludeReplicas", "properties": {"autoFailoverDisabled": {"description": "If `true`, <PERSON><PERSON> doesn't route requests to a replica outside the <`include_replicas` list when all of the specified replicas are unavailable or unhealthy. Default value is `false`.", "type": "boolean"}, "replicaSelections": {"description": "The directed read replica selector.", "items": {"$ref": "ReplicaSelection"}, "type": "array"}}, "type": "object"}, "IncrementalBackupSpec": {"description": "The specification for incremental backup chains. An incremental backup stores the delta of changes between a previous backup and the database contents at a given version time. An incremental backup chain consists of a full backup and zero or more successive incremental backups. The first backup created for an incremental backup chain is always a full backup.", "id": "IncrementalBackupSpec", "properties": {}, "type": "object"}, "IndexAdvice": {"description": "Recommendation to add new indexes to run queries more efficiently.", "id": "IndexAdvice", "properties": {"ddl": {"description": "Optional. DDL statements to add new indexes that will improve the query.", "items": {"type": "string"}, "type": "array"}, "improvementFactor": {"description": "Optional. Estimated latency improvement factor. For example if the query currently takes 500 ms to run and the estimated latency with new indexes is 100 ms this field will be 5.", "format": "double", "type": "number"}}, "type": "object"}, "IndexedHotKey": {"description": "A message representing a (sparse) collection of hot keys for specific key buckets.", "id": "IndexedHotKey", "properties": {"sparseHotKeys": {"additionalProperties": {"format": "int32", "type": "integer"}, "description": "A (sparse) mapping from key bucket index to the index of the specific hot row key for that key bucket. The index of the hot row key can be translated to the actual row key via the ScanData.VisualizationData.indexed_keys repeated field.", "type": "object"}}, "type": "object"}, "IndexedKeyRangeInfos": {"description": "A message representing a (sparse) collection of KeyRangeInfos for specific key buckets.", "id": "IndexedKeyRangeInfos", "properties": {"keyRangeInfos": {"additionalProperties": {"$ref": "KeyRangeInfos"}, "description": "A (sparse) mapping from key bucket index to the KeyRangeInfos for that key bucket.", "type": "object"}}, "type": "object"}, "Instance": {"description": "An isolated set of Cloud Spanner resources on which databases can be hosted.", "id": "Instance", "properties": {"autoscalingConfig": {"$ref": "AutoscalingConfig", "description": "Optional. The autoscaling configuration. Autoscaling is enabled if this field is set. When autoscaling is enabled, node_count and processing_units are treated as OUTPUT_ONLY fields and reflect the current compute capacity allocated to the instance."}, "config": {"description": "Required. The name of the instance's configuration. Values are of the form `projects//instanceConfigs/`. See also InstanceConfig and ListInstanceConfigs.", "type": "string"}, "createTime": {"description": "Output only. The time at which the instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "defaultBackupScheduleType": {"description": "Optional. Controls the default backup schedule behavior for new databases within the instance. By default, a backup schedule is created automatically when a new database is created in a new instance. Note that the `AUTOMATIC` value isn't permitted for free instances, as backups and backup schedules aren't supported for free instances. In the `GetInstance` or `ListInstances` response, if the value of `default_backup_schedule_type` isn't set, or set to `NONE`, Spanner doesn't create a default backup schedule for new databases in the instance.", "enum": ["DEFAULT_<PERSON>C<PERSON>UP_SCHEDULE_TYPE_UNSPECIFIED", "NONE", "AUTOMATIC"], "enumDescriptions": ["Not specified.", "A default backup schedule isn't created automatically when a new database is created in the instance.", "A default backup schedule is created automatically when a new database is created in the instance. The default backup schedule creates a full backup every 24 hours. These full backups are retained for 7 days. You can edit or delete the default backup schedule once it's created."], "type": "string"}, "displayName": {"description": "Required. The descriptive name for this instance as it appears in UIs. Must be unique per project and between 4 and 30 characters in length.", "type": "string"}, "edition": {"description": "Optional. The `Edition` of the current instance.", "enum": ["EDITION_UNSPECIFIED", "STANDARD", "ENTERPRISE", "ENTERPRISE_PLUS"], "enumDescriptions": ["Edition not specified.", "Standard edition.", "Enterprise edition.", "Enterprise Plus edition."], "type": "string"}, "endpointUris": {"description": "Deprecated. This field is not populated.", "items": {"type": "string"}, "type": "array"}, "freeInstanceMetadata": {"$ref": "FreeInstanceMetadata", "description": "Free instance metadata. Only populated for free instances."}, "instanceType": {"description": "The `InstanceType` of the current instance.", "enum": ["INSTANCE_TYPE_UNSPECIFIED", "PROVISIONED", "FREE_INSTANCE"], "enumDescriptions": ["Not specified.", "Provisioned instances have dedicated resources, standard usage limits and support.", "Free instances provide no guarantee for dedicated resources, [node_count, processing_units] should be 0. They come with stricter usage limits and limited support."], "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cloud Labels are a flexible and lightweight mechanism for organizing cloud resources into groups that reflect a customer's organizational needs and deployment strategies. Cloud Labels can be used to filter collections of resources. They can be used to control how resource metrics are aggregated. And they can be used as arguments to policy management rules (e.g. route, firewall, load balancing, etc.). * Label keys must be between 1 and 63 characters long and must conform to the following regular expression: `a-z{0,62}`. * Label values must be between 0 and 63 characters long and must conform to the regular expression `[a-z0-9_-]{0,63}`. * No more than 64 labels can be associated with a given resource. See https://goo.gl/xmQnxf for more information on and examples of labels. If you plan to use labels in your own code, please note that additional characters may be allowed in the future. And so you are advised to use an internal label representation, such as JSON, which doesn't rely upon specific characters being disallowed. For example, representing labels as the string: name + \"_\" + value would prove problematic if we were to allow \"_\" in a future release.", "type": "object"}, "name": {"description": "Required. A unique identifier for the instance, which cannot be changed after the instance is created. Values are of the form `projects//instances/a-z*[a-z0-9]`. The final segment of the name must be between 2 and 64 characters in length.", "type": "string"}, "nodeCount": {"description": "The number of nodes allocated to this instance. At most, one of either `node_count` or `processing_units` should be present in the message. Users can set the `node_count` field to specify the target number of nodes allocated to the instance. If autoscaling is enabled, `node_count` is treated as an `OUTPUT_ONLY` field and reflects the current number of nodes allocated to the instance. This might be zero in API responses for instances that are not yet in the `READY` state. If the instance has varying node count across replicas (achieved by setting `asymmetric_autoscaling_options` in the autoscaling configuration), the `node_count` set here is the maximum node count across all replicas. For more information, see [Compute capacity, nodes, and processing units](https://cloud.google.com/spanner/docs/compute-capacity).", "format": "int32", "type": "integer"}, "processingUnits": {"description": "The number of processing units allocated to this instance. At most, one of either `processing_units` or `node_count` should be present in the message. Users can set the `processing_units` field to specify the target number of processing units allocated to the instance. If autoscaling is enabled, `processing_units` is treated as an `OUTPUT_ONLY` field and reflects the current number of processing units allocated to the instance. This might be zero in API responses for instances that are not yet in the `READY` state. If the instance has varying processing units per replica (achieved by setting `asymmetric_autoscaling_options` in the autoscaling configuration), the `processing_units` set here is the maximum processing units across all replicas. For more information, see [Compute capacity, nodes and processing units](https://cloud.google.com/spanner/docs/compute-capacity).", "format": "int32", "type": "integer"}, "replicaComputeCapacity": {"description": "Output only. Lists the compute capacity per ReplicaSelection. A replica selection identifies a set of replicas with common properties. Replicas identified by a ReplicaSelection are scaled with the same compute capacity.", "items": {"$ref": "ReplicaComputeCapacity"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The current instance state. For CreateInstance, the state must be either omitted or set to `CREATING`. For UpdateInstance, the state must be either omitted or set to `READY`.", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY"], "enumDescriptions": ["Not specified.", "The instance is still being created. Resources may not be available yet, and operations such as database creation may not work.", "The instance is fully created and ready to do work such as creating databases."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which the instance was most recently updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "InstanceConfig": {"description": "A possible configuration for a Cloud Spanner instance. Configurations define the geographic placement of nodes and their replication.", "id": "InstanceConfig", "properties": {"baseConfig": {"description": "Base configuration name, e.g. projects//instanceConfigs/nam3, based on which this configuration is created. Only set for user-managed configurations. `base_config` must refer to a configuration of type `GOOGLE_MANAGED` in the same project as this configuration.", "type": "string"}, "configType": {"description": "Output only. Whether this instance configuration is a Google-managed or user-managed configuration.", "enum": ["TYPE_UNSPECIFIED", "GOOGLE_MANAGED", "USER_MANAGED"], "enumDescriptions": ["Unspecified.", "Google-managed configuration.", "User-managed configuration."], "readOnly": true, "type": "string"}, "displayName": {"description": "The name of this instance configuration as it appears in UIs.", "type": "string"}, "etag": {"description": "etag is used for optimistic concurrency control as a way to help prevent simultaneous updates of a instance configuration from overwriting each other. It is strongly suggested that systems make use of the etag in the read-modify-write cycle to perform instance configuration updates in order to avoid race conditions: An etag is returned in the response which contains instance configurations, and systems are expected to put that etag in the request to update instance configuration to ensure that their change is applied to the same version of the instance configuration. If no etag is provided in the call to update the instance configuration, then the existing instance configuration is overwritten blindly.", "type": "string"}, "freeInstanceAvailability": {"description": "Output only. Describes whether free instances are available to be created in this instance configuration.", "enum": ["FREE_INSTANCE_AVAILABILITY_UNSPECIFIED", "AVAILABLE", "UNSUPPORTED", "DISABLED", "QUOTA_EXCEEDED"], "enumDescriptions": ["Not specified.", "Indicates that free instances are available to be created in this instance configuration.", "Indicates that free instances are not supported in this instance configuration.", "Indicates that free instances are currently not available to be created in this instance configuration.", "Indicates that additional free instances cannot be created in this instance configuration because the project has reached its limit of free instances."], "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cloud Labels are a flexible and lightweight mechanism for organizing cloud resources into groups that reflect a customer's organizational needs and deployment strategies. Cloud Labels can be used to filter collections of resources. They can be used to control how resource metrics are aggregated. And they can be used as arguments to policy management rules (e.g. route, firewall, load balancing, etc.). * Label keys must be between 1 and 63 characters long and must conform to the following regular expression: `a-z{0,62}`. * Label values must be between 0 and 63 characters long and must conform to the regular expression `[a-z0-9_-]{0,63}`. * No more than 64 labels can be associated with a given resource. See https://goo.gl/xmQnxf for more information on and examples of labels. If you plan to use labels in your own code, please note that additional characters may be allowed in the future. Therefore, you are advised to use an internal label representation, such as JSON, which doesn't rely upon specific characters being disallowed. For example, representing labels as the string: name + \"_\" + value would prove problematic if we were to allow \"_\" in a future release.", "type": "object"}, "leaderOptions": {"description": "Allowed values of the \"default_leader\" schema option for databases in instances that use this instance configuration.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "A unique identifier for the instance configuration. Values are of the form `projects//instanceConfigs/a-z*`. User instance configuration must start with `custom-`.", "type": "string"}, "optionalReplicas": {"description": "Output only. The available optional replicas to choose from for user-managed configurations. Populated for Google-managed configurations.", "items": {"$ref": "ReplicaInfo"}, "readOnly": true, "type": "array"}, "quorumType": {"description": "Output only. The `QuorumType` of the instance configuration.", "enum": ["QUORUM_TYPE_UNSPECIFIED", "REGION", "DUAL_REGION", "MULTI_REGION"], "enumDescriptions": ["Quorum type not specified.", "An instance configuration tagged with `REGION` quorum type forms a write quorum in a single region.", "An instance configuration tagged with the `DUAL_REGION` quorum type forms a write quorum with exactly two read-write regions in a multi-region configuration. This instance configuration requires failover in the event of regional failures.", "An instance configuration tagged with the `MULTI_REGION` quorum type forms a write quorum from replicas that are spread across more than one region in a multi-region configuration."], "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. If true, the instance configuration is being created or updated. If false, there are no ongoing operations for the instance configuration.", "readOnly": true, "type": "boolean"}, "replicas": {"description": "The geographic placement of nodes in this instance configuration and their replication properties. To create user-managed configurations, input `replicas` must include all replicas in `replicas` of the `base_config` and include one or more replicas in the `optional_replicas` of the `base_config`.", "items": {"$ref": "ReplicaInfo"}, "type": "array"}, "state": {"description": "Output only. The current instance configuration state. Applicable only for `USER_MANAGED` configurations.", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY"], "enumDescriptions": ["Not specified.", "The instance configuration is still being created.", "The instance configuration is fully created and ready to be used to create instances."], "readOnly": true, "type": "string"}, "storageLimitPerProcessingUnit": {"description": "Output only. The storage limit in bytes per processing unit.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "InstanceEncryptionConfig": {"description": "Encryption configuration for a Cloud Spanner database.", "id": "InstanceEncryptionConfig", "properties": {"kmsKeyName": {"description": "Optional. This field is maintained for backwards compatibility. For new callers, we recommend using `kms_key_names` to specify the KMS key. `kms_key_name` should only be used if the location of the KMS key matches the database instance’s configuration (location) exactly. E.g. The KMS location is in us-central1 or nam3 and the database instance is also in us-central1 or nam3. The Cloud KMS key to be used for encrypting and decrypting the database. Values are of the form `projects//locations//keyRings//cryptoKeys/`.", "type": "string"}, "kmsKeyNames": {"description": "Optional. Specifies the KMS configuration for one or more keys used to encrypt the database. Values are of the form `projects//locations//keyRings//cryptoKeys/`. The keys referenced by `kms_key_names` must fully cover all regions of the database's instance configuration. Some examples: * For regional (single-region) instance configurations, specify a regional location KMS key. * For multi-region instance configurations of type `GOOGLE_MANAGED`, either specify a multi-region location KMS key or multiple regional location KMS keys that cover all regions in the instance configuration. * For an instance configuration of type `USER_MANAGED`, specify only regional location KMS keys to cover each region in the instance configuration. Multi-region location KMS keys aren't supported for `USER_MANAGED` type instance configurations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "InstanceOperationProgress": {"description": "Encapsulates progress related information for a Cloud Spanner long running instance operations.", "id": "InstanceOperationProgress", "properties": {"endTime": {"description": "If set, the time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "progressPercent": {"description": "Percent completion of the operation. Values are between 0 and 100 inclusive.", "format": "int32", "type": "integer"}, "startTime": {"description": "Time the request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "InstancePartition": {"description": "An isolated set of Cloud Spanner resources that databases can define placements on.", "id": "InstancePartition", "properties": {"config": {"description": "Required. The name of the instance partition's configuration. Values are of the form `projects//instanceConfigs/`. See also InstanceConfig and ListInstanceConfigs.", "type": "string"}, "createTime": {"description": "Output only. The time at which the instance partition was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The descriptive name for this instance partition as it appears in UIs. Must be unique per project and between 4 and 30 characters in length.", "type": "string"}, "etag": {"description": "Used for optimistic concurrency control as a way to help prevent simultaneous updates of a instance partition from overwriting each other. It is strongly suggested that systems make use of the etag in the read-modify-write cycle to perform instance partition updates in order to avoid race conditions: An etag is returned in the response which contains instance partitions, and systems are expected to put that etag in the request to update instance partitions to ensure that their change will be applied to the same version of the instance partition. If no etag is provided in the call to update instance partition, then the existing instance partition is overwritten blindly.", "type": "string"}, "name": {"description": "Required. A unique identifier for the instance partition. Values are of the form `projects//instances//instancePartitions/a-z*[a-z0-9]`. The final segment of the name must be between 2 and 64 characters in length. An instance partition's name cannot be changed after the instance partition is created.", "type": "string"}, "nodeCount": {"description": "The number of nodes allocated to this instance partition. Users can set the `node_count` field to specify the target number of nodes allocated to the instance partition. This may be zero in API responses for instance partitions that are not yet in state `READY`.", "format": "int32", "type": "integer"}, "processingUnits": {"description": "The number of processing units allocated to this instance partition. Users can set the `processing_units` field to specify the target number of processing units allocated to the instance partition. This might be zero in API responses for instance partitions that are not yet in the `READY` state.", "format": "int32", "type": "integer"}, "referencingBackups": {"deprecated": true, "description": "Output only. Deprecated: This field is not populated. Output only. The names of the backups that reference this instance partition. Referencing backups should share the parent instance. The existence of any referencing backup prevents the instance partition from being deleted.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "referencingDatabases": {"description": "Output only. The names of the databases that reference this instance partition. Referencing databases should share the parent instance. The existence of any referencing database prevents the instance partition from being deleted.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The current instance partition state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY"], "enumDescriptions": ["Not specified.", "The instance partition is still being created. Resources may not be available yet, and operations such as creating placements using this instance partition may not work.", "The instance partition is fully created and ready to do work such as creating placements and using in databases."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which the instance partition was most recently updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "InstanceReplicaSelection": {"description": "ReplicaSelection identifies replicas with common properties.", "id": "InstanceReplicaSelection", "properties": {"location": {"description": "Required. Name of the location of the replicas (e.g., \"us-central1\").", "type": "string"}}, "type": "object"}, "Key": {"description": "A split key.", "id": "Key", "properties": {"keyParts": {"description": "Required. The column values making up the split key.", "items": {"type": "any"}, "type": "array"}}, "type": "object"}, "KeyRange": {"description": "KeyRange represents a range of rows in a table or index. A range has a start key and an end key. These keys can be open or closed, indicating if the range includes rows with that key. Keys are represented by lists, where the ith value in the list corresponds to the ith component of the table or index primary key. Individual values are encoded as described here. For example, consider the following table definition: CREATE TABLE UserEvents ( UserName STRING(MAX), EventDate STRING(10) ) PRIMARY KEY(UserName, EventDate); The following keys name rows in this table: \"<PERSON>\", \"2014-09-23\" Since the `UserEvents` table's `PRIMARY KEY` clause names two columns, each `UserEvents` key has two elements; the first is the `UserName`, and the second is the `EventDate`. Key ranges with multiple components are interpreted lexicographically by component using the table or index key's declared sort order. For example, the following range returns all events for user `\"Bob\"` that occurred in the year 2015: \"start_closed\": [\"Bob\", \"2015-01-01\"] \"end_closed\": [\"<PERSON>\", \"2015-12-31\"] Start and end keys can omit trailing key components. This affects the inclusion and exclusion of rows that exactly match the provided key components: if the key is closed, then rows that exactly match the provided components are included; if the key is open, then rows that exactly match are not included. For example, the following range includes all events for `\"Bob\"` that occurred during and after the year 2000: \"start_closed\": [\"Bob\", \"2000-01-01\"] \"end_closed\": [\"Bob\"] The next example retrieves all events for `\"Bob\"`: \"start_closed\": [\"Bob\"] \"end_closed\": [\"Bob\"] To retrieve events before the year 2000: \"start_closed\": [\"Bob\"] \"end_open\": [\"Bob\", \"2000-01-01\"] The following range includes all rows in the table: \"start_closed\": [] \"end_closed\": [] This range returns all users whose `UserName` begins with any character from A to C: \"start_closed\": [\"A\"] \"end_open\": [\"D\"] This range returns all users whose `UserName` begins with B: \"start_closed\": [\"B\"] \"end_open\": [\"C\"] Key ranges honor column sort order. For example, suppose a table is defined as follows: CREATE TABLE DescendingSortedTable { Key INT64, ... ) PRIMARY KEY(Key DESC); The following range retrieves all rows with key values between 1 and 100 inclusive: \"start_closed\": [\"100\"] \"end_closed\": [\"1\"] Note that 100 is passed as the start, and 1 is passed as the end, because `Key` is a descending column in the schema.", "id": "KeyRange", "properties": {"endClosed": {"description": "If the end is closed, then the range includes all rows whose first `len(end_closed)` key columns exactly match `end_closed`.", "items": {"type": "any"}, "type": "array"}, "endOpen": {"description": "If the end is open, then the range excludes rows whose first `len(end_open)` key columns exactly match `end_open`.", "items": {"type": "any"}, "type": "array"}, "startClosed": {"description": "If the start is closed, then the range includes all rows whose first `len(start_closed)` key columns exactly match `start_closed`.", "items": {"type": "any"}, "type": "array"}, "startOpen": {"description": "If the start is open, then the range excludes rows whose first `len(start_open)` key columns exactly match `start_open`.", "items": {"type": "any"}, "type": "array"}}, "type": "object"}, "KeyRangeInfo": {"description": "A message representing information for a key range (possibly one key).", "id": "KeyRangeInfo", "properties": {"contextValues": {"description": "The list of context values for this key range.", "items": {"$ref": "ContextValue"}, "type": "array"}, "endKeyIndex": {"description": "The index of the end key in indexed_keys.", "format": "int32", "type": "integer"}, "info": {"$ref": "LocalizedString", "description": "Information about this key range, for all metrics."}, "keysCount": {"description": "The number of keys this range covers.", "format": "int64", "type": "string"}, "metric": {"$ref": "LocalizedString", "description": "The name of the metric. e.g. \"latency\"."}, "startKeyIndex": {"description": "The index of the start key in indexed_keys.", "format": "int32", "type": "integer"}, "timeOffset": {"description": "The time offset. This is the time since the start of the time interval.", "format": "google-duration", "type": "string"}, "unit": {"$ref": "LocalizedString", "description": "The unit of the metric. This is an unstructured field and will be mapped as is to the user."}, "value": {"description": "The value of the metric.", "format": "float", "type": "number"}}, "type": "object"}, "KeyRangeInfos": {"description": "A message representing a list of specific information for multiple key ranges.", "id": "KeyRangeInfos", "properties": {"infos": {"description": "The list individual KeyRangeInfos.", "items": {"$ref": "KeyRangeInfo"}, "type": "array"}, "totalSize": {"description": "The total size of the list of all KeyRangeInfos. This may be larger than the number of repeated messages above. If that is the case, this number may be used to determine how many are not being shown.", "format": "int32", "type": "integer"}}, "type": "object"}, "KeySet": {"description": "`KeySet` defines a collection of Cloud Spanner keys and/or key ranges. All the keys are expected to be in the same table or index. The keys need not be sorted in any particular way. If the same key is specified multiple times in the set (for example if two ranges, two keys, or a key and a range overlap), Cloud Spanner behaves as if the key were only specified once.", "id": "KeySet", "properties": {"all": {"description": "For convenience `all` can be set to `true` to indicate that this `KeySet` matches all keys in the table or index. Note that any keys specified in `keys` or `ranges` are only yielded once.", "type": "boolean"}, "keys": {"description": "A list of specific keys. Entries in `keys` should have exactly as many elements as there are columns in the primary or index key with which this `KeySet` is used. Individual key values are encoded as described here.", "items": {"items": {"type": "any"}, "type": "array"}, "type": "array"}, "ranges": {"description": "A list of key ranges. See KeyRange for more information about key range specifications.", "items": {"$ref": "KeyRange"}, "type": "array"}}, "type": "object"}, "ListBackupOperationsResponse": {"description": "The response for ListBackupOperations.", "id": "ListBackupOperationsResponse", "properties": {"nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListBackupOperations call to fetch more of the matching metadata.", "type": "string"}, "operations": {"description": "The list of matching backup long-running operations. Each operation's name will be prefixed by the backup's name. The operation's metadata field type `metadata.type_url` describes the type of the metadata. Operations returned include those that are pending or have completed/failed/canceled within the last 7 days. Operations returned are ordered by `operation.metadata.value.progress.start_time` in descending order starting from the most recently started operation.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListBackupSchedulesResponse": {"description": "The response for ListBackupSchedules.", "id": "ListBackupSchedulesResponse", "properties": {"backupSchedules": {"description": "The list of backup schedules for a database.", "items": {"$ref": "BackupSchedule"}, "type": "array"}, "nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListBackupSchedules call to fetch more of the schedules.", "type": "string"}}, "type": "object"}, "ListBackupsResponse": {"description": "The response for ListBackups.", "id": "ListBackupsResponse", "properties": {"backups": {"description": "The list of matching backups. Backups returned are ordered by `create_time` in descending order, starting from the most recent `create_time`.", "items": {"$ref": "Backup"}, "type": "array"}, "nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListBackups call to fetch more of the matching backups.", "type": "string"}}, "type": "object"}, "ListDatabaseOperationsResponse": {"description": "The response for ListDatabaseOperations.", "id": "ListDatabaseOperationsResponse", "properties": {"nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListDatabaseOperations call to fetch more of the matching metadata.", "type": "string"}, "operations": {"description": "The list of matching database long-running operations. Each operation's name will be prefixed by the database's name. The operation's metadata field type `metadata.type_url` describes the type of the metadata.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListDatabaseRolesResponse": {"description": "The response for ListDatabaseRoles.", "id": "ListDatabaseRolesResponse", "properties": {"databaseRoles": {"description": "Database roles that matched the request.", "items": {"$ref": "DatabaseRole"}, "type": "array"}, "nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListDatabaseRoles call to fetch more of the matching roles.", "type": "string"}}, "type": "object"}, "ListDatabasesResponse": {"description": "The response for ListDatabases.", "id": "ListDatabasesResponse", "properties": {"databases": {"description": "Databases that matched the request.", "items": {"$ref": "Database"}, "type": "array"}, "nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListDatabases call to fetch more of the matching databases.", "type": "string"}}, "type": "object"}, "ListInstanceConfigOperationsResponse": {"description": "The response for ListInstanceConfigOperations.", "id": "ListInstanceConfigOperationsResponse", "properties": {"nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListInstanceConfigOperations call to fetch more of the matching metadata.", "type": "string"}, "operations": {"description": "The list of matching instance configuration long-running operations. Each operation's name will be prefixed by the name of the instance configuration. The operation's metadata field type `metadata.type_url` describes the type of the metadata.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListInstanceConfigsResponse": {"description": "The response for ListInstanceConfigs.", "id": "ListInstanceConfigsResponse", "properties": {"instanceConfigs": {"description": "The list of requested instance configurations.", "items": {"$ref": "InstanceConfig"}, "type": "array"}, "nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListInstanceConfigs call to fetch more of the matching instance configurations.", "type": "string"}}, "type": "object"}, "ListInstancePartitionOperationsResponse": {"description": "The response for ListInstancePartitionOperations.", "id": "ListInstancePartitionOperationsResponse", "properties": {"nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListInstancePartitionOperations call to fetch more of the matching metadata.", "type": "string"}, "operations": {"description": "The list of matching instance partition long-running operations. Each operation's name will be prefixed by the instance partition's name. The operation's metadata field type `metadata.type_url` describes the type of the metadata.", "items": {"$ref": "Operation"}, "type": "array"}, "unreachableInstancePartitions": {"description": "The list of unreachable instance partitions. It includes the names of instance partitions whose operation metadata could not be retrieved within instance_partition_deadline.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListInstancePartitionsResponse": {"description": "The response for ListInstancePartitions.", "id": "ListInstancePartitionsResponse", "properties": {"instancePartitions": {"description": "The list of requested instancePartitions.", "items": {"$ref": "InstancePartition"}, "type": "array"}, "nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListInstancePartitions call to fetch more of the matching instance partitions.", "type": "string"}, "unreachable": {"description": "The list of unreachable instances or instance partitions. It includes the names of instances or instance partitions whose metadata could not be retrieved within instance_partition_deadline.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListInstancesResponse": {"description": "The response for ListInstances.", "id": "ListInstancesResponse", "properties": {"instances": {"description": "The list of requested instances.", "items": {"$ref": "Instance"}, "type": "array"}, "nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListInstances call to fetch more of the matching instances.", "type": "string"}, "unreachable": {"description": "The list of unreachable instances. It includes the names of instances whose metadata could not be retrieved within instance_deadline.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListScansResponse": {"description": "Response method from the ListScans method.", "id": "ListScansResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "scans": {"description": "Available scans based on the list query parameters.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "ListSessionsResponse": {"description": "The response for ListSessions.", "id": "ListSessionsResponse", "properties": {"nextPageToken": {"description": "`next_page_token` can be sent in a subsequent ListSessions call to fetch more of the matching sessions.", "type": "string"}, "sessions": {"description": "The list of requested sessions.", "items": {"$ref": "Session"}, "type": "array"}}, "type": "object"}, "LocalizedString": {"description": "A message representing a user-facing string whose value may need to be translated before being displayed.", "id": "LocalizedString", "properties": {"args": {"additionalProperties": {"type": "string"}, "description": "A map of arguments used when creating the localized message. Keys represent parameter names which may be used by the localized version when substituting dynamic values.", "type": "object"}, "message": {"description": "The canonical English version of this message. If no token is provided or the front-end has no message associated with the token, this text will be displayed as-is.", "type": "string"}, "token": {"description": "The token identifying the message, e.g. 'METRIC_READ_CPU'. This should be unique within the service.", "type": "string"}}, "type": "object"}, "Metric": {"description": "A message representing the actual monitoring data, values for each key bucket over time, of a metric.", "id": "Metric", "properties": {"aggregation": {"description": "The aggregation function used to aggregate each key bucket", "enum": ["AGGREGATION_UNSPECIFIED", "MAX", "SUM"], "enumDescriptions": ["Required default value.", "Use the maximum of all values.", "Use the sum of all values."], "type": "string"}, "category": {"$ref": "LocalizedString", "description": "The category of the metric, e.g. \"Activity\", \"Alerts\", \"Reads\", etc."}, "derived": {"$ref": "DerivedMetric", "description": "The references to numerator and denominator metrics for a derived metric."}, "displayLabel": {"$ref": "LocalizedString", "description": "The displayed label of the metric."}, "hasNonzeroData": {"description": "Whether the metric has any non-zero data.", "type": "boolean"}, "hotValue": {"description": "The value that is considered hot for the metric. On a per metric basis hotness signals high utilization and something that might potentially be a cause for concern by the end user. hot_value is used to calibrate and scale visual color scales.", "format": "float", "type": "number"}, "indexedHotKeys": {"additionalProperties": {"$ref": "IndexedHotKey"}, "description": "The (sparse) mapping from time index to an IndexedHotKey message, representing those time intervals for which there are hot keys.", "type": "object"}, "indexedKeyRangeInfos": {"additionalProperties": {"$ref": "IndexedKeyRangeInfos"}, "description": "The (sparse) mapping from time interval index to an IndexedKeyRangeInfos message, representing those time intervals for which there are informational messages concerning key ranges.", "type": "object"}, "info": {"$ref": "LocalizedString", "description": "Information about the metric."}, "matrix": {"$ref": "MetricMatrix", "description": "The data for the metric as a matrix."}, "unit": {"$ref": "LocalizedString", "description": "The unit of the metric."}, "visible": {"description": "Whether the metric is visible to the end user.", "type": "boolean"}}, "type": "object"}, "MetricMatrix": {"description": "A message representing a matrix of floats.", "id": "MetricMatrix", "properties": {"rows": {"description": "The rows of the matrix.", "items": {"$ref": "MetricMatrixRow"}, "type": "array"}}, "type": "object"}, "MetricMatrixRow": {"description": "A message representing a row of a matrix of floats.", "id": "MetricMatrixRow", "properties": {"cols": {"description": "The columns of the row.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "MoveInstanceRequest": {"description": "The request for MoveInstance.", "id": "MoveInstanceRequest", "properties": {"targetConfig": {"description": "Required. The target instance configuration where to move the instance. Values are of the form `projects//instanceConfigs/`.", "type": "string"}, "targetDatabaseMoveConfigs": {"description": "Optional. The configuration for each database in the target instance configuration.", "items": {"$ref": "DatabaseMoveConfig"}, "type": "array"}}, "type": "object"}, "MultiplexedSessionPrecommitToken": {"description": "When a read-write transaction is executed on a multiplexed session, this precommit token is sent back to the client as a part of the Transaction message in the BeginTransaction response and also as a part of the ResultSet and PartialResultSet responses.", "id": "MultiplexedSessionPrecommitToken", "properties": {"precommitToken": {"description": "Opaque precommit token.", "format": "byte", "type": "string"}, "seqNum": {"description": "An incrementing seq number is generated on every precommit token that is returned. Clients should remember the precommit token with the highest sequence number from the current transaction attempt.", "format": "int32", "type": "integer"}}, "type": "object"}, "Mutation": {"description": "A modification to one or more Cloud Spanner rows. Mutations can be applied to a Cloud Spanner database by sending them in a Commit call.", "id": "Mutation", "properties": {"delete": {"$ref": "Delete", "description": "Delete rows from a table. Succeeds whether or not the named rows were present."}, "insert": {"$ref": "Write", "description": "Insert new rows in a table. If any of the rows already exist, the write or transaction fails with error `ALREADY_EXISTS`."}, "insertOrUpdate": {"$ref": "Write", "description": "Like insert, except that if the row already exists, then its column values are overwritten with the ones provided. Any column values not explicitly written are preserved. When using insert_or_update, just as when using insert, all `NOT NULL` columns in the table must be given a value. This holds true even when the row already exists and will therefore actually be updated."}, "replace": {"$ref": "Write", "description": "Like insert, except that if the row already exists, it is deleted, and the column values provided are inserted instead. Unlike insert_or_update, this means any values not explicitly written become `NULL`. In an interleaved table, if you create the child table with the `ON DELETE CASCADE` annotation, then replacing a parent row also deletes the child rows. Otherwise, you must delete the child rows before you replace the parent row."}, "update": {"$ref": "Write", "description": "Update existing rows in a table. If any of the rows does not already exist, the transaction fails with error `NOT_FOUND`."}}, "type": "object"}, "MutationGroup": {"description": "A group of mutations to be committed together. Related mutations should be placed in a group. For example, two mutations inserting rows with the same primary key prefix in both parent and child tables are related.", "id": "MutationGroup", "properties": {"mutations": {"description": "Required. The mutations in this group.", "items": {"$ref": "Mutation"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationProgress": {"description": "Encapsulates progress related information for a Cloud Spanner long running operation.", "id": "OperationProgress", "properties": {"endTime": {"description": "If set, the time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "progressPercent": {"description": "Percent completion of the operation. Values are between 0 and 100 inclusive.", "format": "int32", "type": "integer"}, "startTime": {"description": "Time the request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "OptimizeRestoredDatabaseMetadata": {"description": "Metadata type for the long-running operation used to track the progress of optimizations performed on a newly restored database. This long-running operation is automatically created by the system after the successful completion of a database restore, and cannot be cancelled.", "id": "OptimizeRestoredDatabaseMetadata", "properties": {"name": {"description": "Name of the restored database being optimized.", "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "The progress of the post-restore optimizations."}}, "type": "object"}, "PartialResultSet": {"description": "Partial results from a streaming read or SQL query. Streaming reads and SQL queries better tolerate large result sets, large rows, and large values, but are a little trickier to consume.", "id": "PartialResultSet", "properties": {"chunkedValue": {"description": "If true, then the final value in values is chunked, and must be combined with more values from subsequent `PartialResultSet`s to obtain a complete field value.", "type": "boolean"}, "last": {"description": "Optional. Indicates whether this is the last `PartialResultSet` in the stream. The server might optionally set this field. Clients shouldn't rely on this field being set in all cases.", "type": "boolean"}, "metadata": {"$ref": "ResultSetMetadata", "description": "Metadata about the result set, such as row type information. Only present in the first response."}, "precommitToken": {"$ref": "MultiplexedSessionPrecommitToken", "description": "Optional. A precommit token is included if the read-write transaction has multiplexed sessions enabled. Pass the precommit token with the highest sequence number from this transaction attempt to the Commit request for this transaction."}, "resumeToken": {"description": "Streaming calls might be interrupted for a variety of reasons, such as TCP connection loss. If this occurs, the stream of results can be resumed by re-sending the original request and including `resume_token`. Note that executing any other transaction in the same session invalidates the token.", "format": "byte", "type": "string"}, "stats": {"$ref": "ResultSetStats", "description": "Query plan and execution statistics for the statement that produced this streaming result set. These can be requested by setting ExecuteSqlRequest.query_mode and are sent only once with the last response in the stream. This field is also present in the last response for DML statements."}, "values": {"description": "A streamed result set consists of a stream of values, which might be split into many `PartialResultSet` messages to accommodate large rows and/or large values. Every N complete values defines a row, where N is equal to the number of entries in metadata.row_type.fields. Most values are encoded based on type as described here. It's possible that the last value in values is \"chunked\", meaning that the rest of the value is sent in subsequent `PartialResultSet`(s). This is denoted by the chunked_value field. Two or more chunked values can be merged to form a complete value as follows: * `bool/number/null`: can't be chunked * `string`: concatenate the strings * `list`: concatenate the lists. If the last element in a list is a `string`, `list`, or `object`, merge it with the first element in the next list by applying these rules recursively. * `object`: concatenate the (field name, field value) pairs. If a field name is duplicated, then apply these rules recursively to merge the field values. Some examples of merging: Strings are concatenated. \"foo\", \"bar\" => \"foobar\" Lists of non-strings are concatenated. [2, 3], [4] => [2, 3, 4] Lists are concatenated, but the last and first elements are merged because they are strings. [\"a\", \"b\"], [\"c\", \"d\"] => [\"a\", \"bc\", \"d\"] Lists are concatenated, but the last and first elements are merged because they are lists. Recursively, the last and first elements of the inner lists are merged because they are strings. [\"a\", [\"b\", \"c\"]], [[\"d\"], \"e\"] => [\"a\", [\"b\", \"cd\"], \"e\"] Non-overlapping object fields are combined. {\"a\": \"1\"}, {\"b\": \"2\"} => {\"a\": \"1\", \"b\": 2\"} Overlapping object fields are merged. {\"a\": \"1\"}, {\"a\": \"2\"} => {\"a\": \"12\"} Examples of merging objects containing lists of strings. {\"a\": [\"1\"]}, {\"a\": [\"2\"]} => {\"a\": [\"12\"]} For a more complete example, suppose a streaming SQL query is yielding a result set whose rows contain a single string field. The following `PartialResultSet`s might be yielded: { \"metadata\": { ... } \"values\": [\"Hello\", \"W\"] \"chunked_value\": true \"resume_token\": \"Af65...\" } { \"values\": [\"orl\"] \"chunked_value\": true } { \"values\": [\"d\"] \"resume_token\": \"Zx1B...\" } This sequence of `PartialResultSet`s encodes two rows, one containing the field value `\"Hello\"`, and a second containing the field value `\"World\" = \"W\" + \"orl\" + \"d\"`. Not all `PartialResultSet`s contain a `resume_token`. Execution can only be resumed from a previously yielded `resume_token`. For the above sequence of `PartialResultSet`s, resuming the query with `\"resume_token\": \"Af65...\"` yields results from the `PartialResultSet` with value \"orl\".", "items": {"type": "any"}, "type": "array"}}, "type": "object"}, "Partition": {"description": "Information returned for each partition returned in a PartitionResponse.", "id": "Partition", "properties": {"partitionToken": {"description": "This token can be passed to `Read`, `StreamingRead`, `ExecuteSql`, or `ExecuteStreamingSql` requests to restrict the results to those identified by this partition token.", "format": "byte", "type": "string"}}, "type": "object"}, "PartitionOptions": {"description": "Options for a `PartitionQueryRequest` and `PartitionReadRequest`.", "id": "PartitionOptions", "properties": {"maxPartitions": {"description": "**Note:** This hint is currently ignored by `PartitionQuery` and `PartitionRead` requests. The desired maximum number of partitions to return. For example, this might be set to the number of workers available. The default for this option is currently 10,000. The maximum value is currently 200,000. This is only a hint. The actual number of partitions returned can be smaller or larger than this maximum count request.", "format": "int64", "type": "string"}, "partitionSizeBytes": {"description": "**Note:** This hint is currently ignored by `PartitionQuery` and `PartitionRead` requests. The desired data size for each partition generated. The default for this option is currently 1 GiB. This is only a hint. The actual size of each partition can be smaller or larger than this size request.", "format": "int64", "type": "string"}}, "type": "object"}, "PartitionQueryRequest": {"description": "The request for PartitionQuery", "id": "PartitionQueryRequest", "properties": {"paramTypes": {"additionalProperties": {"$ref": "Type"}, "description": "It isn't always possible for Cloud Spanner to infer the right SQL type from a JSON value. For example, values of type `BYTES` and values of type `STRING` both appear in params as JSON strings. In these cases, `param_types` can be used to specify the exact SQL type for some or all of the SQL query parameters. See the definition of Type for more information about SQL types.", "type": "object"}, "params": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Parameter names and values that bind to placeholders in the SQL string. A parameter placeholder consists of the `@` character followed by the parameter name (for example, `@firstName`). Parameter names can contain letters, numbers, and underscores. Parameters can appear anywhere that a literal value is expected. The same parameter name can be used more than once, for example: `\"WHERE id > @msg_id AND id < @msg_id + 100\"` It's an error to execute a SQL statement with unbound parameters.", "type": "object"}, "partitionOptions": {"$ref": "PartitionOptions", "description": "Additional options that affect how many partitions are created."}, "sql": {"description": "Required. The query request to generate partitions for. The request fails if the query isn't root partitionable. For a query to be root partitionable, it needs to satisfy a few conditions. For example, if the query execution plan contains a distributed union operator, then it must be the first operator in the plan. For more information about other conditions, see [Read data in parallel](https://cloud.google.com/spanner/docs/reads#read_data_in_parallel). The query request must not contain DML commands, such as `INSERT`, `UPDATE`, or `DELETE`. Use `ExecuteStreamingSql` with a `PartitionedDml` transaction for large, partition-friendly DML operations.", "type": "string"}, "transaction": {"$ref": "TransactionSelector", "description": "Read-only snapshot transactions are supported, read and write and single-use transactions are not."}}, "type": "object"}, "PartitionReadRequest": {"description": "The request for PartitionRead", "id": "PartitionReadRequest", "properties": {"columns": {"description": "The columns of table to be returned for each row matching this request.", "items": {"type": "string"}, "type": "array"}, "index": {"description": "If non-empty, the name of an index on table. This index is used instead of the table primary key when interpreting key_set and sorting result rows. See key_set for further information.", "type": "string"}, "keySet": {"$ref": "KeySet", "description": "Required. `key_set` identifies the rows to be yielded. `key_set` names the primary keys of the rows in table to be yielded, unless index is present. If index is present, then key_set instead names index keys in index. It isn't an error for the `key_set` to name rows that don't exist in the database. Read yields nothing for nonexistent rows."}, "partitionOptions": {"$ref": "PartitionOptions", "description": "Additional options that affect how many partitions are created."}, "table": {"description": "Required. The name of the table in the database to be read.", "type": "string"}, "transaction": {"$ref": "TransactionSelector", "description": "Read only snapshot transactions are supported, read/write and single use transactions are not."}}, "type": "object"}, "PartitionResponse": {"description": "The response for PartitionQuery or PartitionRead", "id": "PartitionResponse", "properties": {"partitions": {"description": "Partitions created by this request.", "items": {"$ref": "Partition"}, "type": "array"}, "transaction": {"$ref": "Transaction", "description": "Transaction created by this request."}}, "type": "object"}, "PartitionedDml": {"description": "Message type to initiate a Partitioned DML transaction.", "id": "PartitionedDml", "properties": {}, "type": "object"}, "PlanNode": {"description": "Node information for nodes appearing in a QueryPlan.plan_nodes.", "id": "PlanNode", "properties": {"childLinks": {"description": "List of child node `index`es and their relationship to this parent.", "items": {"$ref": "ChildLink"}, "type": "array"}, "displayName": {"description": "The display name for the node.", "type": "string"}, "executionStats": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The execution statistics associated with the node, contained in a group of key-value pairs. Only present if the plan was returned as a result of a profile query. For example, number of executions, number of rows/time per execution etc.", "type": "object"}, "index": {"description": "The `PlanNode`'s index in node list.", "format": "int32", "type": "integer"}, "kind": {"description": "Used to determine the type of node. May be needed for visualizing different kinds of nodes differently. For example, If the node is a SCALAR node, it will have a condensed representation which can be used to directly embed a description of the node in its parent.", "enum": ["KIND_UNSPECIFIED", "RELATIONAL", "SCALAR"], "enumDescriptions": ["Not specified.", "Denotes a Relational operator node in the expression tree. Relational operators represent iterative processing of rows during query execution. For example, a `TableScan` operation that reads rows from a table.", "Denotes a Scalar node in the expression tree. Scalar nodes represent non-iterable entities in the query plan. For example, constants or arithmetic operators appearing inside predicate expressions or references to column names."], "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Attributes relevant to the node contained in a group of key-value pairs. For example, a Parameter Reference node could have the following information in its metadata: { \"parameter_reference\": \"param1\", \"parameter_type\": \"array\" }", "type": "object"}, "shortRepresentation": {"$ref": "ShortRepresentation", "description": "Condensed representation for SCALAR nodes."}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PrefixNode": {"description": "A message representing a key prefix node in the key prefix hierarchy. for eg. Bigtable keyspaces are lexicographically ordered mappings of keys to values. Keys often have a shared prefix structure where users use the keys to organize data. Eg ///employee In this case Keysight will possibly use one node for a company and reuse it for all employees that fall under the company. Doing so improves legibility in the UI.", "id": "PrefixNode", "properties": {"dataSourceNode": {"description": "Whether this corresponds to a data_source name.", "type": "boolean"}, "depth": {"description": "The depth in the prefix hierarchy.", "format": "int32", "type": "integer"}, "endIndex": {"description": "The index of the end key bucket of the range that this node spans.", "format": "int32", "type": "integer"}, "startIndex": {"description": "The index of the start key bucket of the range that this node spans.", "format": "int32", "type": "integer"}, "word": {"description": "The string represented by the prefix node.", "type": "string"}}, "type": "object"}, "QueryAdvisorResult": {"description": "Output of query advisor analysis.", "id": "QueryAdvisorResult", "properties": {"indexAdvice": {"description": "Optional. Index Recommendation for a query. This is an optional field and the recommendation will only be available when the recommendation guarantees significant improvement in query performance.", "items": {"$ref": "IndexAdvice"}, "type": "array"}}, "type": "object"}, "QueryOptions": {"description": "Query optimizer configuration.", "id": "QueryOptions", "properties": {"optimizerStatisticsPackage": {"description": "An option to control the selection of optimizer statistics package. This parameter allows individual queries to use a different query optimizer statistics package. Specifying `latest` as a value instructs Cloud Spanner to use the latest generated statistics package. If not specified, Cloud Spanner uses the statistics package set at the database level options, or the latest package if the database option isn't set. The statistics package requested by the query has to be exempt from garbage collection. This can be achieved with the following DDL statement: ```sql ALTER STATISTICS SET OPTIONS (allow_gc=false) ``` The list of available statistics packages can be queried from `INFORMATION_SCHEMA.SPANNER_STATISTICS`. Executing a SQL statement with an invalid optimizer statistics package or with a statistics package that allows garbage collection fails with an `INVALID_ARGUMENT` error.", "type": "string"}, "optimizerVersion": {"description": "An option to control the selection of optimizer version. This parameter allows individual queries to pick different query optimizer versions. Specifying `latest` as a value instructs Cloud Spanner to use the latest supported query optimizer version. If not specified, Cloud Spanner uses the optimizer version set at the database level options. Any other positive integer (from the list of supported optimizer versions) overrides the default optimizer version for query execution. The list of supported optimizer versions can be queried from `SPANNER_SYS.SUPPORTED_OPTIMIZER_VERSIONS`. Executing a SQL statement with an invalid optimizer version fails with an `INVALID_ARGUMENT` error. See https://cloud.google.com/spanner/docs/query-optimizer/manage-query-optimizer for more information on managing the query optimizer. The `optimizer_version` statement hint has precedence over this setting.", "type": "string"}}, "type": "object"}, "QueryPlan": {"description": "Contains an ordered list of nodes appearing in the query plan.", "id": "QueryPlan", "properties": {"planNodes": {"description": "The nodes in the query plan. Plan nodes are returned in pre-order starting with the plan root. Each PlanNode's `id` corresponds to its index in `plan_nodes`.", "items": {"$ref": "PlanNode"}, "type": "array"}, "queryAdvice": {"$ref": "QueryAdvisorResult", "description": "Optional. The advise/recommendations for a query. Currently this field will be serving index recommendations for a query."}}, "type": "object"}, "QuorumInfo": {"description": "Information about the dual-region quorum.", "id": "QuorumInfo", "properties": {"etag": {"description": "Output only. The etag is used for optimistic concurrency control as a way to help prevent simultaneous `ChangeQuorum` requests that might create a race condition.", "readOnly": true, "type": "string"}, "initiator": {"description": "Output only. Whether this `ChangeQuorum` is Google or User initiated.", "enum": ["INITIATOR_UNSPECIFIED", "GOOGLE", "USER"], "enumDescriptions": ["Unspecified.", "`ChangeQuorum` initiated by Google.", "`ChangeQuorum` initiated by <PERSON><PERSON>."], "readOnly": true, "type": "string"}, "quorumType": {"$ref": "QuorumType", "description": "Output only. The type of this quorum. See QuorumType for more information about quorum type specifications.", "readOnly": true}, "startTime": {"description": "Output only. The timestamp when the request was triggered.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "QuorumType": {"description": "Information about the database quorum type. This only applies to dual-region instance configs.", "id": "QuorumType", "properties": {"dualRegion": {"$ref": "DualRegionQuorum", "description": "Dual-region quorum type."}, "singleRegion": {"$ref": "SingleRegionQuorum", "description": "Single-region quorum type."}}, "type": "object"}, "ReadOnly": {"description": "Message type to initiate a read-only transaction.", "id": "Read<PERSON>nly", "properties": {"exactStaleness": {"description": "Executes all reads at a timestamp that is `exact_staleness` old. The timestamp is chosen soon after the read is started. Guarantees that all writes that have committed more than the specified number of seconds ago are visible. Because Cloud Spanner chooses the exact timestamp, this mode works even if the client's local clock is substantially skewed from Cloud Spanner commit timestamps. Useful for reading at nearby replicas without the distributed timestamp negotiation overhead of `max_staleness`.", "format": "google-duration", "type": "string"}, "maxStaleness": {"description": "Read data at a timestamp >= `NOW - max_staleness` seconds. Guarantees that all writes that have committed more than the specified number of seconds ago are visible. Because Cloud Spanner chooses the exact timestamp, this mode works even if the client's local clock is substantially skewed from Cloud Spanner commit timestamps. Useful for reading the freshest data available at a nearby replica, while bounding the possible staleness if the local replica has fallen behind. Note that this option can only be used in single-use transactions.", "format": "google-duration", "type": "string"}, "minReadTimestamp": {"description": "Executes all reads at a timestamp >= `min_read_timestamp`. This is useful for requesting fresher data than some previous read, or data that is fresh enough to observe the effects of some previously committed transaction whose timestamp is known. Note that this option can only be used in single-use transactions. A timestamp in RFC3339 UTC \\\"Zulu\\\" format, accurate to nanoseconds. Example: `\"2014-10-02T15:01:23.045123456Z\"`.", "format": "google-datetime", "type": "string"}, "readTimestamp": {"description": "Executes all reads at the given timestamp. Unlike other modes, reads at a specific timestamp are repeatable; the same read at the same timestamp always returns the same data. If the timestamp is in the future, the read is blocked until the specified timestamp, modulo the read's deadline. Useful for large scale consistent reads such as mapreduces, or for coordinating many reads against a consistent snapshot of the data. A timestamp in RFC3339 UTC \\\"Zulu\\\" format, accurate to nanoseconds. Example: `\"2014-10-02T15:01:23.045123456Z\"`.", "format": "google-datetime", "type": "string"}, "returnReadTimestamp": {"description": "If true, the Cloud Spanner-selected read timestamp is included in the Transaction message that describes the transaction.", "type": "boolean"}, "strong": {"description": "Read at a timestamp where all previously committed transactions are visible.", "type": "boolean"}}, "type": "object"}, "ReadRequest": {"description": "The request for Read and StreamingRead.", "id": "ReadRequest", "properties": {"columns": {"description": "Required. The columns of table to be returned for each row matching this request.", "items": {"type": "string"}, "type": "array"}, "dataBoostEnabled": {"description": "If this is for a partitioned read and this field is set to `true`, the request is executed with Spanner Data Boost independent compute resources. If the field is set to `true` but the request doesn't set `partition_token`, the API returns an `INVALID_ARGUMENT` error.", "type": "boolean"}, "directedReadOptions": {"$ref": "DirectedReadOptions", "description": "Directed read options for this request."}, "index": {"description": "If non-empty, the name of an index on table. This index is used instead of the table primary key when interpreting key_set and sorting result rows. See key_set for further information.", "type": "string"}, "keySet": {"$ref": "KeySet", "description": "Required. `key_set` identifies the rows to be yielded. `key_set` names the primary keys of the rows in table to be yielded, unless index is present. If index is present, then key_set instead names index keys in index. If the partition_token field is empty, rows are yielded in table primary key order (if index is empty) or index key order (if index is non-empty). If the partition_token field isn't empty, rows are yielded in an unspecified order. It isn't an error for the `key_set` to name rows that don't exist in the database. Read yields nothing for nonexistent rows."}, "limit": {"description": "If greater than zero, only the first `limit` rows are yielded. If `limit` is zero, the default is no limit. A limit can't be specified if `partition_token` is set.", "format": "int64", "type": "string"}, "lockHint": {"description": "Optional. Lock Hint for the request, it can only be used with read-write transactions.", "enum": ["LOCK_HINT_UNSPECIFIED", "LOCK_HINT_SHARED", "LOCK_HINT_EXCLUSIVE"], "enumDescriptions": ["Default value. `LOCK_HINT_UNSPECIFIED` is equivalent to `LOCK_HINT_SHARED`.", "Acquire shared locks. By default when you perform a read as part of a read-write transaction, Spanner acquires shared read locks, which allows other reads to still access the data until your transaction is ready to commit. When your transaction is committing and writes are being applied, the transaction attempts to upgrade to an exclusive lock for any data you are writing. For more information about locks, see [Lock modes](https://cloud.google.com/spanner/docs/introspection/lock-statistics#explain-lock-modes).", "Acquire exclusive locks. Requesting exclusive locks is beneficial if you observe high write contention, which means you notice that multiple transactions are concurrently trying to read and write to the same data, resulting in a large number of aborts. This problem occurs when two transactions initially acquire shared locks and then both try to upgrade to exclusive locks at the same time. In this situation both transactions are waiting for the other to give up their lock, resulting in a deadlocked situation. Spanner is able to detect this occurring and force one of the transactions to abort. However, this is a slow and expensive operation and results in lower performance. In this case it makes sense to acquire exclusive locks at the start of the transaction because then when multiple transactions try to act on the same data, they automatically get serialized. Each transaction waits its turn to acquire the lock and avoids getting into deadlock situations. Because the exclusive lock hint is just a hint, it shouldn't be considered equivalent to a mutex. In other words, you shouldn't use Spanner exclusive locks as a mutual exclusion mechanism for the execution of code outside of Spanner. **Note:** Request exclusive locks judiciously because they block others from reading that data for the entire transaction, rather than just when the writes are being performed. Unless you observe high write contention, you should use the default of shared read locks so you don't prematurely block other clients from reading the data that you're writing to."], "type": "string"}, "orderBy": {"description": "Optional. Order for the returned rows. By default, Spanner returns result rows in primary key order except for PartitionRead requests. For applications that don't require rows to be returned in primary key (`ORDER_BY_PRIMARY_KEY`) order, setting `ORDER_BY_NO_ORDER` option allows Spanner to optimize row retrieval, resulting in lower latencies in certain cases (for example, bulk point lookups).", "enum": ["ORDER_BY_UNSPECIFIED", "ORDER_BY_PRIMARY_KEY", "ORDER_BY_NO_ORDER"], "enumDescriptions": ["Default value. `ORDER_BY_UNSPECIFIED` is equivalent to `ORDER_BY_PRIMARY_KEY`.", "Read rows are returned in primary key order. In the event that this option is used in conjunction with the `partition_token` field, the API returns an `INVALID_ARGUMENT` error.", "Read rows are returned in any order."], "type": "string"}, "partitionToken": {"description": "If present, results are restricted to the specified partition previously created using `PartitionRead`. There must be an exact match for the values of fields common to this message and the PartitionReadRequest message used to create this partition_token.", "format": "byte", "type": "string"}, "requestOptions": {"$ref": "RequestOptions", "description": "Common options for this request."}, "resumeToken": {"description": "If this request is resuming a previously interrupted read, `resume_token` should be copied from the last PartialResultSet yielded before the interruption. Doing this enables the new read to resume where the last read left off. The rest of the request parameters must exactly match the request that yielded this token.", "format": "byte", "type": "string"}, "table": {"description": "Required. The name of the table in the database to be read.", "type": "string"}, "transaction": {"$ref": "TransactionSelector", "description": "The transaction to use. If none is provided, the default is a temporary read-only transaction with strong concurrency."}}, "type": "object"}, "ReadWrite": {"description": "Message type to initiate a read-write transaction. Currently this transaction type has no options.", "id": "ReadWrite", "properties": {"multiplexedSessionPreviousTransactionId": {"description": "Optional. Clients should pass the transaction ID of the previous transaction attempt that was aborted if this transaction is being executed on a multiplexed session.", "format": "byte", "type": "string"}, "readLockMode": {"description": "Read lock mode for the transaction.", "enum": ["READ_LOCK_MODE_UNSPECIFIED", "PESSIMISTIC", "OPTIMISTIC"], "enumDescriptions": ["Default value. * If isolation level is REPEATABLE_READ, then it is an error to specify `read_lock_mode`. Locking semantics default to `OPTIMISTIC`. No validation checks are done for reads, except to validate that the data that was served at the snapshot time is unchanged at commit time in the following cases: 1. reads done as part of queries that use `SELECT FOR UPDATE` 2. reads done as part of statements with a `LOCK_SCANNED_RANGES` hint 3. reads done as part of DML statements * At all other isolation levels, if `read_lock_mode` is the default value, then pessimistic read locks are used.", "Pessimistic lock mode. Read locks are acquired immediately on read. Semantics described only applies to SERIALIZABLE isolation.", "Optimistic lock mode. Locks for reads within the transaction are not acquired on read. Instead the locks are acquired on a commit to validate that read/queried data has not changed since the transaction started. Semantics described only applies to SERIALIZABLE isolation."], "type": "string"}}, "type": "object"}, "ReplicaComputeCapacity": {"description": "ReplicaComputeCapacity describes the amount of server resources that are allocated to each replica identified by the replica selection.", "id": "ReplicaComputeCapacity", "properties": {"nodeCount": {"description": "The number of nodes allocated to each replica. This may be zero in API responses for instances that are not yet in state `READY`.", "format": "int32", "type": "integer"}, "processingUnits": {"description": "The number of processing units allocated to each replica. This may be zero in API responses for instances that are not yet in state `READY`.", "format": "int32", "type": "integer"}, "replicaSelection": {"$ref": "InstanceReplicaSelection", "description": "Required. Identifies replicas by specified properties. All replicas in the selection have the same amount of compute capacity."}}, "type": "object"}, "ReplicaInfo": {"id": "ReplicaInfo", "properties": {"defaultLeaderLocation": {"description": "If true, this location is designated as the default leader location where leader replicas are placed. See the [region types documentation](https://cloud.google.com/spanner/docs/instances#region_types) for more details.", "type": "boolean"}, "location": {"description": "The location of the serving resources, e.g., \"us-central1\".", "type": "string"}, "type": {"description": "The type of replica.", "enum": ["TYPE_UNSPECIFIED", "READ_WRITE", "READ_ONLY", "WITNESS"], "enumDescriptions": ["Not specified.", "Read-write replicas support both reads and writes. These replicas: * Maintain a full copy of your data. * Serve reads. * Can vote whether to commit a write. * Participate in leadership election. * Are eligible to become a leader.", "Read-only replicas only support reads (not writes). Read-only replicas: * Maintain a full copy of your data. * Serve reads. * Do not participate in voting to commit writes. * Are not eligible to become a leader.", "Witness replicas don't support reads but do participate in voting to commit writes. Witness replicas: * Do not maintain a full copy of data. * Do not serve reads. * Vote whether to commit writes. * Participate in leader election but are not eligible to become leader."], "type": "string"}}, "type": "object"}, "ReplicaSelection": {"description": "The directed read replica selector. Callers must provide one or more of the following fields for replica selection: * `location` - The location must be one of the regions within the multi-region configuration of your database. * `type` - The type of the replica. Some examples of using replica_selectors are: * `location:us-east1` --> The \"us-east1\" replica(s) of any available type is used to process the request. * `type:READ_ONLY` --> The \"READ_ONLY\" type replica(s) in the nearest available location are used to process the request. * `location:us-east1 type:READ_ONLY` --> The \"READ_ONLY\" type replica(s) in location \"us-east1\" is used to process the request.", "id": "ReplicaSelection", "properties": {"location": {"description": "The location or region of the serving requests, for example, \"us-east1\".", "type": "string"}, "type": {"description": "The type of replica.", "enum": ["TYPE_UNSPECIFIED", "READ_WRITE", "READ_ONLY"], "enumDescriptions": ["Not specified.", "Read-write replicas support both reads and writes.", "Read-only replicas only support reads (not writes)."], "type": "string"}}, "type": "object"}, "RequestOptions": {"description": "Common request options for various APIs.", "id": "RequestOptions", "properties": {"priority": {"description": "Priority for the request.", "enum": ["PRIORITY_UNSPECIFIED", "PRIORITY_LOW", "PRIORITY_MEDIUM", "PRIORITY_HIGH"], "enumDescriptions": ["`PRIORITY_UNSPECIFIED` is equivalent to `PRIORITY_HIGH`.", "This specifies that the request is low priority.", "This specifies that the request is medium priority.", "This specifies that the request is high priority."], "type": "string"}, "requestTag": {"description": "A per-request tag which can be applied to queries or reads, used for statistics collection. Both `request_tag` and `transaction_tag` can be specified for a read or query that belongs to a transaction. This field is ignored for requests where it's not applicable (for example, `CommitRequest`). Legal characters for `request_tag` values are all printable characters (ASCII 32 - 126) and the length of a request_tag is limited to 50 characters. Values that exceed this limit are truncated. Any leading underscore (_) characters are removed from the string.", "type": "string"}, "transactionTag": {"description": "A tag used for statistics collection about this transaction. Both `request_tag` and `transaction_tag` can be specified for a read or query that belongs to a transaction. The value of transaction_tag should be the same for all requests belonging to the same transaction. If this request doesn't belong to any transaction, `transaction_tag` is ignored. Legal characters for `transaction_tag` values are all printable characters (ASCII 32 - 126) and the length of a `transaction_tag` is limited to 50 characters. Values that exceed this limit are truncated. Any leading underscore (_) characters are removed from the string.", "type": "string"}}, "type": "object"}, "RestoreDatabaseEncryptionConfig": {"description": "Encryption configuration for the restored database.", "id": "RestoreDatabaseEncryptionConfig", "properties": {"encryptionType": {"description": "Required. The encryption type of the restored database.", "enum": ["ENCRYPTION_TYPE_UNSPECIFIED", "USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION", "GOOGLE_DEFAULT_ENCRYPTION", "CUSTOMER_MANAGED_ENCRYPTION"], "enumDescriptions": ["Unspecified. Do not use.", "This is the default option when encryption_config is not specified.", "Use Google default encryption.", "Use customer managed encryption. If specified, `kms_key_name` must must contain a valid Cloud KMS key."], "type": "string"}, "kmsKeyName": {"description": "Optional. The Cloud KMS key that will be used to encrypt/decrypt the restored database. This field should be set only when encryption_type is `CUSTOMER_MANAGED_ENCRYPTION`. Values are of the form `projects//locations//keyRings//cryptoKeys/`.", "type": "string"}, "kmsKeyNames": {"description": "Optional. Specifies the KMS configuration for one or more keys used to encrypt the database. Values have the form `projects//locations//keyRings//cryptoKeys/`. The keys referenced by `kms_key_names` must fully cover all regions of the database's instance configuration. Some examples: * For regional (single-region) instance configurations, specify a regional location KMS key. * For multi-region instance configurations of type `GOOGLE_MANAGED`, either specify a multi-region location KMS key or multiple regional location KMS keys that cover all regions in the instance configuration. * For an instance configuration of type `USER_MANAGED`, specify only regional location KMS keys to cover each region in the instance configuration. Multi-region location KMS keys aren't supported for `USER_MANAGED` type instance configurations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "RestoreDatabaseMetadata": {"description": "Metadata type for the long-running operation returned by RestoreDatabase.", "id": "RestoreDatabaseMetadata", "properties": {"backupInfo": {"$ref": "BackupInfo", "description": "Information about the backup used to restore the database."}, "cancelTime": {"description": "The time at which cancellation of this operation was received. Operations.CancelOperation starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "format": "google-datetime", "type": "string"}, "name": {"description": "Name of the database being created and restored to.", "type": "string"}, "optimizeDatabaseOperationName": {"description": "If exists, the name of the long-running operation that will be used to track the post-restore optimization process to optimize the performance of the restored database, and remove the dependency on the restore source. The name is of the form `projects//instances//databases//operations/` where the is the name of database being created and restored to. The metadata type of the long-running operation is OptimizeRestoredDatabaseMetadata. This long-running operation will be automatically created by the system after the RestoreDatabase long-running operation completes successfully. This operation will not be created if the restore was not successful.", "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "The progress of the RestoreDatabase operation."}, "sourceType": {"description": "The type of the restore source.", "enum": ["TYPE_UNSPECIFIED", "BACKUP"], "enumDescriptions": ["No restore associated.", "A backup was used as the source of the restore."], "type": "string"}}, "type": "object"}, "RestoreDatabaseRequest": {"description": "The request for RestoreDatabase.", "id": "RestoreDatabaseRequest", "properties": {"backup": {"description": "Name of the backup from which to restore. Values are of the form `projects//instances//backups/`.", "type": "string"}, "databaseId": {"description": "Required. The id of the database to create and restore to. This database must not already exist. The `database_id` appended to `parent` forms the full database name of the form `projects//instances//databases/`.", "type": "string"}, "encryptionConfig": {"$ref": "RestoreDatabaseEncryptionConfig", "description": "Optional. An encryption configuration describing the encryption type and key resources in Cloud KMS used to encrypt/decrypt the database to restore to. If this field is not specified, the restored database will use the same encryption configuration as the backup by default, namely encryption_type = `USE_CONFIG_DEFAULT_OR_BACKUP_ENCRYPTION`."}}, "type": "object"}, "RestoreInfo": {"description": "Information about the database restore.", "id": "RestoreInfo", "properties": {"backupInfo": {"$ref": "BackupInfo", "description": "Information about the backup used to restore the database. The backup may no longer exist."}, "sourceType": {"description": "The type of the restore source.", "enum": ["TYPE_UNSPECIFIED", "BACKUP"], "enumDescriptions": ["No restore associated.", "A backup was used as the source of the restore."], "type": "string"}}, "type": "object"}, "ResultSet": {"description": "Results from Read or ExecuteSql.", "id": "ResultSet", "properties": {"metadata": {"$ref": "ResultSetMetadata", "description": "Metadata about the result set, such as row type information."}, "precommitToken": {"$ref": "MultiplexedSessionPrecommitToken", "description": "Optional. A precommit token is included if the read-write transaction is on a multiplexed session. Pass the precommit token with the highest sequence number from this transaction attempt to the Commit request for this transaction."}, "rows": {"description": "Each element in `rows` is a row whose format is defined by metadata.row_type. The ith element in each row matches the ith field in metadata.row_type. Elements are encoded based on type as described here.", "items": {"items": {"type": "any"}, "type": "array"}, "type": "array"}, "stats": {"$ref": "ResultSetStats", "description": "Query plan and execution statistics for the SQL statement that produced this result set. These can be requested by setting ExecuteSqlRequest.query_mode. DML statements always produce stats containing the number of rows modified, unless executed using the ExecuteSqlRequest.QueryMode.PLAN ExecuteSqlRequest.query_mode. Other fields might or might not be populated, based on the ExecuteSqlRequest.query_mode."}}, "type": "object"}, "ResultSetMetadata": {"description": "Metadata about a ResultSet or PartialResultSet.", "id": "ResultSetMetadata", "properties": {"rowType": {"$ref": "StructType", "description": "Indicates the field names and types for the rows in the result set. For example, a SQL query like `\"SELECT UserId, UserName FROM Users\"` could return a `row_type` value like: \"fields\": [ { \"name\": \"UserId\", \"type\": { \"code\": \"INT64\" } }, { \"name\": \"UserName\", \"type\": { \"code\": \"STRING\" } }, ]"}, "transaction": {"$ref": "Transaction", "description": "If the read or SQL query began a transaction as a side-effect, the information about the new transaction is yielded here."}, "undeclaredParameters": {"$ref": "StructType", "description": "A SQL query can be parameterized. In PLAN mode, these parameters can be undeclared. This indicates the field names and types for those undeclared parameters in the SQL query. For example, a SQL query like `\"SELECT * FROM Users where UserId = @userId and UserName = @userName \"` could return a `undeclared_parameters` value like: \"fields\": [ { \"name\": \"UserId\", \"type\": { \"code\": \"INT64\" } }, { \"name\": \"UserName\", \"type\": { \"code\": \"STRING\" } }, ]"}}, "type": "object"}, "ResultSetStats": {"description": "Additional statistics about a ResultSet or PartialResultSet.", "id": "ResultSetStats", "properties": {"queryPlan": {"$ref": "QueryPlan", "description": "QueryPlan for the query associated with this result."}, "queryStats": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Aggregated statistics from the execution of the query. Only present when the query is profiled. For example, a query could return the statistics as follows: { \"rows_returned\": \"3\", \"elapsed_time\": \"1.22 secs\", \"cpu_time\": \"1.19 secs\" }", "type": "object"}, "rowCountExact": {"description": "Standard DML returns an exact count of rows that were modified.", "format": "int64", "type": "string"}, "rowCountLowerBound": {"description": "Partitioned DML doesn't offer exactly-once semantics, so it returns a lower bound of the rows modified.", "format": "int64", "type": "string"}}, "type": "object"}, "RollbackRequest": {"description": "The request for <PERSON><PERSON>.", "id": "RollbackRequest", "properties": {"transactionId": {"description": "Required. The transaction to roll back.", "format": "byte", "type": "string"}}, "type": "object"}, "Scan": {"description": "Scan is a structure which describes Cloud Key Visualizer scan information.", "id": "<PERSON><PERSON>", "properties": {"details": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Additional information provided by the implementer.", "type": "object"}, "endTime": {"description": "The upper bound for when the scan is defined.", "format": "google-datetime", "type": "string"}, "name": {"description": "The unique name of the scan, specific to the Database service implementing this interface.", "type": "string"}, "scanData": {"$ref": "ScanData", "description": "Output only. Cloud Key Visualizer scan data. Note, this field is not available to the ListScans method.", "readOnly": true}, "startTime": {"description": "A range of time (inclusive) for when the scan is defined. The lower bound for when the scan is defined.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ScanData": {"description": "ScanData contains Cloud Key Visualizer scan data used by the caller to construct a visualization.", "id": "ScanData", "properties": {"data": {"$ref": "VisualizationData", "description": "Cloud Key Visualizer scan data. The range of time this information covers is captured via the above time range fields. Note, this field is not available to the ListScans method."}, "endTime": {"description": "The upper bound for when the contained data is defined.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "A range of time (inclusive) for when the contained data is defined. The lower bound for when the contained data is defined.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Session": {"description": "A session in the Cloud Spanner API.", "id": "Session", "properties": {"approximateLastUseTime": {"description": "Output only. The approximate timestamp when the session is last used. It's typically earlier than the actual last use time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The timestamp when the session is created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creatorRole": {"description": "The database role which created this session.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels for the session. * Label keys must be between 1 and 63 characters long and must conform to the following regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. * Label values must be between 0 and 63 characters long and must conform to the regular expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`. * No more than 64 labels can be associated with a given session. See https://goo.gl/xmQnxf for more information on and examples of labels.", "type": "object"}, "multiplexed": {"description": "Optional. If `true`, specifies a multiplexed session. Use a multiplexed session for multiple, concurrent read-only operations. Don't use them for read-write transactions, partitioned reads, or partitioned queries. Use `sessions.create` to create multiplexed sessions. Don't use BatchCreateSessions to create a multiplexed session. You can't delete or list multiplexed sessions.", "type": "boolean"}, "name": {"description": "Output only. The name of the session. This is always system-assigned.", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}}, "type": "object"}, "ShortRepresentation": {"description": "Condensed representation of a node and its subtree. Only present for `SCALAR` PlanNode(s).", "id": "ShortRepresentation", "properties": {"description": {"description": "A string representation of the expression subtree rooted at this node.", "type": "string"}, "subqueries": {"additionalProperties": {"format": "int32", "type": "integer"}, "description": "A mapping of (subquery variable name) -> (subquery node id) for cases where the `description` string of this node references a `SCALAR` subquery contained in the expression subtree rooted at this node. The referenced `SCALAR` subquery may not necessarily be a direct child of this node.", "type": "object"}}, "type": "object"}, "SingleRegionQuorum": {"description": "Message type for a single-region quorum.", "id": "SingleRegionQuorum", "properties": {"servingLocation": {"description": "Required. The location of the serving region, e.g. \"us-central1\". The location must be one of the regions within the dual-region instance configuration of your database. The list of valid locations is available using the GetInstanceConfig API. This should only be used if you plan to change quorum to the single-region quorum type.", "type": "string"}}, "type": "object"}, "SplitPoints": {"description": "The split points of a table or an index.", "id": "SplitPoints", "properties": {"expireTime": {"description": "Optional. The expiration timestamp of the split points. A timestamp in the past means immediate expiration. The maximum value can be 30 days in the future. Defaults to 10 days in the future if not specified.", "format": "google-datetime", "type": "string"}, "index": {"description": "The index to split. If specified, the `table` field must refer to the index's base table.", "type": "string"}, "keys": {"description": "Required. The list of split keys. In essence, the split boundaries.", "items": {"$ref": "Key"}, "type": "array"}, "table": {"description": "The table to split.", "type": "string"}}, "type": "object"}, "Statement": {"description": "A single DML statement.", "id": "Statement", "properties": {"paramTypes": {"additionalProperties": {"$ref": "Type"}, "description": "It isn't always possible for Cloud Spanner to infer the right SQL type from a JSON value. For example, values of type `BYTES` and values of type `STRING` both appear in params as JSON strings. In these cases, `param_types` can be used to specify the exact SQL type for some or all of the SQL statement parameters. See the definition of Type for more information about SQL types.", "type": "object"}, "params": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Parameter names and values that bind to placeholders in the DML string. A parameter placeholder consists of the `@` character followed by the parameter name (for example, `@firstName`). Parameter names can contain letters, numbers, and underscores. Parameters can appear anywhere that a literal value is expected. The same parameter name can be used more than once, for example: `\"WHERE id > @msg_id AND id < @msg_id + 100\"` It's an error to execute a SQL statement with unbound parameters.", "type": "object"}, "sql": {"description": "Required. The DML string.", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StructType": {"description": "`StructType` defines the fields of a STRUCT type.", "id": "StructType", "properties": {"fields": {"description": "The list of fields that make up this struct. Order is significant, because values of this struct type are represented as lists, where the order of field values matches the order of fields in the StructType. In turn, the order of fields matches the order of columns in a read request, or the order of fields in the `SELECT` clause of a query.", "items": {"$ref": "Field"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "REQUIRED: The set of permissions to check for 'resource'. Permissions with wildcards (such as '*', 'spanner.*', 'spanner.instances.*') are not allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Transaction": {"description": "A transaction.", "id": "Transaction", "properties": {"id": {"description": "`id` may be used to identify the transaction in subsequent Read, ExecuteSql, Commit, or Rollback calls. Single-use read-only transactions do not have IDs, because single-use transactions do not support multiple requests.", "format": "byte", "type": "string"}, "precommitToken": {"$ref": "MultiplexedSessionPrecommitToken", "description": "A precommit token is included in the response of a BeginTransaction request if the read-write transaction is on a multiplexed session and a mutation_key was specified in the BeginTransaction. The precommit token with the highest sequence number from this transaction attempt should be passed to the Commit request for this transaction."}, "readTimestamp": {"description": "For snapshot read-only transactions, the read timestamp chosen for the transaction. Not returned by default: see TransactionOptions.ReadOnly.return_read_timestamp. A timestamp in RFC3339 UTC \\\"Zulu\\\" format, accurate to nanoseconds. Example: `\"2014-10-02T15:01:23.045123456Z\"`.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "TransactionOptions": {"description": "Transactions: Each session can have at most one active transaction at a time (note that standalone reads and queries use a transaction internally and do count towards the one transaction limit). After the active transaction is completed, the session can immediately be re-used for the next transaction. It is not necessary to create a new session for each transaction. Transaction modes: Cloud Spanner supports three transaction modes: 1. Locking read-write. This type of transaction is the only way to write data into Cloud Spanner. These transactions rely on pessimistic locking and, if necessary, two-phase commit. Locking read-write transactions may abort, requiring the application to retry. 2. Snapshot read-only. Snapshot read-only transactions provide guaranteed consistency across several reads, but do not allow writes. Snapshot read-only transactions can be configured to read at timestamps in the past, or configured to perform a strong read (where Spanner selects a timestamp such that the read is guaranteed to see the effects of all transactions that have committed before the start of the read). Snapshot read-only transactions do not need to be committed. Queries on change streams must be performed with the snapshot read-only transaction mode, specifying a strong read. See TransactionOptions.ReadOnly.strong for more details. 3. Partitioned DML. This type of transaction is used to execute a single Partitioned DML statement. Partitioned DML partitions the key space and runs the DML statement over each partition in parallel using separate, internal transactions that commit independently. Partitioned DML transactions do not need to be committed. For transactions that only read, snapshot read-only transactions provide simpler semantics and are almost always faster. In particular, read-only transactions do not take locks, so they do not conflict with read-write transactions. As a consequence of not taking locks, they also do not abort, so retry loops are not needed. Transactions may only read-write data in a single database. They may, however, read-write data in different tables within that database. Locking read-write transactions: Locking transactions may be used to atomically read-modify-write data anywhere in a database. This type of transaction is externally consistent. Clients should attempt to minimize the amount of time a transaction is active. Faster transactions commit with higher probability and cause less contention. Cloud Spanner attempts to keep read locks active as long as the transaction continues to do reads, and the transaction has not been terminated by Commit or Rollback. Long periods of inactivity at the client may cause Cloud Spanner to release a transaction's locks and abort it. Conceptually, a read-write transaction consists of zero or more reads or SQL statements followed by Commit. At any time before Commit, the client can send a Rollback request to abort the transaction. Semantics: Cloud Spanner can commit the transaction if all read locks it acquired are still valid at commit time, and it is able to acquire write locks for all writes. Cloud Spanner can abort the transaction for any reason. If a commit attempt returns `ABORTED`, Cloud Spanner guarantees that the transaction has not modified any user data in Cloud Spanner. Unless the transaction commits, Cloud Spanner makes no guarantees about how long the transaction's locks were held for. It is an error to use Cloud Spanner locks for any sort of mutual exclusion other than between Cloud Spanner transactions themselves. Retrying aborted transactions: When a transaction aborts, the application can choose to retry the whole transaction again. To maximize the chances of successfully committing the retry, the client should execute the retry in the same session as the original attempt. The original session's lock priority increases with each consecutive abort, meaning that each attempt has a slightly better chance of success than the previous. Note that the lock priority is preserved per session (not per transaction). Lock priority is set by the first read or write in the first attempt of a read-write transaction. If the application starts a new session to retry the whole transaction, the transaction loses its original lock priority. Moreover, the lock priority is only preserved if the transaction fails with an `ABORTED` error. Under some circumstances (for example, many transactions attempting to modify the same row(s)), a transaction can abort many times in a short period before successfully committing. Thus, it is not a good idea to cap the number of retries a transaction can attempt; instead, it is better to limit the total amount of time spent retrying. Idle transactions: A transaction is considered idle if it has no outstanding reads or SQL queries and has not started a read or SQL query within the last 10 seconds. Idle transactions can be aborted by Cloud Spanner so that they don't hold on to locks indefinitely. If an idle transaction is aborted, the commit fails with error `ABORTED`. If this behavior is undesirable, periodically executing a simple SQL query in the transaction (for example, `SELECT 1`) prevents the transaction from becoming idle. Snapshot read-only transactions: Snapshot read-only transactions provides a simpler method than locking read-write transactions for doing several consistent reads. However, this type of transaction does not support writes. Snapshot transactions do not take locks. Instead, they work by choosing a Cloud Spanner timestamp, then executing all reads at that timestamp. Since they do not acquire locks, they do not block concurrent read-write transactions. Unlike locking read-write transactions, snapshot read-only transactions never abort. They can fail if the chosen read timestamp is garbage collected; however, the default garbage collection policy is generous enough that most applications do not need to worry about this in practice. Snapshot read-only transactions do not need to call Commit or Rollback (and in fact are not permitted to do so). To execute a snapshot transaction, the client specifies a timestamp bound, which tells Cloud Spanner how to choose a read timestamp. The types of timestamp bound are: - Strong (the default). - Bounded staleness. - Exact staleness. If the Cloud Spanner database to be read is geographically distributed, stale read-only transactions can execute more quickly than strong or read-write transactions, because they are able to execute far from the leader replica. Each type of timestamp bound is discussed in detail below. Strong: Strong reads are guaranteed to see the effects of all transactions that have committed before the start of the read. Furthermore, all rows yielded by a single read are consistent with each other -- if any part of the read observes a transaction, all parts of the read see the transaction. Strong reads are not repeatable: two consecutive strong read-only transactions might return inconsistent results if there are concurrent writes. If consistency across reads is required, the reads should be executed within a transaction or at an exact read timestamp. Queries on change streams (see below for more details) must also specify the strong read timestamp bound. See TransactionOptions.ReadOnly.strong. Exact staleness: These timestamp bounds execute reads at a user-specified timestamp. Reads at a timestamp are guaranteed to see a consistent prefix of the global transaction history: they observe modifications done by all transactions with a commit timestamp less than or equal to the read timestamp, and observe none of the modifications done by transactions with a larger commit timestamp. They block until all conflicting transactions that can be assigned commit timestamps <= the read timestamp have finished. The timestamp can either be expressed as an absolute Cloud Spanner commit timestamp or a staleness relative to the current time. These modes do not require a \"negotiation phase\" to pick a timestamp. As a result, they execute slightly faster than the equivalent boundedly stale concurrency modes. On the other hand, boundedly stale reads usually return fresher results. See TransactionOptions.ReadOnly.read_timestamp and TransactionOptions.ReadOnly.exact_staleness. Bounded staleness: Bounded staleness modes allow Cloud Spanner to pick the read timestamp, subject to a user-provided staleness bound. Cloud Spanner chooses the newest timestamp within the staleness bound that allows execution of the reads at the closest available replica without blocking. All rows yielded are consistent with each other -- if any part of the read observes a transaction, all parts of the read see the transaction. Boundedly stale reads are not repeatable: two stale reads, even if they use the same staleness bound, can execute at different timestamps and thus return inconsistent results. Boundedly stale reads execute in two phases: the first phase negotiates a timestamp among all replicas needed to serve the read. In the second phase, reads are executed at the negotiated timestamp. As a result of the two phase execution, bounded staleness reads are usually a little slower than comparable exact staleness reads. However, they are typically able to return fresher results, and are more likely to execute at the closest replica. Because the timestamp negotiation requires up-front knowledge of which rows are read, it can only be used with single-use read-only transactions. See TransactionOptions.ReadOnly.max_staleness and TransactionOptions.ReadOnly.min_read_timestamp. Old read timestamps and garbage collection: Cloud Spanner continuously garbage collects deleted and overwritten data in the background to reclaim storage space. This process is known as \"version GC\". By default, version GC reclaims versions after they are one hour old. Because of this, Cloud Spanner can't perform reads at read timestamps more than one hour in the past. This restriction also applies to in-progress reads and/or SQL queries whose timestamp become too old while executing. Reads and SQL queries with too-old read timestamps fail with the error `FAILED_PRECONDITION`. You can configure and extend the `VERSION_RETENTION_PERIOD` of a database up to a period as long as one week, which allows Cloud Spanner to perform reads up to one week in the past. Querying change Streams: A Change Stream is a schema object that can be configured to watch data changes on the entire database, a set of tables, or a set of columns in a database. When a change stream is created, Spanner automatically defines a corresponding SQL Table-Valued Function (TVF) that can be used to query the change records in the associated change stream using the ExecuteStreamingSql API. The name of the TVF for a change stream is generated from the name of the change stream: READ_. All queries on change stream TVFs must be executed using the ExecuteStreamingSql API with a single-use read-only transaction with a strong read-only timestamp_bound. The change stream TVF allows users to specify the start_timestamp and end_timestamp for the time range of interest. All change records within the retention period is accessible using the strong read-only timestamp_bound. All other TransactionOptions are invalid for change stream queries. In addition, if TransactionOptions.read_only.return_read_timestamp is set to true, a special value of 2^63 - 2 is returned in the Transaction message that describes the transaction, instead of a valid read timestamp. This special value should be discarded and not used for any subsequent queries. Please see https://cloud.google.com/spanner/docs/change-streams for more details on how to query the change stream TVFs. Partitioned DML transactions: Partitioned DML transactions are used to execute DML statements with a different execution strategy that provides different, and often better, scalability properties for large, table-wide operations than DML in a ReadWrite transaction. Smaller scoped statements, such as an OLTP workload, should prefer using ReadWrite transactions. Partitioned DML partitions the keyspace and runs the DML statement on each partition in separate, internal transactions. These transactions commit automatically when complete, and run independently from one another. To reduce lock contention, this execution strategy only acquires read locks on rows that match the WHERE clause of the statement. Additionally, the smaller per-partition transactions hold locks for less time. That said, Partitioned DML is not a drop-in replacement for standard DML used in ReadWrite transactions. - The DML statement must be fully-partitionable. Specifically, the statement must be expressible as the union of many statements which each access only a single row of the table. - The statement is not applied atomically to all rows of the table. Rather, the statement is applied atomically to partitions of the table, in independent transactions. Secondary index rows are updated atomically with the base table rows. - Partitioned DML does not guarantee exactly-once execution semantics against a partition. The statement is applied at least once to each partition. It is strongly recommended that the DML statement should be idempotent to avoid unexpected results. For instance, it is potentially dangerous to run a statement such as `UPDATE table SET column = column + 1` as it could be run multiple times against some rows. - The partitions are committed automatically - there is no support for Commit or Rollback. If the call returns an error, or if the client issuing the ExecuteSql call dies, it is possible that some rows had the statement executed on them successfully. It is also possible that statement was never executed against other rows. - Partitioned DML transactions may only contain the execution of a single DML statement via ExecuteSql or ExecuteStreamingSql. - If any error is encountered during the execution of the partitioned DML operation (for instance, a UNIQUE INDEX violation, division by zero, or a value that can't be stored due to schema constraints), then the operation is stopped at that point and an error is returned. It is possible that at this point, some partitions have been committed (or even committed multiple times), and other partitions have not been run at all. Given the above, Partitioned DML is good fit for large, database-wide, operations that are idempotent, such as deleting old rows from a very large table.", "id": "TransactionOptions", "properties": {"excludeTxnFromChangeStreams": {"description": "When `exclude_txn_from_change_streams` is set to `true`, it prevents read or write transactions from being tracked in change streams. * If the DDL option `allow_txn_exclusion` is set to `true`, then the updates made within this transaction aren't recorded in the change stream. * If you don't set the DDL option `allow_txn_exclusion` or if it's set to `false`, then the updates made within this transaction are recorded in the change stream. When `exclude_txn_from_change_streams` is set to `false` or not set, modifications from this transaction are recorded in all change streams that are tracking columns modified by these transactions. The `exclude_txn_from_change_streams` option can only be specified for read-write or partitioned DML transactions, otherwise the API returns an `INVALID_ARGUMENT` error.", "type": "boolean"}, "isolationLevel": {"description": "Isolation level for the transaction.", "enum": ["ISOLATION_LEVEL_UNSPECIFIED", "SERIALIZABLE", "REPEATABLE_READ"], "enumDescriptions": ["Default value. If the value is not specified, the `SERIALIZABLE` isolation level is used.", "All transactions appear as if they executed in a serial order, even if some of the reads, writes, and other operations of distinct transactions actually occurred in parallel. Spanner assigns commit timestamps that reflect the order of committed transactions to implement this property. Spanner offers a stronger guarantee than serializability called external consistency. For further details, please refer to https://cloud.google.com/spanner/docs/true-time-external-consistency#serializability.", "All reads performed during the transaction observe a consistent snapshot of the database, and the transaction is only successfully committed in the absence of conflicts between its updates and any concurrent updates that have occurred since that snapshot. Consequently, in contrast to `SERIALIZABLE` transactions, only write-write conflicts are detected in snapshot transactions. This isolation level does not support Read-only and Partitioned DML transactions. When `REPEATABLE_READ` is specified on a read-write transaction, the locking semantics default to `OPTIMISTIC`."], "type": "string"}, "partitionedDml": {"$ref": "PartitionedDml", "description": "Partitioned DML transaction. Authorization to begin a Partitioned DML transaction requires `spanner.databases.beginPartitionedDmlTransaction` permission on the `session` resource."}, "readOnly": {"$ref": "Read<PERSON>nly", "description": "Transaction does not write. Authorization to begin a read-only transaction requires `spanner.databases.beginReadOnlyTransaction` permission on the `session` resource."}, "readWrite": {"$ref": "ReadWrite", "description": "Transaction may write. Authorization to begin a read-write transaction requires `spanner.databases.beginOrRollbackReadWriteTransaction` permission on the `session` resource."}}, "type": "object"}, "TransactionSelector": {"description": "This message is used to select the transaction in which a Read or ExecuteSql call runs. See TransactionOptions for more information about transactions.", "id": "TransactionSelector", "properties": {"begin": {"$ref": "TransactionOptions", "description": "Begin a new transaction and execute this read or SQL query in it. The transaction ID of the new transaction is returned in ResultSetMetadata.transaction, which is a Transaction."}, "id": {"description": "Execute the read or SQL query in a previously-started transaction.", "format": "byte", "type": "string"}, "singleUse": {"$ref": "TransactionOptions", "description": "Execute the read or SQL query in a temporary transaction. This is the most efficient way to execute a transaction that consists of a single SQL query."}}, "type": "object"}, "Type": {"description": "`Type` indicates the type of a Cloud Spanner value, as might be stored in a table cell or returned from an SQL query.", "id": "Type", "properties": {"arrayElementType": {"$ref": "Type", "description": "If code == ARRAY, then `array_element_type` is the type of the array elements."}, "code": {"description": "Required. The TypeCode for this type.", "enum": ["TYPE_CODE_UNSPECIFIED", "BOOL", "INT64", "FLOAT64", "FLOAT32", "TIMESTAMP", "DATE", "STRING", "BYTES", "ARRAY", "STRUCT", "NUMERIC", "JSON", "PROTO", "ENUM", "INTERVAL"], "enumDescriptions": ["Not specified.", "Encoded as JSON `true` or `false`.", "Encoded as `string`, in decimal format.", "Encoded as `number`, or the strings `\"NaN\"`, `\"Infinity\"`, or `\"-Infinity\"`.", "Encoded as `number`, or the strings `\"NaN\"`, `\"Infinity\"`, or `\"-Infinity\"`.", "Encoded as `string` in RFC 3339 timestamp format. The time zone must be present, and must be `\"Z\"`. If the schema has the column option `allow_commit_timestamp=true`, the placeholder string `\"spanner.commit_timestamp()\"` can be used to instruct the system to insert the commit timestamp associated with the transaction commit.", "Encoded as `string` in RFC 3339 date format.", "Encoded as `string`.", "Encoded as a base64-encoded `string`, as described in RFC 4648, section 4.", "Encoded as `list`, where the list elements are represented according to array_element_type.", "Encoded as `list`, where list element `i` is represented according to [struct_type.fields[i]][google.spanner.v1.StructType.fields].", "Encoded as `string`, in decimal format or scientific notation format. Decimal format: `[+-]Digits[.[Digits]]` or `+-.Digits` Scientific notation: `[+-]Digits[.[Digits]][ExponentIndicator[+-]Digits]` or `+-.Digits[ExponentIndicator[+-]Digits]` (ExponentIndicator is `\"e\"` or `\"E\"`)", "Encoded as a JSON-formatted `string` as described in RFC 7159. The following rules are applied when parsing JSON input: - Whitespace characters are not preserved. - If a JSON object has duplicate keys, only the first key is preserved. - Members of a JSON object are not guaranteed to have their order preserved. - JSON array elements will have their order preserved.", "Encoded as a base64-encoded `string`, as described in RFC 4648, section 4.", "Encoded as `string`, in decimal format.", "Encoded as `string`, in `ISO8601` duration format - `P[n]Y[n]M[n]DT[n]H[n]M[n[.fraction]]S` where `n` is an integer. For example, `P1Y2M3DT4H5M6.5S` represents time duration of 1 year, 2 months, 3 days, 4 hours, 5 minutes, and 6.5 seconds."], "type": "string"}, "protoTypeFqn": {"description": "If code == PROTO or code == ENUM, then `proto_type_fqn` is the fully qualified name of the proto type representing the proto/enum definition.", "type": "string"}, "structType": {"$ref": "StructType", "description": "If code == STRUCT, then `struct_type` provides type information for the struct's fields."}, "typeAnnotation": {"description": "The TypeAnnotationCode that disambiguates SQL type that Spanner will use to represent values of this type during query processing. This is necessary for some type codes because a single TypeCode can be mapped to different SQL types depending on the SQL dialect. type_annotation typically is not needed to process the content of a value (it doesn't affect serialization) and clients can ignore it on the read path.", "enum": ["TYPE_ANNOTATION_CODE_UNSPECIFIED", "PG_NUMERIC", "PG_JSONB", "PG_OID"], "enumDescriptions": ["Not specified.", "PostgreSQL compatible NUMERIC type. This annotation needs to be applied to Type instances having NUMERIC type code to specify that values of this type should be treated as PostgreSQL NUMERIC values. Currently this annotation is always needed for NUMERIC when a client interacts with PostgreSQL-enabled Spanner databases.", "PostgreSQL compatible JSONB type. This annotation needs to be applied to Type instances having JSON type code to specify that values of this type should be treated as PostgreSQL JSONB values. Currently this annotation is always needed for JSON when a client interacts with PostgreSQL-enabled Spanner databases.", "PostgreSQL compatible OID type. This annotation can be used by a client interacting with PostgreSQL-enabled Spanner database to specify that a value should be treated using the semantics of the OID type."], "type": "string"}}, "type": "object"}, "UpdateDatabaseDdlMetadata": {"description": "Metadata type for the operation returned by UpdateDatabaseDdl.", "id": "UpdateDatabaseDdlMetadata", "properties": {"actions": {"description": "The brief action info for the DDL statements. `actions[i]` is the brief info for `statements[i]`.", "items": {"$ref": "DdlStatementActionInfo"}, "type": "array"}, "commitTimestamps": {"description": "Reports the commit timestamps of all statements that have succeeded so far, where `commit_timestamps[i]` is the commit timestamp for the statement `statements[i]`.", "items": {"format": "google-datetime", "type": "string"}, "type": "array"}, "database": {"description": "The database being modified.", "type": "string"}, "progress": {"description": "The progress of the UpdateDatabaseDdl operations. All DDL statements will have continuously updating progress, and `progress[i]` is the operation progress for `statements[i]`. Also, `progress[i]` will have start time and end time populated with commit timestamp of operation, as well as a progress of 100% once the operation has completed.", "items": {"$ref": "OperationProgress"}, "type": "array"}, "statements": {"description": "For an update this list contains all the statements. For an individual statement, this list contains only that statement.", "items": {"type": "string"}, "type": "array"}, "throttled": {"description": "Output only. When true, indicates that the operation is throttled e.g. due to resource constraints. When resources become available the operation will resume and this field will be false again.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "UpdateDatabaseDdlRequest": {"description": "Enqueues the given DDL statements to be applied, in order but not necessarily all at once, to the database schema at some point (or points) in the future. The server checks that the statements are executable (syntactically valid, name tables that exist, etc.) before enqueueing them, but they may still fail upon later execution (e.g., if a statement from another batch of statements is applied first and it conflicts in some way, or if there is some data-related problem like a `NULL` value in a column to which `NOT NULL` would be added). If a statement fails, all subsequent statements in the batch are automatically cancelled. Each batch of statements is assigned a name which can be used with the Operations API to monitor progress. See the operation_id field for more details.", "id": "UpdateDatabaseDdlRequest", "properties": {"operationId": {"description": "If empty, the new update request is assigned an automatically-generated operation ID. Otherwise, `operation_id` is used to construct the name of the resulting Operation. Specifying an explicit operation ID simplifies determining whether the statements were executed in the event that the UpdateDatabaseDdl call is replayed, or the return value is otherwise lost: the database and `operation_id` fields can be combined to form the `name` of the resulting longrunning.Operation: `/operations/`. `operation_id` should be unique within the database, and must be a valid identifier: `a-z*`. Note that automatically-generated operation IDs always begin with an underscore. If the named operation already exists, UpdateDatabaseDdl returns `ALREADY_EXISTS`.", "type": "string"}, "protoDescriptors": {"description": "Optional. Proto descriptors used by CREATE/ALTER PROTO BUNDLE statements. Contains a protobuf-serialized [google.protobuf.FileDescriptorSet](https://github.com/protocolbuffers/protobuf/blob/main/src/google/protobuf/descriptor.proto). To generate it, [install](https://grpc.io/docs/protoc-installation/) and run `protoc` with --include_imports and --descriptor_set_out. For example, to generate for moon/shot/app.proto, run ``` $protoc --proto_path=/app_path --proto_path=/lib_path \\ --include_imports \\ --descriptor_set_out=descriptors.data \\ moon/shot/app.proto ``` For more details, see protobuffer [self description](https://developers.google.com/protocol-buffers/docs/techniques#self-description).", "format": "byte", "type": "string"}, "statements": {"description": "Required. DDL statements to be applied to the database.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "UpdateDatabaseMetadata": {"description": "Metadata type for the operation returned by UpdateDatabase.", "id": "UpdateDatabaseMetadata", "properties": {"cancelTime": {"description": "The time at which this operation was cancelled. If set, this operation is in the process of undoing itself (which is best-effort).", "format": "google-datetime", "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "The progress of the UpdateDatabase operation."}, "request": {"$ref": "UpdateDatabaseRequest", "description": "The request for UpdateDatabase."}}, "type": "object"}, "UpdateDatabaseRequest": {"description": "The request for UpdateDatabase.", "id": "UpdateDatabaseRequest", "properties": {"database": {"$ref": "Database", "description": "Required. The database to update. The `name` field of the database is of the form `projects//instances//databases/`."}, "updateMask": {"description": "Required. The list of fields to update. Currently, only `enable_drop_protection` field can be updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "UpdateInstanceConfigMetadata": {"description": "Metadata type for the operation returned by UpdateInstanceConfig.", "id": "UpdateInstanceConfigMetadata", "properties": {"cancelTime": {"description": "The time at which this operation was cancelled.", "format": "google-datetime", "type": "string"}, "instanceConfig": {"$ref": "InstanceConfig", "description": "The desired instance configuration after updating."}, "progress": {"$ref": "InstanceOperationProgress", "description": "The progress of the UpdateInstanceConfig operation."}}, "type": "object"}, "UpdateInstanceConfigRequest": {"description": "The request for UpdateInstanceConfig.", "id": "UpdateInstanceConfigRequest", "properties": {"instanceConfig": {"$ref": "InstanceConfig", "description": "Required. The user instance configuration to update, which must always include the instance configuration name. Otherwise, only fields mentioned in update_mask need be included. To prevent conflicts of concurrent updates, etag can be used."}, "updateMask": {"description": "Required. A mask specifying which fields in InstanceConfig should be updated. The field mask must always be specified; this prevents any future fields in InstanceConfig from being erased accidentally by clients that do not know about them. Only display_name and labels can be updated.", "format": "google-fieldmask", "type": "string"}, "validateOnly": {"description": "An option to validate, but not actually execute, a request, and provide the same response.", "type": "boolean"}}, "type": "object"}, "UpdateInstanceMetadata": {"description": "Metadata type for the operation returned by UpdateInstance.", "id": "UpdateInstanceMetadata", "properties": {"cancelTime": {"description": "The time at which this operation was cancelled. If set, this operation is in the process of undoing itself (which is guaranteed to succeed) and cannot be cancelled again.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "expectedFulfillmentPeriod": {"description": "The expected fulfillment period of this update operation.", "enum": ["FULFILLMENT_PERIOD_UNSPECIFIED", "FULFILLMENT_PERIOD_NORMAL", "FULFILLMENT_PERIOD_EXTENDED"], "enumDescriptions": ["Not specified.", "Normal fulfillment period. The operation is expected to complete within minutes.", "Extended fulfillment period. It can take up to an hour for the operation to complete."], "type": "string"}, "instance": {"$ref": "Instance", "description": "The desired end state of the update."}, "startTime": {"description": "The time at which UpdateInstance request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UpdateInstancePartitionMetadata": {"description": "Metadata type for the operation returned by UpdateInstancePartition.", "id": "UpdateInstancePartitionMetadata", "properties": {"cancelTime": {"description": "The time at which this operation was cancelled. If set, this operation is in the process of undoing itself (which is guaranteed to succeed) and cannot be cancelled again.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "instancePartition": {"$ref": "InstancePartition", "description": "The desired end state of the update."}, "startTime": {"description": "The time at which UpdateInstancePartition request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UpdateInstancePartitionRequest": {"description": "The request for UpdateInstancePartition.", "id": "UpdateInstancePartitionRequest", "properties": {"fieldMask": {"description": "Required. A mask specifying which fields in InstancePartition should be updated. The field mask must always be specified; this prevents any future fields in InstancePartition from being erased accidentally by clients that do not know about them.", "format": "google-fieldmask", "type": "string"}, "instancePartition": {"$ref": "InstancePartition", "description": "Required. The instance partition to update, which must always include the instance partition name. Otherwise, only fields mentioned in field_mask need be included."}}, "type": "object"}, "UpdateInstanceRequest": {"description": "The request for UpdateInstance.", "id": "UpdateInstanceRequest", "properties": {"fieldMask": {"description": "Required. A mask specifying which fields in Instance should be updated. The field mask must always be specified; this prevents any future fields in Instance from being erased accidentally by clients that do not know about them.", "format": "google-fieldmask", "type": "string"}, "instance": {"$ref": "Instance", "description": "Required. The instance to update, which must always include the instance name. Otherwise, only fields mentioned in field_mask need be included."}}, "type": "object"}, "VisualizationData": {"id": "VisualizationData", "properties": {"dataSourceEndToken": {"description": "The token signifying the end of a data_source.", "type": "string"}, "dataSourceSeparatorToken": {"description": "The token delimiting a datasource name from the rest of a key in a data_source.", "type": "string"}, "diagnosticMessages": {"description": "The list of messages (info, alerts, ...)", "items": {"$ref": "DiagnosticMessage"}, "type": "array"}, "endKeyStrings": {"description": "We discretize the entire keyspace into buckets. Assuming each bucket has an inclusive keyrange and covers keys from k(i) ... k(n). In this case k(n) would be an end key for a given range. end_key_string is the collection of all such end keys", "items": {"type": "string"}, "type": "array"}, "hasPii": {"description": "Whether this scan contains PII.", "type": "boolean"}, "indexedKeys": {"description": "Keys of key ranges that contribute significantly to a given metric Can be thought of as heavy hitters.", "items": {"type": "string"}, "type": "array"}, "keySeparator": {"description": "The token delimiting the key prefixes.", "type": "string"}, "keyUnit": {"description": "The unit for the key: e.g. 'key' or 'chunk'.", "enum": ["KEY_UNIT_UNSPECIFIED", "KEY", "CHUNK"], "enumDescriptions": ["Required default value", "Each entry corresponds to one key", "Each entry corresponds to a chunk of keys"], "type": "string"}, "metrics": {"description": "The list of data objects for each metric.", "items": {"$ref": "Metric"}, "type": "array"}, "prefixNodes": {"description": "The list of extracted key prefix nodes used in the key prefix hierarchy.", "items": {"$ref": "PrefixNode"}, "type": "array"}}, "type": "object"}, "Write": {"description": "Arguments to insert, update, insert_or_update, and replace operations.", "id": "Write", "properties": {"columns": {"description": "The names of the columns in table to be written. The list of columns must contain enough columns to allow Cloud Spanner to derive values for all primary key columns in the row(s) to be modified.", "items": {"type": "string"}, "type": "array"}, "table": {"description": "Required. The table whose rows will be written.", "type": "string"}, "values": {"description": "The values to be written. `values` can contain more than one list of values. If it does, then multiple rows are written, one for each entry in `values`. Each list in `values` must have exactly as many entries as there are entries in columns above. Sending multiple lists is equivalent to sending multiple `Mutation`s, each containing one `values` entry and repeating table and columns. Individual values in each list are encoded as described here.", "items": {"items": {"type": "any"}, "type": "array"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Spanner API", "version": "v1", "version_module": true}