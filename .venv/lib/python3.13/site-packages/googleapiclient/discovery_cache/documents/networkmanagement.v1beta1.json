{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://networkmanagement.googleapis.com/", "batchPath": "batch", "canonicalName": "Network Management", "description": "The Network Management API provides a collection of network performance monitoring and diagnostic capabilities.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "networkmanagement:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://networkmanagement.mtls.googleapis.com/", "name": "networkmanagement", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}", "httpMethod": "GET", "id": "networkmanagement.organizations.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1beta1/organizations/{organizationsId}/locations", "httpMethod": "GET", "id": "networkmanagement.organizations.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"global": {"resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/global/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "networkmanagement.organizations.locations.global.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^organizations/[^/]+/locations/global/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/global/operations/{operationsId}", "httpMethod": "DELETE", "id": "networkmanagement.organizations.locations.global.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^organizations/[^/]+/locations/global/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/global/operations/{operationsId}", "httpMethod": "GET", "id": "networkmanagement.organizations.locations.global.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/global/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/global/operations", "httpMethod": "GET", "id": "networkmanagement.organizations.locations.global.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/global$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "vpcFlowLogsConfigs": {"methods": {"create": {"description": "Creates a new `VpcFlowLogsConfig`. If a configuration with the exact same settings already exists (even if the ID is different), the creation fails. Notes: 1. Creating a configuration with state=DISABLED will fail 2. The following fields are not considered as `settings` for the purpose of the check mentioned above, therefore - creating another configuration with the same fields but different values for the following fields will fail as well: * name * create_time * update_time * labels * description", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/vpcFlowLogsConfigs", "httpMethod": "POST", "id": "networkmanagement.organizations.locations.vpcFlowLogsConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the VPC Flow Logs configuration to create: `projects/{project_id}/locations/global` `organizations/{organization_id}/locations/global`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "vpcFlowLogsConfigId": {"description": "Required. ID of the `VpcFlowLogsConfig`.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/vpcFlowLogsConfigs", "request": {"$ref": "VpcFlowLogsConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a specific `VpcFlowLogsConfig`.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/vpcFlowLogsConfigs/{vpcFlowLogsConfigsId}", "httpMethod": "DELETE", "id": "networkmanagement.organizations.locations.vpcFlowLogsConfigs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `VpcFlowLogsConfig` resource name using one of the form: `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}` `organizations/{organization_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/vpcFlowLogsConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a specific `VpcFlowLogsConfig`.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/vpcFlowLogsConfigs/{vpcFlowLogsConfigsId}", "httpMethod": "GET", "id": "networkmanagement.organizations.locations.vpcFlowLogsConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `VpcFlowLogsConfig` resource name using the form: `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}` `organizations/{organization_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/vpcFlowLogsConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "VpcFlowLogsConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all `VpcFlowLogsConfigs` in a given organization.", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/vpcFlowLogsConfigs", "httpMethod": "GET", "id": "networkmanagement.organizations.locations.vpcFlowLogsConfigs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Lists the `VpcFlowLogsConfigs` that match the filter expression. A filter expression must use the supported [CEL logic operators] (https://cloud.google.com/vpc/docs/about-flow-logs-records#supported_cel_logic_operators).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Number of `VpcFlowLogsConfigs` to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Page token from an earlier query, as returned in `next_page_token`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the VpcFlowLogsConfig: `projects/{project_id}/locations/global` `organizations/{organization_id}/locations/global`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/vpcFlowLogsConfigs", "response": {"$ref": "ListVpcFlowLogsConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing `VpcFlowLogsConfig`. If a configuration with the exact same settings already exists (even if the ID is different), the creation fails. Notes: 1. Updating a configuration with state=DISABLED will fail 2. The following fields are not considered as `settings` for the purpose of the check mentioned above, therefore - updating another configuration with the same fields but different values for the following fields will fail as well: * name * create_time * update_time * labels * description", "flatPath": "v1beta1/organizations/{organizationsId}/locations/{locationsId}/vpcFlowLogsConfigs/{vpcFlowLogsConfigsId}", "httpMethod": "PATCH", "id": "networkmanagement.organizations.locations.vpcFlowLogsConfigs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Unique name of the configuration using one of the forms: `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config_id}` `organizations/{organization_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config_id}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/vpcFlowLogsConfigs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update. At least one path must be supplied in this field.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "VpcFlowLogsConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "networkmanagement.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1beta1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "networkmanagement.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"global": {"resources": {"connectivityTests": {"methods": {"create": {"description": "Creates a new Connectivity Test. After you create a test, the reachability analysis is performed as part of the long running operation, which completes when the analysis completes. If the endpoint specifications in `ConnectivityTest` are invalid (for example, containing non-existent resources in the network, or you don't have read permissions to the network configurations of listed projects), then the reachability result returns a value of `UNKNOWN`. If the endpoint specifications in `ConnectivityTest` are incomplete, the reachability result returns a value of AMBIGUOUS. For more information, see the Connectivity Test documentation.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests", "httpMethod": "POST", "id": "networkmanagement.projects.locations.global.connectivityTests.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the Connectivity Test to create: `projects/{project_id}/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/global$", "required": true, "type": "string"}, "testId": {"description": "Required. The logical name of the Connectivity Test in your project with the following restrictions: * Must contain only lowercase letters, numbers, and hyphens. * Must start with a letter. * Must be between 1-40 characters. * Must end with a number or a letter. * Must be unique within the customer project", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/connectivityTests", "request": {"$ref": "ConnectivityTest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a specific `ConnectivityTest`.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests/{connectivityTestsId}", "httpMethod": "DELETE", "id": "networkmanagement.projects.locations.global.connectivityTests.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Connectivity Test resource name using the form: `projects/{project_id}/locations/global/connectivityTests/{test_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/global/connectivityTests/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a specific Connectivity Test.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests/{connectivityTestsId}", "httpMethod": "GET", "id": "networkmanagement.projects.locations.global.connectivityTests.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `ConnectivityTest` resource name using the form: `projects/{project_id}/locations/global/connectivityTests/{test_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/global/connectivityTests/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "ConnectivityTest"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests/{connectivityTestsId}:getIamPolicy", "httpMethod": "GET", "id": "networkmanagement.projects.locations.global.connectivityTests.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/connectivityTests/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all Connectivity Tests owned by a project.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests", "httpMethod": "GET", "id": "networkmanagement.projects.locations.global.connectivityTests.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Lists the `ConnectivityTests` that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form ` ` where operators: `<`, `>`, `<=`, `>=`, `!=`, `=`, `:` are supported (colon `:` represents a HAS operator which is roughly synonymous with equality). can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = \"projects/proj-1/locations/global/connectivityTests/test-1 - Filter by labels: - Resources that have a key called `foo` labels.foo:* - Resources that have a key called `foo` whose value is `bar` labels.foo = bar", "location": "query", "type": "string"}, "orderBy": {"description": "Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of `ConnectivityTests` to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token from an earlier query, as returned in `next_page_token`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the Connectivity Tests: `projects/{project_id}/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/global$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/connectivityTests", "response": {"$ref": "ListConnectivityTestsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the configuration of an existing `ConnectivityTest`. After you update a test, the reachability analysis is performed as part of the long running operation, which completes when the analysis completes. The Reachability state in the test resource is updated with the new result. If the endpoint specifications in `ConnectivityTest` are invalid (for example, they contain non-existent resources in the network, or the user does not have read permissions to the network configurations of listed projects), then the reachability result returns a value of UNKNOWN. If the endpoint specifications in `ConnectivityTest` are incomplete, the reachability result returns a value of `AMBIGUOUS`. See the documentation in `ConnectivityTest` for more details.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests/{connectivityTestsId}", "httpMethod": "PATCH", "id": "networkmanagement.projects.locations.global.connectivityTests.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Unique name of the resource using the form: `projects/{project_id}/locations/global/connectivityTests/{test}`", "location": "path", "pattern": "^projects/[^/]+/locations/global/connectivityTests/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update. At least one path must be supplied in this field.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "ConnectivityTest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rerun": {"description": "Rerun an existing `ConnectivityTest`. After the user triggers the rerun, the reachability analysis is performed as part of the long running operation, which completes when the analysis completes. Even though the test configuration remains the same, the reachability result may change due to underlying network configuration changes. If the endpoint specifications in `ConnectivityTest` become invalid (for example, specified resources are deleted in the network, or you lost read permissions to the network configurations of listed projects), then the reachability result returns a value of `UNKNOWN`.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests/{connectivityTestsId}:rerun", "httpMethod": "POST", "id": "networkmanagement.projects.locations.global.connectivityTests.rerun", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Connectivity Test resource name using the form: `projects/{project_id}/locations/global/connectivityTests/{test_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/global/connectivityTests/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:rerun", "request": {"$ref": "RerunConnectivityTestRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests/{connectivityTestsId}:setIamPolicy", "httpMethod": "POST", "id": "networkmanagement.projects.locations.global.connectivityTests.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/connectivityTests/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/connectivityTests/{connectivityTestsId}:testIamPermissions", "httpMethod": "POST", "id": "networkmanagement.projects.locations.global.connectivityTests.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/global/connectivityTests/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "networkmanagement.projects.locations.global.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/global/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/operations/{operationsId}", "httpMethod": "DELETE", "id": "networkmanagement.projects.locations.global.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/global/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/operations/{operationsId}", "httpMethod": "GET", "id": "networkmanagement.projects.locations.global.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/global/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/global/operations", "httpMethod": "GET", "id": "networkmanagement.projects.locations.global.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/global$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "vpcFlowLogsConfigs": {"methods": {"create": {"description": "Creates a new `VpcFlowLogsConfig`. If a configuration with the exact same settings already exists (even if the ID is different), the creation fails. Notes: 1. Creating a configuration with state=DISABLED will fail 2. The following fields are not considered as `settings` for the purpose of the check mentioned above, therefore - creating another configuration with the same fields but different values for the following fields will fail as well: * name * create_time * update_time * labels * description", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/vpcFlowLogsConfigs", "httpMethod": "POST", "id": "networkmanagement.projects.locations.vpcFlowLogsConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the VPC Flow Logs configuration to create: `projects/{project_id}/locations/global` `organizations/{organization_id}/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "vpcFlowLogsConfigId": {"description": "Required. ID of the `VpcFlowLogsConfig`.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/vpcFlowLogsConfigs", "request": {"$ref": "VpcFlowLogsConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a specific `VpcFlowLogsConfig`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/vpcFlowLogsConfigs/{vpcFlowLogsConfigsId}", "httpMethod": "DELETE", "id": "networkmanagement.projects.locations.vpcFlowLogsConfigs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `VpcFlowLogsConfig` resource name using one of the form: `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}` `organizations/{organization_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vpcFlowLogsConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a specific `VpcFlowLogsConfig`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/vpcFlowLogsConfigs/{vpcFlowLogsConfigsId}", "httpMethod": "GET", "id": "networkmanagement.projects.locations.vpcFlowLogsConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. `VpcFlowLogsConfig` resource name using the form: `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}` `organizations/{organization_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vpcFlowLogsConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "VpcFlowLogsConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all `VpcFlowLogsConfigs` in a given project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/vpcFlowLogsConfigs", "httpMethod": "GET", "id": "networkmanagement.projects.locations.vpcFlowLogsConfigs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Lists the `VpcFlowLogsConfigs` that match the filter expression. A filter expression must use the supported [CEL logic operators] (https://cloud.google.com/vpc/docs/about-flow-logs-records#supported_cel_logic_operators).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Number of `VpcFlowLogsConfigs` to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Page token from an earlier query, as returned in `next_page_token`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the VpcFlowLogsConfig: `projects/{project_id}/locations/global` `organizations/{organization_id}/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/vpcFlowLogsConfigs", "response": {"$ref": "ListVpcFlowLogsConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing `VpcFlowLogsConfig`. If a configuration with the exact same settings already exists (even if the ID is different), the creation fails. Notes: 1. Updating a configuration with state=DISABLED will fail 2. The following fields are not considered as `settings` for the purpose of the check mentioned above, therefore - updating another configuration with the same fields but different values for the following fields will fail as well: * name * create_time * update_time * labels * description", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/vpcFlowLogsConfigs/{vpcFlowLogsConfigsId}", "httpMethod": "PATCH", "id": "networkmanagement.projects.locations.vpcFlowLogsConfigs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Unique name of the configuration using one of the forms: `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config_id}` `organizations/{organization_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vpcFlowLogsConfigs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update. At least one path must be supplied in this field.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "VpcFlowLogsConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryOrgVpcFlowLogsConfigs": {"description": "QueryOrgVpcFlowLogsConfigs lists Organization resources for a given project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/vpcFlowLogsConfigs:queryOrgVpcFlowLogsConfigs", "httpMethod": "GET", "id": "networkmanagement.projects.locations.vpcFlowLogsConfigs.queryOrgVpcFlowLogsConfigs", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Lists the `VpcFlowLogsConfigs` that match the filter expression. A filter expression must use the supported [CEL logic operators] (https://cloud.google.com/vpc/docs/about-flow-logs-records#supported_cel_logic_operators).", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Number of `VpcFlowLogsConfigs` to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Page token from an earlier query, as returned in `next_page_token`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the VpcFlowLogsConfig: `projects/{project_id}/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/vpcFlowLogsConfigs:queryOrgVpcFlowLogsConfigs", "response": {"$ref": "QueryOrgVpcFlowLogsConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250611", "rootUrl": "https://networkmanagement.googleapis.com/", "schemas": {"AbortInfo": {"description": "Details of the final state \"abort\" and associated resource.", "id": "AbortInfo", "properties": {"cause": {"description": "Causes that the analysis is aborted.", "enum": ["CAUSE_UNSPECIFIED", "UNKNOWN_NETWORK", "UNKNOWN_PROJECT", "NO_EXTERNAL_IP", "UNINTENDED_DESTINATION", "SOURCE_ENDPOINT_NOT_FOUND", "MISMATCHED_SOURCE_NETWORK", "DESTINATION_ENDPOINT_NOT_FOUND", "MISMATCHED_DESTINATION_NETWORK", "UNKNOWN_IP", "GOOGLE_MANAGED_SERVICE_UNKNOWN_IP", "SOURCE_IP_ADDRESS_NOT_IN_SOURCE_NETWORK", "PERMISSION_DENIED", "PERMISSION_DENIED_NO_CLOUD_NAT_CONFIGS", "PERMISSION_DENIED_NO_NEG_ENDPOINT_CONFIGS", "PERMISSION_DENIED_NO_CLOUD_ROUTER_CONFIGS", "NO_SOURCE_LOCATION", "INVALID_ARGUMENT", "TRACE_TOO_LONG", "INTERNAL_ERROR", "UNSUPPORTED", "MISMATCHED_IP_VERSION", "GKE_KONNECTIVITY_PROXY_UNSUPPORTED", "RESOURCE_CONFIG_NOT_FOUND", "VM_INSTANCE_CONFIG_NOT_FOUND", "NETWORK_CONFIG_NOT_FOUND", "FIREWALL_CONFIG_NOT_FOUND", "ROUTE_CONFIG_NOT_FOUND", "GOOGLE_MANAGED_SERVICE_AMBIGUOUS_PSC_ENDPOINT", "GOOGLE_MANAGED_SERVICE_AMBIGUOUS_ENDPOINT", "SOURCE_PSC_CLOUD_SQL_UNSUPPORTED", "SOURCE_REDIS_CLUSTER_UNSUPPORTED", "SOURCE_REDIS_INSTANCE_UNSUPPORTED", "SOURCE_FORWARDING_RULE_UNSUPPORTED", "NON_ROUTABLE_IP_ADDRESS", "UNKNOWN_ISSUE_IN_GOOGLE_MANAGED_PROJECT", "UNSUPPORTED_GOOGLE_MANAGED_PROJECT_CONFIG", "NO_SERVERLESS_IP_RANGES"], "enumDeprecated": [false, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Cause is unspecified.", "Aborted due to unknown network. Deprecated, not used in the new tests.", "Aborted because no project information can be derived from the test input. Deprecated, not used in the new tests.", "Aborted because traffic is sent from a public IP to an instance without an external IP. Deprecated, not used in the new tests.", "Aborted because none of the traces matches destination information specified in the input test request. Deprecated, not used in the new tests.", "Aborted because the source endpoint could not be found. Deprecated, not used in the new tests.", "Aborted because the source network does not match the source endpoint. Deprecated, not used in the new tests.", "Aborted because the destination endpoint could not be found. Deprecated, not used in the new tests.", "Aborted because the destination network does not match the destination endpoint. Deprecated, not used in the new tests.", "Aborted because no endpoint with the packet's destination IP address is found.", "Aborted because no endpoint with the packet's destination IP is found in the Google-managed project.", "Aborted because the source IP address doesn't belong to any of the subnets of the source VPC network.", "Aborted because user lacks permission to access all or part of the network configurations required to run the test.", "Aborted because user lacks permission to access Cloud NAT configs required to run the test.", "Aborted because user lacks permission to access Network endpoint group endpoint configs required to run the test.", "Aborted because user lacks permission to access Cloud Router configs required to run the test.", "Aborted because no valid source or destination endpoint is derived from the input test request.", "Aborted because the source or destination endpoint specified in the request is invalid. Some examples: - The request might contain malformed resource URI, project ID, or IP address. - The request might contain inconsistent information (for example, the request might include both the instance and the network, but the instance might not have a NIC in that network).", "Aborted because the number of steps in the trace exceeds a certain limit. It might be caused by a routing loop.", "Aborted due to internal server error.", "Aborted because the test scenario is not supported.", "Aborted because the source and destination resources have no common IP version.", "Aborted because the connection between the control plane and the node of the source cluster is initiated by the node and managed by the Konnectivity proxy.", "Aborted because expected resource configuration was missing.", "Aborted because expected VM instance configuration was missing.", "Aborted because expected network configuration was missing.", "Aborted because expected firewall configuration was missing.", "Aborted because expected route configuration was missing.", "Aborted because PSC endpoint selection for the Google-managed service is ambiguous (several PSC endpoints satisfy test input).", "Aborted because endpoint selection for the Google-managed service is ambiguous (several endpoints satisfy test input).", "Aborted because tests with a PSC-based Cloud SQL instance as a source are not supported.", "Aborted because tests with a Redis Cluster as a source are not supported.", "Aborted because tests with a Redis Instance as a source are not supported.", "Aborted because tests with a forwarding rule as a source are not supported.", "Aborted because one of the endpoints is a non-routable IP address (loopback, link-local, etc).", "Aborted due to an unknown issue in the Google-managed project.", "Aborted due to an unsupported configuration of the Google-managed project.", "Aborted because the source endpoint is a Cloud Run revision with direct VPC access enabled, but there are no reserved serverless IP ranges."], "type": "string"}, "ipAddress": {"description": "IP address that caused the abort.", "type": "string"}, "projectsMissingPermission": {"description": "List of project IDs the user specified in the request but lacks access to. In this case, analysis is aborted with the PERMISSION_DENIED cause.", "items": {"type": "string"}, "type": "array"}, "resourceUri": {"description": "URI of the resource that caused the abort.", "type": "string"}}, "type": "object"}, "AppEngineVersionEndpoint": {"description": "Wrapper for the App Engine service version attributes.", "id": "AppEngineVersionEndpoint", "properties": {"uri": {"description": "An [App Engine](https://cloud.google.com/appengine) [service version](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1/apps.services.versions) name.", "type": "string"}}, "type": "object"}, "AppEngineVersionInfo": {"description": "For display only. Metadata associated with an App Engine version.", "id": "AppEngineVersionInfo", "properties": {"displayName": {"description": "Name of an App Engine version.", "type": "string"}, "environment": {"description": "App Engine execution environment for a version.", "type": "string"}, "runtime": {"description": "Runtime of the App Engine version.", "type": "string"}, "uri": {"description": "URI of an App Engine version.", "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CloudFunctionEndpoint": {"description": "Wrapper for Cloud Function attributes.", "id": "CloudFunctionEndpoint", "properties": {"uri": {"description": "A [Cloud Function](https://cloud.google.com/functions) name.", "type": "string"}}, "type": "object"}, "CloudFunctionInfo": {"description": "For display only. Metadata associated with a Cloud Function.", "id": "CloudFunctionInfo", "properties": {"displayName": {"description": "Name of a Cloud Function.", "type": "string"}, "location": {"description": "Location in which the Cloud Function is deployed.", "type": "string"}, "uri": {"description": "URI of a Cloud Function.", "type": "string"}, "versionId": {"description": "Latest successfully deployed version id of the Cloud Function.", "format": "int64", "type": "string"}}, "type": "object"}, "CloudRunRevisionEndpoint": {"description": "Wrapper for Cloud Run revision attributes.", "id": "CloudRunRevisionEndpoint", "properties": {"serviceUri": {"description": "Output only. The URI of the Cloud Run service that the revision belongs to. The format is: projects/{project}/locations/{location}/services/{service}", "readOnly": true, "type": "string"}, "uri": {"description": "A [Cloud Run](https://cloud.google.com/run) [revision](https://cloud.google.com/run/docs/reference/rest/v1/namespaces.revisions/get) URI. The format is: projects/{project}/locations/{location}/revisions/{revision}", "type": "string"}}, "type": "object"}, "CloudRunRevisionInfo": {"description": "For display only. Metadata associated with a Cloud Run revision.", "id": "CloudRunRevisionInfo", "properties": {"displayName": {"description": "Name of a Cloud Run revision.", "type": "string"}, "location": {"description": "Location in which this revision is deployed.", "type": "string"}, "serviceUri": {"description": "URI of Cloud Run service this revision belongs to.", "type": "string"}, "uri": {"description": "URI of a Cloud Run revision.", "type": "string"}}, "type": "object"}, "CloudSQLInstanceInfo": {"description": "For display only. Metadata associated with a Cloud SQL instance.", "id": "CloudSQLInstanceInfo", "properties": {"displayName": {"description": "Name of a Cloud SQL instance.", "type": "string"}, "externalIp": {"description": "External IP address of a Cloud SQL instance.", "type": "string"}, "internalIp": {"description": "Internal IP address of a Cloud SQL instance.", "type": "string"}, "networkUri": {"description": "URI of a Cloud SQL instance network or empty string if the instance does not have one.", "type": "string"}, "region": {"description": "Region in which the Cloud SQL instance is running.", "type": "string"}, "uri": {"description": "URI of a Cloud SQL instance.", "type": "string"}}, "type": "object"}, "ConnectivityTest": {"description": "A Connectivity Test for a network reachability analysis.", "id": "ConnectivityTest", "properties": {"bypassFirewallChecks": {"description": "Whether the analysis should skip firewall checking. Default value is false.", "type": "boolean"}, "createTime": {"description": "Output only. The time the test was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "The user-supplied description of the Connectivity Test. Maximum of 512 characters.", "type": "string"}, "destination": {"$ref": "Endpoint", "description": "Required. Destination specification of the Connectivity Test. You can use a combination of destination IP address, URI of a supported endpoint, project ID, or VPC network to identify the destination location. Reachability analysis proceeds even if the destination location is ambiguous. However, the test result might include endpoints or use a destination that you don't intend to test."}, "displayName": {"description": "Output only. The display name of a Connectivity Test.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Resource labels to represent user-provided metadata.", "type": "object"}, "name": {"description": "Identifier. Unique name of the resource using the form: `projects/{project_id}/locations/global/connectivityTests/{test}`", "type": "string"}, "probingDetails": {"$ref": "ProbingDetails", "description": "Output only. The probing details of this test from the latest run, present for applicable tests only. The details are updated when creating a new test, updating an existing test, or triggering a one-time rerun of an existing test.", "readOnly": true}, "protocol": {"description": "IP Protocol of the test. When not provided, \"TCP\" is assumed.", "type": "string"}, "reachabilityDetails": {"$ref": "ReachabilityDetails", "description": "Output only. The reachability details of this test from the latest run. The details are updated when creating a new test, updating an existing test, or triggering a one-time rerun of an existing test.", "readOnly": true}, "relatedProjects": {"description": "Other projects that may be relevant for reachability analysis. This is applicable to scenarios where a test can cross project boundaries.", "items": {"type": "string"}, "type": "array"}, "returnReachabilityDetails": {"$ref": "ReachabilityDetails", "description": "Output only. The reachability details of this test from the latest run for the return path. The details are updated when creating a new test, updating an existing test, or triggering a one-time rerun of an existing test.", "readOnly": true}, "roundTrip": {"description": "Whether run analysis for the return path from destination to source. Default value is false.", "type": "boolean"}, "source": {"$ref": "Endpoint", "description": "Required. Source specification of the Connectivity Test. You can use a combination of source IP address, URI of a supported endpoint, project ID, or VPC network to identify the source location. Reachability analysis might proceed even if the source location is ambiguous. However, the test result might include endpoints or use a source that you don't intend to test."}, "updateTime": {"description": "Output only. The time the test's configuration was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DeliverInfo": {"description": "Details of the final state \"deliver\" and associated resource.", "id": "DeliverInfo", "properties": {"googleServiceType": {"description": "Recognized type of a Google Service the packet is delivered to (if applicable).", "enum": ["GOOGLE_SERVICE_TYPE_UNSPECIFIED", "IAP", "GFE_PROXY_OR_HEALTH_CHECK_PROBER", "CLOUD_DNS", "PRIVATE_GOOGLE_ACCESS", "SERVERLESS_VPC_ACCESS"], "enumDescriptions": ["Unspecified Google Service.", "Identity aware proxy. https://cloud.google.com/iap/docs/using-tcp-forwarding", "One of two services sharing IP ranges: * Load Balancer proxy * Centralized Health Check prober https://cloud.google.com/load-balancing/docs/firewall-rules", "Connectivity from Cloud DNS to forwarding targets or alternate name servers that use private routing. https://cloud.google.com/dns/docs/zones/forwarding-zones#firewall-rules https://cloud.google.com/dns/docs/policies#firewall-rules", "private.googleapis.com and restricted.googleapis.com", "Google API via Serverless VPC Access. https://cloud.google.com/vpc/docs/serverless-vpc-access"], "type": "string"}, "ipAddress": {"description": "IP address of the target (if applicable).", "type": "string"}, "pscGoogleApiTarget": {"description": "PSC Google API target the packet is delivered to (if applicable).", "type": "string"}, "resourceUri": {"description": "URI of the resource that the packet is delivered to.", "type": "string"}, "storageBucket": {"description": "Name of the Cloud Storage Bucket the packet is delivered to (if applicable).", "type": "string"}, "target": {"description": "Target type where the packet is delivered to.", "enum": ["TARGET_UNSPECIFIED", "INSTANCE", "INTERNET", "GOOGLE_API", "GKE_MASTER", "CLOUD_SQL_INSTANCE", "PSC_PUBLISHED_SERVICE", "PSC_GOOGLE_API", "PSC_VPC_SC", "SERVERLESS_NEG", "STORAGE_BUCKET", "PRIVATE_NETWORK", "CLOUD_FUNCTION", "APP_ENGINE_VERSION", "CLOUD_RUN_REVISION", "GOOGLE_MANAGED_SERVICE", "REDIS_INSTANCE", "REDIS_CLUSTER"], "enumDescriptions": ["Target not specified.", "Target is a Compute Engine instance.", "Target is the internet.", "Target is a Google API.", "Target is a Google Kubernetes Engine cluster master.", "Target is a Cloud SQL instance.", "Target is a published service that uses [Private Service Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-services).", "Target is Google APIs that use [Private Service Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-apis).", "Target is a VPC-SC that uses [Private Service Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-apis).", "Target is a serverless network endpoint group.", "Target is a Cloud Storage bucket.", "Target is a private network. Used only for return traces.", "Target is a Cloud Function. Used only for return traces.", "Target is a App Engine service version. Used only for return traces.", "Target is a Cloud Run revision. Used only for return traces.", "Target is a Google-managed service. Used only for return traces.", "Target is a Redis Instance.", "Target is a Redis Cluster."], "type": "string"}}, "type": "object"}, "DirectVpcEgressConnectionInfo": {"description": "For display only. Metadata associated with a serverless direct VPC egress connection.", "id": "DirectVpcEgressConnectionInfo", "properties": {"networkUri": {"description": "URI of direct access network.", "type": "string"}, "region": {"description": "Region in which the Direct VPC egress is deployed.", "type": "string"}, "selectedIpAddress": {"description": "Selected starting IP address, from the selected IP range.", "type": "string"}, "selectedIpRange": {"description": "Selected IP range.", "type": "string"}, "subnetworkUri": {"description": "URI of direct access subnetwork.", "type": "string"}}, "type": "object"}, "DropInfo": {"description": "Details of the final state \"drop\" and associated resource.", "id": "DropInfo", "properties": {"cause": {"description": "Cause that the packet is dropped.", "enum": ["CAUSE_UNSPECIFIED", "UNKNOWN_EXTERNAL_ADDRESS", "FOREIGN_IP_DISALLOWED", "FIREWALL_RULE", "NO_ROUTE", "ROUTE_BLACKHOLE", "ROUTE_WRONG_NETWORK", "ROUTE_NEXT_HOP_IP_ADDRESS_NOT_RESOLVED", "ROUTE_NEXT_HOP_RESOURCE_NOT_FOUND", "ROUTE_NEXT_HOP_INSTANCE_WRONG_NETWORK", "ROUTE_NEXT_HOP_INSTANCE_NON_PRIMARY_IP", "ROUTE_NEXT_HOP_FORWARDING_RULE_IP_MISMATCH", "ROUTE_NEXT_HOP_VPN_TUNNEL_NOT_ESTABLISHED", "ROUTE_NEXT_HOP_FORWARDING_RULE_TYPE_INVALID", "NO_ROUTE_FROM_INTERNET_TO_PRIVATE_IPV6_ADDRESS", "NO_ROUTE_FROM_EXTERNAL_IPV6_SOURCE_TO_PRIVATE_IPV6_ADDRESS", "VPN_TUNNEL_LOCAL_SELECTOR_MISMATCH", "VPN_TUNNEL_REMOTE_SELECTOR_MISMATCH", "PRIVATE_TRAFFIC_TO_INTERNET", "PRIVATE_GOOGLE_ACCESS_DISALLOWED", "PRIVATE_GOOGLE_ACCESS_VIA_VPN_TUNNEL_UNSUPPORTED", "NO_EXTERNAL_ADDRESS", "UNKNOWN_INTERNAL_ADDRESS", "FORWARDING_RULE_MISMATCH", "FORWARDING_RULE_NO_INSTANCES", "FIREWALL_BLOCKING_LOAD_BALANCER_BACKEND_HEALTH_CHECK", "INGRESS_FIREWALL_TAGS_UNSUPPORTED_BY_DIRECT_VPC_EGRESS", "INSTANCE_NOT_RUNNING", "GKE_CLUSTER_NOT_RUNNING", "CLOUD_SQL_INSTANCE_NOT_RUNNING", "REDIS_INSTANCE_NOT_RUNNING", "REDIS_CLUSTER_NOT_RUNNING", "TRAFFIC_TYPE_BLOCKED", "GKE_MASTER_UNAUTHORIZED_ACCESS", "CLOUD_SQL_INSTANCE_UNAUTHORIZED_ACCESS", "DROPPED_INSIDE_GKE_SERVICE", "DROPPED_INSIDE_CLOUD_SQL_SERVICE", "GOOGLE_MANAGED_SERVICE_NO_PEERING", "GOOGLE_MANAGED_SERVICE_NO_PSC_ENDPOINT", "GKE_PSC_ENDPOINT_MISSING", "CLOUD_SQL_INSTANCE_NO_IP_ADDRESS", "GKE_CONTROL_PLANE_REGION_MISMATCH", "PUBLIC_GKE_CONTROL_PLANE_TO_PRIVATE_DESTINATION", "GKE_CONTROL_PLANE_NO_ROUTE", "CLOUD_SQL_INSTANCE_NOT_CONFIGURED_FOR_EXTERNAL_TRAFFIC", "PUBLIC_CLOUD_SQL_INSTANCE_TO_PRIVATE_DESTINATION", "CLOUD_SQL_INSTANCE_NO_ROUTE", "CLOUD_SQL_CONNECTOR_REQUIRED", "CLOUD_FUNCTION_NOT_ACTIVE", "VPC_CONNECTOR_NOT_SET", "VPC_CONNECTOR_NOT_RUNNING", "VPC_CONNECTOR_SERVERLESS_TRAFFIC_BLOCKED", "VPC_CONNECTOR_HEALTH_CHECK_TRAFFIC_BLOCKED", "FORWARDING_RULE_REGION_MISMATCH", "PSC_CONNECTION_NOT_ACCEPTED", "PSC_ENDPOINT_ACCESSED_FROM_PEERED_NETWORK", "PSC_NEG_PRODUCER_ENDPOINT_NO_GLOBAL_ACCESS", "PSC_NEG_PRODUCER_FORWARDING_RULE_MULTIPLE_PORTS", "CLOUD_SQL_PSC_NEG_UNSUPPORTED", "NO_NAT_SUBNETS_FOR_PSC_SERVICE_ATTACHMENT", "PSC_TRANSITIVITY_NOT_PROPAGATED", "HYBRID_NEG_NON_DYNAMIC_ROUTE_MATCHED", "HYBRID_NEG_NON_LOCAL_DYNAMIC_ROUTE_MATCHED", "CLOUD_RUN_REVISION_NOT_READY", "DROPPED_INSIDE_PSC_SERVICE_PRODUCER", "LOAD_BALANCER_HAS_NO_PROXY_SUBNET", "CLOUD_NAT_NO_ADDRESSES", "ROUTING_LOOP", "DROPPED_INSIDE_GOOGLE_MANAGED_SERVICE", "LOAD_BALANCER_BACKEND_INVALID_NETWORK", "BACKEND_SERVICE_NAMED_PORT_NOT_DEFINED", "DESTINATION_IS_PRIVATE_NAT_IP_RANGE", "DROPPED_INSIDE_REDIS_INSTANCE_SERVICE", "REDIS_INSTANCE_UNSUPPORTED_PORT", "REDIS_INSTANCE_CONNECTING_FROM_PUPI_ADDRESS", "REDIS_INSTANCE_NO_ROUTE_TO_DESTINATION_NETWORK", "REDIS_INSTANCE_NO_EXTERNAL_IP", "REDIS_INSTANCE_UNSUPPORTED_PROTOCOL", "DROPPED_INSIDE_REDIS_CLUSTER_SERVICE", "REDIS_CLUSTER_UNSUPPORTED_PORT", "REDIS_CLUSTER_NO_EXTERNAL_IP", "REDIS_CLUSTER_UNSUPPORTED_PROTOCOL", "NO_ADVERTISED_ROUTE_TO_GCP_DESTINATION", "NO_TRAFFIC_SELECTOR_TO_GCP_DESTINATION", "NO_KNOWN_ROUTE_FROM_PEERED_NETWORK_TO_DESTINATION", "PRIVATE_NAT_TO_PSC_ENDPOINT_UNSUPPORTED", "PSC_PORT_MAPPING_PORT_MISMATCH", "PSC_PORT_MAPPING_WITHOUT_PSC_CONNECTION_UNSUPPORTED", "UNSUPPORTED_ROUTE_MATCHED_FOR_NAT64_DESTINATION", "TRAFFIC_FROM_HYBRID_ENDPOINT_TO_INTERNET_DISALLOWED", "NO_MATCHING_NAT64_GATEWAY", "LOAD_BALANCER_BACKEND_IP_VERSION_MISMATCH", "NO_KNOWN_ROUTE_FROM_NCC_NETWORK_TO_DESTINATION"], "enumDescriptions": ["Cause is unspecified.", "Destination external address cannot be resolved to a known target. If the address is used in a Google Cloud project, provide the project ID as test input.", "A Compute Engine instance can only send or receive a packet with a foreign IP address if ip_forward is enabled.", "Dropped due to a firewall rule, unless allowed due to connection tracking.", "Dropped due to no matching routes.", "Dropped due to invalid route. Route's next hop is a blackhole.", "Packet is sent to a wrong (unintended) network. Example: you trace a packet from VM1:Network1 to VM2:Network2, however, the route configured in Network1 sends the packet destined for VM2's IP address to Network3.", "Route's next hop IP address cannot be resolved to a GCP resource.", "Route's next hop resource is not found.", "Route's next hop instance doesn't have a NIC in the route's network.", "Route's next hop IP address is not a primary IP address of the next hop instance.", "Route's next hop forwarding rule doesn't match next hop IP address.", "Route's next hop VPN tunnel is down (does not have valid IKE SAs).", "Route's next hop forwarding rule type is invalid (it's not a forwarding rule of the internal passthrough load balancer).", "Packet is sent from the Internet or Google service to the private IPv6 address.", "Packet is sent from the external IPv6 source address of an instance to the private IPv6 address of an instance.", "The packet does not match a policy-based VPN tunnel local selector.", "The packet does not match a policy-based VPN tunnel remote selector.", "Packet with internal destination address sent to the internet gateway.", "Endpoint with only an internal IP address tries to access Google API and services, but Private Google Access is not enabled in the subnet or is not applicable.", "Source endpoint tries to access Google API and services through the VPN tunnel to another network, but Private Google Access needs to be enabled in the source endpoint network.", "Endpoint with only an internal IP address tries to access external hosts, but there is no matching Cloud NAT gateway in the subnet.", "Destination internal address cannot be resolved to a known target. If this is a shared VPC scenario, verify if the service project ID is provided as test input. Otherwise, verify if the IP address is being used in the project.", "Forwarding rule's protocol and ports do not match the packet header.", "Forwarding rule does not have backends configured.", "Firewalls block the health check probes to the backends and cause the backends to be unavailable for traffic from the load balancer. For more details, see [Health check firewall rules](https://cloud.google.com/load-balancing/docs/health-checks#firewall_rules).", "Matching ingress firewall rules by network tags for packets sent via serverless VPC direct egress is unsupported. Behavior is undefined. https://cloud.google.com/run/docs/configuring/vpc-direct-vpc#limitations", "Packet is sent from or to a Compute Engine instance that is not in a running state.", "Packet sent from or to a GKE cluster that is not in running state.", "Packet sent from or to a Cloud SQL instance that is not in running state.", "Packet sent from or to a Redis Instance that is not in running state.", "Packet sent from or to a Redis Cluster that is not in running state.", "The type of traffic is blocked and the user cannot configure a firewall rule to enable it. See [Always blocked traffic](https://cloud.google.com/vpc/docs/firewalls#blockedtraffic) for more details.", "Access to Google Kubernetes Engine cluster master's endpoint is not authorized. See [Access to the cluster endpoints](https://cloud.google.com/kubernetes-engine/docs/how-to/private-clusters#access_to_the_cluster_endpoints) for more details.", "Access to the Cloud SQL instance endpoint is not authorized. See [Authorizing with authorized networks](https://cloud.google.com/sql/docs/mysql/authorize-networks) for more details.", "Packet was dropped inside Google Kubernetes Engine Service.", "Packet was dropped inside Cloud SQL Service.", "Packet was dropped because there is no peering between the originating network and the Google Managed Services Network.", "Packet was dropped because the Google-managed service uses Private Service Connect (PSC), but the PSC endpoint is not found in the project.", "Packet was dropped because the GKE cluster uses Private Service Connect (PSC), but the PSC endpoint is not found in the project.", "Packet was dropped because the Cloud SQL instance has neither a private nor a public IP address.", "Packet was dropped because a GKE cluster private endpoint is unreachable from a region different from the cluster's region.", "Packet sent from a public GKE cluster control plane to a private IP address.", "Packet was dropped because there is no route from a GKE cluster control plane to a destination network.", "Packet sent from a Cloud SQL instance to an external IP address is not allowed. The Cloud SQL instance is not configured to send packets to external IP addresses.", "Packet sent from a Cloud SQL instance with only a public IP address to a private IP address.", "Packet was dropped because there is no route from a Cloud SQL instance to a destination network.", "Packet was dropped because the Cloud SQL instance requires all connections to use Cloud SQL connectors and to target the Cloud SQL proxy port (3307).", "Packet could be dropped because the Cloud Function is not in an active status.", "Packet could be dropped because no VPC connector is set.", "Packet could be dropped because the VPC connector is not in a running state.", "Packet could be dropped because the traffic from the serverless service to the VPC connector is not allowed.", "Packet could be dropped because the health check traffic to the VPC connector is not allowed.", "Packet could be dropped because it was sent from a different region to a regional forwarding without global access.", "The Private Service Connect endpoint is in a project that is not approved to connect to the service.", "The packet is sent to the Private Service Connect endpoint over the peering, but [it's not supported](https://cloud.google.com/vpc/docs/configure-private-service-connect-services#on-premises).", "The packet is sent to the Private Service Connect backend (network endpoint group), but the producer PSC forwarding rule does not have global access enabled.", "The packet is sent to the Private Service Connect backend (network endpoint group), but the producer PSC forwarding rule has multiple ports specified.", "The packet is sent to the Private Service Connect backend (network endpoint group) targeting a Cloud SQL service attachment, but this configuration is not supported.", "No NAT subnets are defined for the PSC service attachment.", "PSC endpoint is accessed via NCC, but PSC transitivity configuration is not yet propagated.", "The packet sent from the hybrid NEG proxy matches a non-dynamic route, but such a configuration is not supported.", "The packet sent from the hybrid NEG proxy matches a dynamic route with a next hop in a different region, but such a configuration is not supported.", "Packet sent from a Cloud Run revision that is not ready.", "Packet was dropped inside Private Service Connect service producer.", "Packet sent to a load balancer, which requires a proxy-only subnet and the subnet is not found.", "Packet sent to Cloud Nat without active NAT IPs.", "Packet is stuck in a routing loop.", "Packet is dropped inside a Google-managed service due to being delivered in return trace to an endpoint that doesn't match the endpoint the packet was sent from in forward trace. Used only for return traces.", "Packet is dropped due to a load balancer backend instance not having a network interface in the network expected by the load balancer.", "Packet is dropped due to a backend service named port not being defined on the instance group level.", "Packet is dropped due to a destination IP range being part of a Private NAT IP range.", "Generic drop cause for a packet being dropped inside a Redis Instance service project.", "Packet is dropped due to an unsupported port being used to connect to a Redis Instance. Port 6379 should be used to connect to a Redis Instance.", "Packet is dropped due to connecting from PUPI address to a PSA based Redis Instance.", "Packet is dropped due to no route to the destination network.", "Redis Instance does not have an external IP address.", "Packet is dropped due to an unsupported protocol being used to connect to a Redis Instance. Only TCP connections are accepted by a Redis Instance.", "Generic drop cause for a packet being dropped inside a Redis Cluster service project.", "Packet is dropped due to an unsupported port being used to connect to a Redis Cluster. Ports 6379 and 11000 to 13047 should be used to connect to a Redis Cluster.", "Redis Cluster does not have an external IP address.", "Packet is dropped due to an unsupported protocol being used to connect to a Redis Cluster. Only TCP connections are accepted by a Redis Cluster.", "Packet from the non-GCP (on-prem) or unknown GCP network is dropped due to the destination IP address not belonging to any IP prefix advertised via BGP by the Cloud Router.", "Packet from the non-GCP (on-prem) or unknown GCP network is dropped due to the destination IP address not belonging to any IP prefix included to the local traffic selector of the VPN tunnel.", "Packet from the unknown peered network is dropped due to no known route from the source network to the destination IP address.", "Sending packets processed by the Private NAT Gateways to the Private Service Connect endpoints is not supported.", "Packet is sent to the PSC port mapping service, but its destination port does not match any port mapping rules.", "Sending packets directly to the PSC port mapping service without going through the PSC connection is not supported.", "Packet with destination IP address within the reserved NAT64 range is dropped due to matching a route of an unsupported type.", "Packet could be dropped because hybrid endpoint like a VPN gateway or Interconnect is not allowed to send traffic to the Internet.", "Packet with destination IP address within the reserved NAT64 range is dropped due to no matching NAT gateway in the subnet.", "Packet is dropped due to being sent to a backend of a passthrough load balancer that doesn't use the same IP version as the frontend.", "Packet from the unknown NCC network is dropped due to no known route from the source network to the destination IP address."], "type": "string"}, "destinationIp": {"description": "Destination IP address of the dropped packet (if relevant).", "type": "string"}, "region": {"description": "Region of the dropped packet (if relevant).", "type": "string"}, "resourceUri": {"description": "URI of the resource that caused the drop.", "type": "string"}, "sourceIp": {"description": "Source IP address of the dropped packet (if relevant).", "type": "string"}}, "type": "object"}, "EdgeLocation": {"description": "Representation of a network edge location as per https://cloud.google.com/vpc/docs/edge-locations.", "id": "EdgeLocation", "properties": {"metropolitanArea": {"description": "Name of the metropolitan area.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Endpoint": {"description": "Source or destination of the Connectivity Test.", "id": "Endpoint", "properties": {"alloyDbInstance": {"description": "An [AlloyDB Instance](https://cloud.google.com/alloydb) URI.", "type": "string"}, "appEngineVersion": {"$ref": "AppEngineVersionEndpoint", "description": "An [App Engine](https://cloud.google.com/appengine) [service version](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1/apps.services.versions). Applicable only to source endpoint."}, "cloudFunction": {"$ref": "CloudFunctionEndpoint", "description": "A [Cloud Function](https://cloud.google.com/functions). Applicable only to source endpoint."}, "cloudRunRevision": {"$ref": "CloudRunRevisionEndpoint", "description": "A [Cloud Run](https://cloud.google.com/run) [revision](https://cloud.google.com/run/docs/reference/rest/v1/namespaces.revisions/get) Applicable only to source endpoint."}, "cloudSqlInstance": {"description": "A [Cloud SQL](https://cloud.google.com/sql) instance URI.", "type": "string"}, "forwardingRule": {"description": "A forwarding rule and its corresponding IP address represent the frontend configuration of a Google Cloud load balancer. Forwarding rules are also used for protocol forwarding, Private Service Connect and other network services to provide forwarding information in the control plane. Applicable only to destination endpoint. Format: projects/{project}/global/forwardingRules/{id} or projects/{project}/regions/{region}/forwardingRules/{id}", "type": "string"}, "forwardingRuleTarget": {"description": "Output only. Specifies the type of the target of the forwarding rule.", "enum": ["FORWARDING_RULE_TARGET_UNSPECIFIED", "INSTANCE", "LOAD_BALANCER", "VPN_GATEWAY", "PSC"], "enumDescriptions": ["Forwarding rule target is unknown.", "Compute Engine instance for protocol forwarding.", "Load Balancer. The specific type can be found from load_balancer_type.", "Classic Cloud VPN Gateway.", "Forwarding Rule is a Private Service Connect endpoint."], "readOnly": true, "type": "string"}, "fqdn": {"description": "DNS endpoint of [Google Kubernetes Engine cluster control plane](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture). Requires gke_master_cluster to be set, can't be used simultaneoulsly with ip_address or network. Applicable only to destination endpoint.", "type": "string"}, "gkeMasterCluster": {"description": "A cluster URI for [Google Kubernetes Engine cluster control plane](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture).", "type": "string"}, "instance": {"description": "A Compute Engine instance URI.", "type": "string"}, "ipAddress": {"description": "The IP address of the endpoint, which can be an external or internal IP.", "type": "string"}, "loadBalancerId": {"description": "Output only. ID of the load balancer the forwarding rule points to. Empty for forwarding rules not related to load balancers.", "readOnly": true, "type": "string"}, "loadBalancerType": {"description": "Output only. Type of the load balancer the forwarding rule points to.", "enum": ["LOAD_BALANCER_TYPE_UNSPECIFIED", "HTTPS_ADVANCED_LOAD_BALANCER", "HTTPS_LOAD_BALANCER", "REGIONAL_HTTPS_LOAD_BALANCER", "INTERNAL_HTTPS_LOAD_BALANCER", "SSL_PROXY_LOAD_BALANCER", "TCP_PROXY_LOAD_BALANCER", "INTERNAL_TCP_PROXY_LOAD_BALANCER", "NETWORK_LOAD_BALANCER", "LEGACY_NETWORK_LOAD_BALANCER", "TCP_UDP_INTERNAL_LOAD_BALANCER"], "enumDescriptions": ["Forwarding rule points to a different target than a load balancer or a load balancer type is unknown.", "Global external HTTP(S) load balancer.", "Global external HTTP(S) load balancer (classic)", "Regional external HTTP(S) load balancer.", "Internal HTTP(S) load balancer.", "External SSL proxy load balancer.", "External TCP proxy load balancer.", "Internal regional TCP proxy load balancer.", "External TCP/UDP Network load balancer.", "Target-pool based external TCP/UDP Network load balancer.", "Internal TCP/UDP load balancer."], "readOnly": true, "type": "string"}, "network": {"description": "A VPC network URI.", "type": "string"}, "networkType": {"description": "Type of the network where the endpoint is located. Applicable only to source endpoint, as destination network type can be inferred from the source.", "enum": ["NETWORK_TYPE_UNSPECIFIED", "GCP_NETWORK", "NON_GCP_NETWORK"], "enumDescriptions": ["Default type if unspecified.", "A network hosted within Google Cloud. To receive more detailed output, specify the URI for the source or destination network.", "A network hosted outside of Google Cloud. This can be an on-premises network, an internet resource or a network hosted by another cloud provider."], "type": "string"}, "port": {"description": "The IP protocol port of the endpoint. Only applicable when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "projectId": {"description": "Project ID where the endpoint is located. The project ID can be derived from the URI if you provide a endpoint or network URI. The following are two cases where you may need to provide the project ID: 1. Only the IP address is specified, and the IP address is within a Google Cloud project. 2. When you are using Shared VPC and the IP address that you provide is from the service project. In this case, the network that the IP address resides in is defined in the host project.", "type": "string"}, "redisCluster": {"description": "A [Redis Cluster](https://cloud.google.com/memorystore/docs/cluster) URI. Applicable only to destination endpoint.", "type": "string"}, "redisInstance": {"description": "A [Redis Instance](https://cloud.google.com/memorystore/docs/redis) URI. Applicable only to destination endpoint.", "type": "string"}}, "type": "object"}, "EndpointInfo": {"description": "For display only. The specification of the endpoints for the test. EndpointInfo is derived from source and destination Endpoint and validated by the backend data plane model.", "id": "EndpointInfo", "properties": {"destinationIp": {"description": "Destination IP address.", "type": "string"}, "destinationNetworkUri": {"description": "URI of the network where this packet is sent to.", "type": "string"}, "destinationPort": {"description": "Destination port. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "protocol": {"description": "IP protocol in string format, for example: \"TCP\", \"UDP\", \"ICMP\".", "type": "string"}, "sourceAgentUri": {"description": "URI of the source telemetry agent this packet originates from.", "type": "string"}, "sourceIp": {"description": "Source IP address.", "type": "string"}, "sourceNetworkUri": {"description": "URI of the network where this packet originates from.", "type": "string"}, "sourcePort": {"description": "Source port. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FirewallInfo": {"description": "For display only. Metadata associated with a VPC firewall rule, an implied VPC firewall rule, or a firewall policy rule.", "id": "FirewallInfo", "properties": {"action": {"description": "Possible values: <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>, APPLY_SECURITY_PROFILE_GROUP", "type": "string"}, "direction": {"description": "Possible values: INGRESS, EGRESS", "type": "string"}, "displayName": {"description": "The display name of the firewall rule. This field might be empty for firewall policy rules.", "type": "string"}, "firewallRuleType": {"description": "The firewall rule's type.", "enum": ["FIREWALL_RULE_TYPE_UNSPECIFIED", "HIERARCHICAL_FIREWALL_POLICY_RULE", "VPC_FIREWALL_RULE", "IMPLIED_VPC_FIREWALL_RULE", "SERVERLESS_VPC_ACCESS_MANAGED_FIREWALL_RULE", "NETWORK_FIREWALL_POLICY_RULE", "NETWORK_REGIONAL_FIREWALL_POLICY_RULE", "UNSUPPORTED_FIREWALL_POLICY_RULE", "TRACKING_STATE", "ANALYSIS_SKIPPED"], "enumDescriptions": ["Unspecified type.", "Hierarchical firewall policy rule. For details, see [Hierarchical firewall policies overview](https://cloud.google.com/vpc/docs/firewall-policies).", "VPC firewall rule. For details, see [VPC firewall rules overview](https://cloud.google.com/vpc/docs/firewalls).", "Implied VPC firewall rule. For details, see [Implied rules](https://cloud.google.com/vpc/docs/firewalls#default_firewall_rules).", "Implicit firewall rules that are managed by serverless VPC access to allow ingress access. They are not visible in the Google Cloud console. For details, see [VPC connector's implicit rules](https://cloud.google.com/functions/docs/networking/connecting-vpc#restrict-access).", "Global network firewall policy rule. For details, see [Network firewall policies](https://cloud.google.com/vpc/docs/network-firewall-policies).", "Regional network firewall policy rule. For details, see [Regional network firewall policies](https://cloud.google.com/firewall/docs/regional-firewall-policies).", "Firewall policy rule containing attributes not yet supported in Connectivity tests. Firewall analysis is skipped if such a rule can potentially be matched. Please see the [list of unsupported configurations](https://cloud.google.com/network-intelligence-center/docs/connectivity-tests/concepts/overview#unsupported-configs).", "Tracking state for response traffic created when request traffic goes through allow firewall rule. For details, see [firewall rules specifications](https://cloud.google.com/firewall/docs/firewalls#specifications)", "Firewall analysis was skipped due to executing Connectivity Test in the BypassFirewallChecks mode"], "type": "string"}, "networkUri": {"description": "The URI of the VPC network that the firewall rule is associated with. This field is not applicable to hierarchical firewall policy rules.", "type": "string"}, "policy": {"description": "The name of the firewall policy that this rule is associated with. This field is not applicable to VPC firewall rules and implied VPC firewall rules.", "type": "string"}, "policyPriority": {"description": "The priority of the firewall policy that this rule is associated with. This field is not applicable to VPC firewall rules and implied VPC firewall rules.", "format": "int32", "type": "integer"}, "policyUri": {"description": "The URI of the firewall policy that this rule is associated with. This field is not applicable to VPC firewall rules and implied VPC firewall rules.", "type": "string"}, "priority": {"description": "The priority of the firewall rule.", "format": "int32", "type": "integer"}, "targetServiceAccounts": {"description": "The target service accounts specified by the firewall rule.", "items": {"type": "string"}, "type": "array"}, "targetTags": {"description": "The target tags defined by the VPC firewall rule. This field is not applicable to firewall policy rules.", "items": {"type": "string"}, "type": "array"}, "uri": {"description": "The URI of the firewall rule. This field is not applicable to implied VPC firewall rules.", "type": "string"}}, "type": "object"}, "ForwardInfo": {"description": "Details of the final state \"forward\" and associated resource.", "id": "ForwardInfo", "properties": {"ipAddress": {"description": "IP address of the target (if applicable).", "type": "string"}, "resourceUri": {"description": "URI of the resource that the packet is forwarded to.", "type": "string"}, "target": {"description": "Target type where this packet is forwarded to.", "enum": ["TARGET_UNSPECIFIED", "PEERING_VPC", "VPN_GATEWAY", "INTERCONNECT", "GKE_MASTER", "IMPORTED_CUSTOM_ROUTE_NEXT_HOP", "CLOUD_SQL_INSTANCE", "ANOTHER_PROJECT", "NCC_HUB", "ROUTER_APPLIANCE", "SECURE_WEB_PROXY_GATEWAY"], "enumDeprecated": [false, false, false, false, true, false, true, false, false, false, false], "enumDescriptions": ["Target not specified.", "Forwarded to a VPC peering network.", "Forwarded to a Cloud VPN gateway.", "Forwarded to a Cloud Interconnect connection.", "Forwarded to a Google Kubernetes Engine Container cluster master.", "Forwarded to the next hop of a custom route imported from a peering VPC.", "Forwarded to a Cloud SQL instance.", "Forwarded to a VPC network in another project.", "Forwarded to an NCC Hub.", "Forwarded to a router appliance.", "Forwarded to a Secure Web Proxy Gateway."], "type": "string"}}, "type": "object"}, "ForwardingRuleInfo": {"description": "For display only. Metadata associated with a Compute Engine forwarding rule.", "id": "ForwardingRuleInfo", "properties": {"displayName": {"description": "Name of the forwarding rule.", "type": "string"}, "loadBalancerName": {"description": "Name of the load balancer the forwarding rule belongs to. Empty for forwarding rules not related to load balancers (like PSC forwarding rules).", "type": "string"}, "matchedPortRange": {"description": "Port range defined in the forwarding rule that matches the packet.", "type": "string"}, "matchedProtocol": {"description": "Protocol defined in the forwarding rule that matches the packet.", "type": "string"}, "networkUri": {"description": "Network URI.", "type": "string"}, "pscGoogleApiTarget": {"description": "PSC Google API target this forwarding rule targets (if applicable).", "type": "string"}, "pscServiceAttachmentUri": {"description": "URI of the PSC service attachment this forwarding rule targets (if applicable).", "type": "string"}, "region": {"description": "Region of the forwarding rule. Set only for regional forwarding rules.", "type": "string"}, "target": {"description": "Target type of the forwarding rule.", "type": "string"}, "uri": {"description": "URI of the forwarding rule.", "type": "string"}, "vip": {"description": "VIP of the forwarding rule.", "type": "string"}}, "type": "object"}, "GKEMasterInfo": {"description": "For display only. Metadata associated with a Google Kubernetes Engine (GKE) cluster master.", "id": "GKEMasterInfo", "properties": {"clusterNetworkUri": {"description": "URI of a GKE cluster network.", "type": "string"}, "clusterUri": {"description": "URI of a GKE cluster.", "type": "string"}, "dnsEndpoint": {"description": "DNS endpoint of a GKE cluster control plane.", "type": "string"}, "externalIp": {"description": "External IP address of a GKE cluster control plane.", "type": "string"}, "internalIp": {"description": "Internal IP address of a GKE cluster control plane.", "type": "string"}}, "type": "object"}, "GoogleServiceInfo": {"description": "For display only. Details of a Google Service sending packets to a VPC network. Although the source IP might be a publicly routable address, some Google Services use special routes within Google production infrastructure to reach Compute Engine Instances. https://cloud.google.com/vpc/docs/routes#special_return_paths", "id": "GoogleServiceInfo", "properties": {"googleServiceType": {"description": "Recognized type of a Google Service.", "enum": ["GOOGLE_SERVICE_TYPE_UNSPECIFIED", "IAP", "GFE_PROXY_OR_HEALTH_CHECK_PROBER", "CLOUD_DNS", "GOOGLE_API", "GOOGLE_API_PSC", "GOOGLE_API_VPC_SC", "SERVERLESS_VPC_ACCESS"], "enumDescriptions": ["Unspecified Google Service.", "Identity aware proxy. https://cloud.google.com/iap/docs/using-tcp-forwarding", "One of two services sharing IP ranges: * Load Balancer proxy * Centralized Health Check prober https://cloud.google.com/load-balancing/docs/firewall-rules", "Connectivity from Cloud DNS to forwarding targets or alternate name servers that use private routing. https://cloud.google.com/dns/docs/zones/forwarding-zones#firewall-rules https://cloud.google.com/dns/docs/policies#firewall-rules", "private.googleapis.com and restricted.googleapis.com", "Google API via Private Service Connect. https://cloud.google.com/vpc/docs/configure-private-service-connect-apis", "Google API via VPC Service Controls. https://cloud.google.com/vpc/docs/configure-private-service-connect-apis", "Google API via Serverless VPC Access. https://cloud.google.com/vpc/docs/serverless-vpc-access"], "type": "string"}, "sourceIp": {"description": "Source IP address.", "type": "string"}}, "type": "object"}, "InstanceInfo": {"description": "For display only. Metadata associated with a Compute Engine instance.", "id": "InstanceInfo", "properties": {"displayName": {"description": "Name of a Compute Engine instance.", "type": "string"}, "externalIp": {"description": "External IP address of the network interface.", "type": "string"}, "interface": {"description": "Name of the network interface of a Compute Engine instance.", "type": "string"}, "internalIp": {"description": "Internal IP address of the network interface.", "type": "string"}, "networkTags": {"description": "Network tags configured on the instance.", "items": {"type": "string"}, "type": "array"}, "networkUri": {"description": "URI of a Compute Engine network.", "type": "string"}, "pscNetworkAttachmentUri": {"description": "URI of the PSC network attachment the NIC is attached to (if relevant).", "type": "string"}, "running": {"deprecated": true, "description": "Indicates whether the Compute Engine instance is running. Deprecated: use the `status` field instead.", "type": "boolean"}, "serviceAccount": {"deprecated": true, "description": "Service account authorized for the instance.", "type": "string"}, "status": {"description": "The status of the instance.", "enum": ["STATUS_UNSPECIFIED", "RUNNING", "NOT_RUNNING"], "enumDescriptions": ["Default unspecified value.", "The instance is running.", "The instance has any status other than \"RUNNING\"."], "type": "string"}, "uri": {"description": "URI of a Compute Engine instance.", "type": "string"}}, "type": "object"}, "LatencyDistribution": {"description": "Describes measured latency distribution.", "id": "LatencyDistribution", "properties": {"latencyPercentiles": {"description": "Representative latency percentiles.", "items": {"$ref": "LatencyPercentile"}, "type": "array"}}, "type": "object"}, "LatencyPercentile": {"description": "Latency percentile rank and value.", "id": "LatencyPercentile", "properties": {"latencyMicros": {"description": "percent-th percentile of latency observed, in microseconds. Fraction of percent/100 of samples have latency lower or equal to the value of this field.", "format": "int64", "type": "string"}, "percent": {"description": "Percentage of samples this data point applies to.", "format": "int32", "type": "integer"}}, "type": "object"}, "ListConnectivityTestsResponse": {"description": "Response for the `ListConnectivityTests` method.", "id": "ListConnectivityTestsResponse", "properties": {"nextPageToken": {"description": "Page token to fetch the next set of Connectivity Tests.", "type": "string"}, "resources": {"description": "List of Connectivity Tests.", "items": {"$ref": "ConnectivityTest"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached (when querying all locations with `-`).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListVpcFlowLogsConfigsResponse": {"description": "Response for the `ListVpcFlowLogsConfigs` method.", "id": "ListVpcFlowLogsConfigsResponse", "properties": {"nextPageToken": {"description": "Page token to fetch the next set of configurations.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached (when querying all locations with `-`).", "items": {"type": "string"}, "type": "array"}, "vpcFlowLogsConfigs": {"description": "List of VPC Flow Log configurations.", "items": {"$ref": "VpcFlowLogsConfig"}, "type": "array"}}, "type": "object"}, "LoadBalancerBackend": {"description": "For display only. Metadata associated with a specific load balancer backend.", "id": "LoadBalancerBackend", "properties": {"displayName": {"description": "Name of a Compute Engine instance or network endpoint.", "type": "string"}, "healthCheckAllowingFirewallRules": {"description": "A list of firewall rule URIs allowing probes from health check IP ranges.", "items": {"type": "string"}, "type": "array"}, "healthCheckBlockingFirewallRules": {"description": "A list of firewall rule URIs blocking probes from health check IP ranges.", "items": {"type": "string"}, "type": "array"}, "healthCheckFirewallState": {"description": "State of the health check firewall configuration.", "enum": ["HEALTH_CHECK_FIREWALL_STATE_UNSPECIFIED", "CONFIGURED", "MISCONFIGURED"], "enumDescriptions": ["State is unspecified. Default state if not populated.", "There are configured firewall rules to allow health check probes to the backend.", "There are firewall rules configured to allow partial health check ranges or block all health check ranges. If a health check probe is sent from denied IP ranges, the health check to the backend will fail. Then, the backend will be marked unhealthy and will not receive traffic sent to the load balancer."], "type": "string"}, "uri": {"description": "URI of a Compute Engine instance or network endpoint.", "type": "string"}}, "type": "object"}, "LoadBalancerBackendInfo": {"description": "For display only. Metadata associated with the load balancer backend.", "id": "LoadBalancerBackendInfo", "properties": {"backendBucketUri": {"description": "URI of the backend bucket this backend targets (if applicable).", "type": "string"}, "backendServiceUri": {"description": "URI of the backend service this backend belongs to (if applicable).", "type": "string"}, "healthCheckFirewallsConfigState": {"description": "Output only. Health check firewalls configuration state for the backend. This is a result of the static firewall analysis (verifying that health check traffic from required IP ranges to the backend is allowed or not). The backend might still be unhealthy even if these firewalls are configured. Please refer to the documentation for more information: https://cloud.google.com/load-balancing/docs/firewall-rules", "enum": ["HEALTH_CHECK_FIREWALLS_CONFIG_STATE_UNSPECIFIED", "FIREWALLS_CONFIGURED", "FIREWALLS_PARTIALLY_CONFIGURED", "FIREWALLS_NOT_CONFIGURED", "FIREWALLS_UNSUPPORTED"], "enumDescriptions": ["Configuration state unspecified. It usually means that the backend has no health check attached, or there was an unexpected configuration error preventing Connectivity tests from verifying health check configuration.", "Firewall rules (policies) allowing health check traffic from all required IP ranges to the backend are configured.", "Firewall rules (policies) allow health check traffic only from a part of required IP ranges.", "Firewall rules (policies) deny health check traffic from all required IP ranges to the backend.", "The network contains firewall rules of unsupported types, so Connectivity tests were not able to verify health check configuration status. Please refer to the documentation for the list of unsupported configurations: https://cloud.google.com/network-intelligence-center/docs/connectivity-tests/concepts/overview#unsupported-configs"], "readOnly": true, "type": "string"}, "healthCheckUri": {"description": "URI of the health check attached to this backend (if applicable).", "type": "string"}, "instanceGroupUri": {"description": "URI of the instance group this backend belongs to (if applicable).", "type": "string"}, "instanceUri": {"description": "URI of the backend instance (if applicable). Populated for instance group backends, and zonal NEG backends.", "type": "string"}, "name": {"description": "Display name of the backend. For example, it might be an instance name for the instance group backends, or an IP address and port for zonal network endpoint group backends.", "type": "string"}, "networkEndpointGroupUri": {"description": "URI of the network endpoint group this backend belongs to (if applicable).", "type": "string"}, "pscGoogleApiTarget": {"description": "PSC Google API target this PSC NEG backend targets (if applicable).", "type": "string"}, "pscServiceAttachmentUri": {"description": "URI of the PSC service attachment this PSC NEG backend targets (if applicable).", "type": "string"}}, "type": "object"}, "LoadBalancerInfo": {"description": "For display only. Metadata associated with a load balancer.", "id": "LoadBalancerInfo", "properties": {"backendType": {"description": "Type of load balancer's backend configuration.", "enum": ["BACKEND_TYPE_UNSPECIFIED", "BACKEND_SERVICE", "TARGET_POOL", "TARGET_INSTANCE"], "enumDescriptions": ["Type is unspecified.", "Backend Service as the load balancer's backend.", "Target Pool as the load balancer's backend.", "Target Instance as the load balancer's backend."], "type": "string"}, "backendUri": {"description": "Backend configuration URI.", "type": "string"}, "backends": {"description": "Information for the loadbalancer backends.", "items": {"$ref": "LoadBalancerBackend"}, "type": "array"}, "healthCheckUri": {"deprecated": true, "description": "URI of the health check for the load balancer. Deprecated and no longer populated as different load balancer backends might have different health checks.", "type": "string"}, "loadBalancerType": {"description": "Type of the load balancer.", "enum": ["LOAD_BALANCER_TYPE_UNSPECIFIED", "INTERNAL_TCP_UDP", "NETWORK_TCP_UDP", "HTTP_PROXY", "TCP_PROXY", "SSL_PROXY"], "enumDescriptions": ["Type is unspecified.", "Internal TCP/UDP load balancer.", "Network TCP/UDP load balancer.", "HTTP(S) proxy load balancer.", "TCP proxy load balancer.", "SSL proxy load balancer."], "type": "string"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "NatInfo": {"description": "For display only. Metadata associated with NAT.", "id": "NatInfo", "properties": {"natGatewayName": {"description": "The name of Cloud NAT Gateway. Only valid when type is CLOUD_NAT.", "type": "string"}, "networkUri": {"description": "URI of the network where NAT translation takes place.", "type": "string"}, "newDestinationIp": {"description": "Destination IP address after NAT translation.", "type": "string"}, "newDestinationPort": {"description": "Destination port after NAT translation. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "newSourceIp": {"description": "Source IP address after NAT translation.", "type": "string"}, "newSourcePort": {"description": "Source port after NAT translation. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "oldDestinationIp": {"description": "Destination IP address before NAT translation.", "type": "string"}, "oldDestinationPort": {"description": "Destination port before NAT translation. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "oldSourceIp": {"description": "Source IP address before NAT translation.", "type": "string"}, "oldSourcePort": {"description": "Source port before NAT translation. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "protocol": {"description": "IP protocol in string format, for example: \"TCP\", \"UDP\", \"ICMP\".", "type": "string"}, "routerUri": {"description": "Uri of the Cloud Router. Only valid when type is CLOUD_NAT.", "type": "string"}, "type": {"description": "Type of NAT.", "enum": ["TYPE_UNSPECIFIED", "INTERNAL_TO_EXTERNAL", "EXTERNAL_TO_INTERNAL", "CLOUD_NAT", "PRIVATE_SERVICE_CONNECT"], "enumDescriptions": ["Type is unspecified.", "From Compute Engine instance's internal address to external address.", "From Compute Engine instance's external address to internal address.", "Cloud NAT Gateway.", "Private service connect NAT."], "type": "string"}}, "type": "object"}, "NetworkInfo": {"description": "For display only. Metadata associated with a Compute Engine network.", "id": "NetworkInfo", "properties": {"displayName": {"description": "Name of a Compute Engine network.", "type": "string"}, "matchedIpRange": {"description": "The IP range of the subnet matching the source IP address of the test.", "type": "string"}, "matchedSubnetUri": {"description": "URI of the subnet matching the source IP address of the test.", "type": "string"}, "region": {"description": "The region of the subnet matching the source IP address of the test.", "type": "string"}, "uri": {"description": "URI of a Compute Engine network.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Metadata describing an Operation", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "API version.", "type": "string"}, "cancelRequested": {"description": "Specifies if cancellation was requested for the operation.", "type": "boolean"}, "createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "statusDetail": {"description": "Human-readable status of the operation, if any.", "type": "string"}, "target": {"description": "Target of the operation - for example projects/project-1/locations/global/connectivityTests/test-1", "type": "string"}, "verb": {"description": "Name of the verb executed by the operation.", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "ProbingDetails": {"description": "Results of active probing from the last run of the test.", "id": "ProbingDetails", "properties": {"abortCause": {"description": "The reason probing was aborted.", "enum": ["PROBING_ABORT_CAUSE_UNSPECIFIED", "PERMISSION_DENIED", "NO_SOURCE_LOCATION"], "enumDescriptions": ["No reason was specified.", "The user lacks permission to access some of the network resources required to run the test.", "No valid source endpoint could be derived from the request."], "type": "string"}, "destinationEgressLocation": {"$ref": "EdgeLocation", "description": "The EdgeLocation from which a packet, destined to the internet, will egress the Google network. This will only be populated for a connectivity test which has an internet destination address. The absence of this field *must not* be used as an indication that the destination is part of the Google network."}, "edgeResponses": {"description": "Probing results for all edge devices.", "items": {"$ref": "SingleEdgeResponse"}, "type": "array"}, "endpointInfo": {"$ref": "EndpointInfo", "description": "The source and destination endpoints derived from the test input and used for active probing."}, "error": {"$ref": "Status", "description": "Details about an internal failure or the cancellation of active probing."}, "probedAllDevices": {"description": "Whether all relevant edge devices were probed.", "type": "boolean"}, "probingLatency": {"$ref": "LatencyDistribution", "description": "Latency as measured by active probing in one direction: from the source to the destination endpoint."}, "result": {"description": "The overall result of active probing.", "enum": ["PROBING_RESULT_UNSPECIFIED", "REACHABLE", "UNREACHABLE", "REACHABILITY_INCONSISTENT", "UNDETERMINED"], "enumDescriptions": ["No result was specified.", "At least 95% of packets reached the destination.", "No packets reached the destination.", "Less than 95% of packets reached the destination.", "Reachability could not be determined. Possible reasons are: * The user lacks permission to access some of the network resources required to run the test. * No valid source endpoint could be derived from the request. * An internal error occurred."], "type": "string"}, "sentProbeCount": {"description": "Number of probes sent.", "format": "int32", "type": "integer"}, "successfulProbeCount": {"description": "Number of probes that reached the destination.", "format": "int32", "type": "integer"}, "verifyTime": {"description": "The time that reachability was assessed through active probing.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ProxyConnectionInfo": {"description": "For display only. Metadata associated with ProxyConnection.", "id": "ProxyConnectionInfo", "properties": {"networkUri": {"description": "URI of the network where connection is proxied.", "type": "string"}, "newDestinationIp": {"description": "Destination IP address of a new connection.", "type": "string"}, "newDestinationPort": {"description": "Destination port of a new connection. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "newSourceIp": {"description": "Source IP address of a new connection.", "type": "string"}, "newSourcePort": {"description": "Source port of a new connection. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "oldDestinationIp": {"description": "Destination IP address of an original connection", "type": "string"}, "oldDestinationPort": {"description": "Destination port of an original connection. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "oldSourceIp": {"description": "Source IP address of an original connection.", "type": "string"}, "oldSourcePort": {"description": "Source port of an original connection. Only valid when protocol is TCP or UDP.", "format": "int32", "type": "integer"}, "protocol": {"description": "IP protocol in string format, for example: \"TCP\", \"UDP\", \"ICMP\".", "type": "string"}, "subnetUri": {"description": "<PERSON><PERSON> of proxy subnet.", "type": "string"}}, "type": "object"}, "QueryOrgVpcFlowLogsConfigsResponse": {"description": "Response for the `QueryVpcFlowLogsConfigs` method.", "id": "QueryOrgVpcFlowLogsConfigsResponse", "properties": {"nextPageToken": {"description": "Page token to fetch the next set of configurations.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached (when querying all locations with `-`).", "items": {"type": "string"}, "type": "array"}, "vpcFlowLogsConfigs": {"description": "List of VPC Flow Log configurations.", "items": {"$ref": "VpcFlowLogsConfig"}, "type": "array"}}, "type": "object"}, "ReachabilityDetails": {"description": "Results of the configuration analysis from the last run of the test.", "id": "ReachabilityDetails", "properties": {"error": {"$ref": "Status", "description": "The details of a failure or a cancellation of reachability analysis."}, "result": {"description": "The overall result of the test's configuration analysis.", "enum": ["RESULT_UNSPECIFIED", "REACHABLE", "UNREACHABLE", "AMBIGUOUS", "UNDETERMINED"], "enumDescriptions": ["No result was specified.", "Possible scenarios are: * The configuration analysis determined that a packet originating from the source is expected to reach the destination. * The analysis didn't complete because the user lacks permission for some of the resources in the trace. However, at the time the user's permission became insufficient, the trace had been successful so far.", "A packet originating from the source is expected to be dropped before reaching the destination.", "The source and destination endpoints do not uniquely identify the test location in the network, and the reachability result contains multiple traces. For some traces, a packet could be delivered, and for others, it would not be. This result is also assigned to configuration analysis of return path if on its own it should be REACHABLE, but configuration analysis of forward path is AMBIGUOUS.", "The configuration analysis did not complete. Possible reasons are: * A permissions error occurred--for example, the user might not have read permission for all of the resources named in the test. * An internal error occurred. * The analyzer received an invalid or unsupported argument or was unable to identify a known endpoint."], "type": "string"}, "traces": {"description": "Result may contain a list of traces if a test has multiple possible paths in the network, such as when destination endpoint is a load balancer with multiple backends.", "items": {"$ref": "Trace"}, "type": "array"}, "verifyTime": {"description": "The time of the configuration analysis.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "RedisClusterInfo": {"description": "For display only. Metadata associated with a Redis Cluster.", "id": "RedisClusterInfo", "properties": {"discoveryEndpointIpAddress": {"description": "Discovery endpoint IP address of a Redis Cluster.", "type": "string"}, "displayName": {"description": "Name of a Redis Cluster.", "type": "string"}, "location": {"description": "Name of the region in which the Redis Cluster is defined. For example, \"us-central1\".", "type": "string"}, "networkUri": {"description": "URI of the network containing the Redis Cluster endpoints in format \"projects/{project_id}/global/networks/{network_id}\".", "type": "string"}, "secondaryEndpointIpAddress": {"description": "Secondary endpoint IP address of a Redis Cluster.", "type": "string"}, "uri": {"description": "URI of a Redis Cluster in format \"projects/{project_id}/locations/{location}/clusters/{cluster_id}\"", "type": "string"}}, "type": "object"}, "RedisInstanceInfo": {"description": "For display only. Metadata associated with a Cloud Redis Instance.", "id": "RedisInstanceInfo", "properties": {"displayName": {"description": "Name of a Cloud Redis Instance.", "type": "string"}, "networkUri": {"description": "URI of a Cloud Redis Instance network.", "type": "string"}, "primaryEndpointIp": {"description": "Primary endpoint IP address of a Cloud Redis Instance.", "type": "string"}, "readEndpointIp": {"description": "Read endpoint IP address of a Cloud Redis Instance (if applicable).", "type": "string"}, "region": {"description": "Region in which the Cloud Redis Instance is defined.", "type": "string"}, "uri": {"description": "URI of a Cloud Redis Instance.", "type": "string"}}, "type": "object"}, "RerunConnectivityTestRequest": {"description": "Request for the `RerunConnectivityTest` method.", "id": "RerunConnectivityTestRequest", "properties": {}, "type": "object"}, "RouteInfo": {"description": "For display only. Metadata associated with a Compute Engine route.", "id": "RouteInfo", "properties": {"advertisedRouteNextHopUri": {"deprecated": true, "description": "For ADVERTISED routes, the URI of their next hop, i.e. the URI of the hybrid endpoint (VPN tunnel, Interconnect attachment, NCC router appliance) the advertised prefix is advertised through, or URI of the source peered network. Deprecated in favor of the next_hop_uri field, not used in new tests.", "type": "string"}, "advertisedRouteSourceRouterUri": {"description": "For ADVERTISED dynamic routes, the URI of the Cloud Router that advertised the corresponding IP prefix.", "type": "string"}, "destIpRange": {"description": "Destination IP range of the route.", "type": "string"}, "destPortRanges": {"description": "Destination port ranges of the route. POLICY_BASED routes only.", "items": {"type": "string"}, "type": "array"}, "displayName": {"description": "Name of a route.", "type": "string"}, "instanceTags": {"description": "Instance tags of the route.", "items": {"type": "string"}, "type": "array"}, "nccHubRouteUri": {"description": "For PEERING_SUBNET and PEERING_DYNAMIC routes that are advertised by NCC Hub, the URI of the corresponding route in NCC Hub's routing table.", "type": "string"}, "nccHubUri": {"description": "URI of the NCC Hub the route is advertised by. PEERING_SUBNET and PEERING_DYNAMIC routes that are advertised by NCC Hub only.", "type": "string"}, "nccSpokeUri": {"description": "URI of the destination NCC Spoke. PEERING_SUBNET and PEERING_DYNAMIC routes that are advertised by NCC Hub only.", "type": "string"}, "networkUri": {"description": "URI of a VPC network where route is located.", "type": "string"}, "nextHop": {"deprecated": true, "description": "String type of the next hop of the route (for example, \"VPN tunnel\"). Deprecated in favor of the next_hop_type and next_hop_uri fields, not used in new tests.", "type": "string"}, "nextHopNetworkUri": {"description": "URI of a VPC network where the next hop resource is located.", "type": "string"}, "nextHopType": {"description": "Type of next hop.", "enum": ["NEXT_HOP_TYPE_UNSPECIFIED", "NEXT_HOP_IP", "NEXT_HOP_INSTANCE", "NEXT_HOP_NETWORK", "NEXT_HOP_PEERING", "NEXT_HOP_INTERCONNECT", "NEXT_HOP_VPN_TUNNEL", "NEXT_HOP_VPN_GATEWAY", "NEXT_HOP_INTERNET_GATEWAY", "NEXT_HOP_BLACKHOLE", "NEXT_HOP_ILB", "NEXT_HOP_ROUTER_APPLIANCE", "NEXT_HOP_NCC_HUB", "SECURE_WEB_PROXY_GATEWAY"], "enumDescriptions": ["Unspecified type. Default value.", "Next hop is an IP address.", "Next hop is a Compute Engine instance.", "Next hop is a VPC network gateway.", "Next hop is a peering VPC. This scenario only happens when the user doesn't have permissions to the project where the next hop resource is located.", "Next hop is an interconnect.", "Next hop is a VPN tunnel.", "Next hop is a VPN gateway. This scenario only happens when tracing connectivity from an on-premises network to Google Cloud through a VPN. The analysis simulates a packet departing from the on-premises network through a VPN tunnel and arriving at a Cloud VPN gateway.", "Next hop is an internet gateway.", "Next hop is blackhole; that is, the next hop either does not exist or is unusable.", "Next hop is the forwarding rule of an Internal Load Balancer.", "Next hop is a [router appliance instance](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/ra-overview).", "Next hop is an NCC hub. This scenario only happens when the user doesn't have permissions to the project where the next hop resource is located.", "Next hop is Secure Web Proxy Gateway."], "type": "string"}, "nextHopUri": {"description": "URI of the next hop resource.", "type": "string"}, "originatingRouteDisplayName": {"description": "For PEERING_SUBNET, PEERING_STATIC and PEERING_DYNAMIC routes, the name of the originating SUBNET/STATIC/DYNAMIC route.", "type": "string"}, "originatingRouteUri": {"description": "For PEERING_SUBNET and PEERING_STATIC routes, the URI of the originating SUBNET/STATIC route.", "type": "string"}, "priority": {"description": "Priority of the route.", "format": "int32", "type": "integer"}, "protocols": {"description": "Protocols of the route. POLICY_BASED routes only.", "items": {"type": "string"}, "type": "array"}, "region": {"description": "Region of the route. DYNAMIC, PEERING_DYNAMIC, POLICY_BASED and ADVERTISED routes only. If set for POLICY_BASED route, this is a region of VLAN attachments for Cloud Interconnect the route applies to.", "type": "string"}, "routeScope": {"deprecated": true, "description": "Indicates where route is applicable. Deprecated, routes with NCC_HUB scope are not included in the trace in new tests.", "enum": ["ROUTE_SCOPE_UNSPECIFIED", "NETWORK", "NCC_HUB"], "enumDescriptions": ["Unspecified scope. Default value.", "Route is applicable to packets in Network.", "Route is applicable to packets using NCC Hub's routing table."], "type": "string"}, "routeType": {"description": "Type of route.", "enum": ["ROUTE_TYPE_UNSPECIFIED", "SUBNET", "STATIC", "DYNAMIC", "PEERING_SUBNET", "PEERING_STATIC", "PEERING_DYNAMIC", "POLICY_BASED", "ADVERTISED"], "enumDescriptions": ["Unspecified type. Default value.", "Route is a subnet route automatically created by the system.", "Static route created by the user, including the default route to the internet.", "Dynamic route exchanged between BGP peers.", "A subnet route received from peering network or NCC Hub.", "A static route received from peering network.", "A dynamic route received from peering network or NCC Hub.", "Policy based route.", "Advertised route. Synthetic route which is used to transition from the StartFromPrivateNetwork state in Connectivity tests."], "type": "string"}, "srcIpRange": {"description": "Source IP address range of the route. POLICY_BASED routes only.", "type": "string"}, "srcPortRanges": {"description": "Source port ranges of the route. POLICY_BASED routes only.", "items": {"type": "string"}, "type": "array"}, "uri": {"description": "URI of a route. SUBNET, STATIC, PEERING_SUBNET (only for peering network) and POLICY_BASED routes only.", "type": "string"}}, "type": "object"}, "ServerlessExternalConnectionInfo": {"description": "For display only. Metadata associated with a serverless public connection.", "id": "ServerlessExternalConnectionInfo", "properties": {"selectedIpAddress": {"description": "Selected starting IP address, from the Google dynamic address pool.", "type": "string"}}, "type": "object"}, "ServerlessNegInfo": {"description": "For display only. Metadata associated with the serverless network endpoint group backend.", "id": "ServerlessNegInfo", "properties": {"negUri": {"description": "URI of the serverless network endpoint group.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SingleEdgeResponse": {"description": "Probing results for a single edge device.", "id": "SingleEdgeResponse", "properties": {"destinationEgressLocation": {"$ref": "EdgeLocation", "description": "The EdgeLocation from which a packet, destined to the internet, will egress the Google network. This will only be populated for a connectivity test which has an internet destination address. The absence of this field *must not* be used as an indication that the destination is part of the Google network."}, "destinationRouter": {"description": "Router name in the format '{router}.{metroshard}'. For example: pf01.aaa01, pr02.aaa01.", "type": "string"}, "probingLatency": {"$ref": "LatencyDistribution", "description": "Latency as measured by active probing in one direction: from the source to the destination endpoint."}, "result": {"description": "The overall result of active probing for this egress device.", "enum": ["PROBING_RESULT_UNSPECIFIED", "REACHABLE", "UNREACHABLE", "REACHABILITY_INCONSISTENT", "UNDETERMINED"], "enumDescriptions": ["No result was specified.", "At least 95% of packets reached the destination.", "No packets reached the destination.", "Less than 95% of packets reached the destination.", "Reachability could not be determined. Possible reasons are: * The user lacks permission to access some of the network resources required to run the test. * No valid source endpoint could be derived from the request. * An internal error occurred."], "type": "string"}, "sentProbeCount": {"description": "Number of probes sent.", "format": "int32", "type": "integer"}, "successfulProbeCount": {"description": "Number of probes that reached the destination.", "format": "int32", "type": "integer"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Step": {"description": "A simulated forwarding path is composed of multiple steps. Each step has a well-defined state and an associated configuration.", "id": "Step", "properties": {"abort": {"$ref": "AbortInfo", "description": "Display information of the final state \"abort\" and reason."}, "appEngineVersion": {"$ref": "AppEngineVersionInfo", "description": "Display information of an App Engine service version."}, "causesDrop": {"description": "This is a step that leads to the final state Drop.", "type": "boolean"}, "cloudFunction": {"$ref": "CloudFunctionInfo", "description": "Display information of a Cloud Function."}, "cloudRunRevision": {"$ref": "CloudRunRevisionInfo", "description": "Display information of a Cloud Run revision."}, "cloudSqlInstance": {"$ref": "CloudSQLInstanceInfo", "description": "Display information of a Cloud SQL instance."}, "deliver": {"$ref": "DeliverInfo", "description": "Display information of the final state \"deliver\" and reason."}, "description": {"description": "A description of the step. Usually this is a summary of the state.", "type": "string"}, "directVpcEgressConnection": {"$ref": "DirectVpcEgressConnectionInfo", "description": "Display information of a serverless direct VPC egress connection."}, "drop": {"$ref": "DropInfo", "description": "Display information of the final state \"drop\" and reason."}, "endpoint": {"$ref": "EndpointInfo", "description": "Display information of the source and destination under analysis. The endpoint information in an intermediate state may differ with the initial input, as it might be modified by state like NAT, or Connection Proxy."}, "firewall": {"$ref": "FirewallInfo", "description": "Display information of a Compute Engine firewall rule."}, "forward": {"$ref": "ForwardInfo", "description": "Display information of the final state \"forward\" and reason."}, "forwardingRule": {"$ref": "ForwardingRuleInfo", "description": "Display information of a Compute Engine forwarding rule."}, "gkeMaster": {"$ref": "GKEMasterInfo", "description": "Display information of a Google Kubernetes Engine cluster master."}, "googleService": {"$ref": "GoogleServiceInfo", "description": "Display information of a Google service"}, "instance": {"$ref": "InstanceInfo", "description": "Display information of a Compute Engine instance."}, "loadBalancer": {"$ref": "LoadBalancerInfo", "deprecated": true, "description": "Display information of the load balancers. Deprecated in favor of the `load_balancer_backend_info` field, not used in new tests."}, "loadBalancerBackendInfo": {"$ref": "LoadBalancerBackendInfo", "description": "Display information of a specific load balancer backend."}, "nat": {"$ref": "NatInfo", "description": "Display information of a NAT."}, "network": {"$ref": "NetworkInfo", "description": "Display information of a Google Cloud network."}, "projectId": {"description": "Project ID that contains the configuration this step is validating.", "type": "string"}, "proxyConnection": {"$ref": "ProxyConnectionInfo", "description": "Display information of a ProxyConnection."}, "redisCluster": {"$ref": "RedisClusterInfo", "description": "Display information of a Redis Cluster."}, "redisInstance": {"$ref": "RedisInstanceInfo", "description": "Display information of a Redis Instance."}, "route": {"$ref": "RouteInfo", "description": "Display information of a Compute Engine route."}, "serverlessExternalConnection": {"$ref": "ServerlessExternalConnectionInfo", "description": "Display information of a serverless public (external) connection."}, "serverlessNeg": {"$ref": "ServerlessNegInfo", "description": "Display information of a Serverless network endpoint group backend. Used only for return traces."}, "state": {"description": "Each step is in one of the pre-defined states.", "enum": ["STATE_UNSPECIFIED", "START_FROM_INSTANCE", "START_FROM_INTERNET", "START_FROM_GOOGLE_SERVICE", "START_FROM_PRIVATE_NETWORK", "START_FROM_GKE_MASTER", "START_FROM_CLOUD_SQL_INSTANCE", "START_FROM_REDIS_INSTANCE", "START_FROM_REDIS_CLUSTER", "START_FROM_CLOUD_FUNCTION", "START_FROM_APP_ENGINE_VERSION", "START_FROM_CLOUD_RUN_REVISION", "START_FROM_STORAGE_BUCKET", "START_FROM_PSC_PUBLISHED_SERVICE", "START_FROM_SERVERLESS_NEG", "APPLY_INGRESS_FIREWALL_RULE", "APPLY_EGRESS_FIREWALL_RULE", "APPLY_ROUTE", "APPLY_FORWARDING_RULE", "ANALYZE_LOAD_BALANCER_BACKEND", "SPOOFING_APPROVED", "ARRIVE_AT_INSTANCE", "ARRIVE_AT_INTERNAL_LOAD_BALANCER", "ARRIVE_AT_EXTERNAL_LOAD_BALANCER", "ARRIVE_AT_VPN_GATEWAY", "ARRIVE_AT_VPN_TUNNEL", "ARRIVE_AT_VPC_CONNECTOR", "DIRECT_VPC_EGRESS_CONNECTION", "SERVERLESS_EXTERNAL_CONNECTION", "NAT", "PROXY_CONNECTION", "DELIVER", "DROP", "FORWARD", "ABORT", "VIEWER_PERMISSION_MISSING"], "enumDeprecated": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, true, true, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Unspecified state.", "Initial state: packet originating from a Compute Engine instance. An InstanceInfo is populated with starting instance information.", "Initial state: packet originating from the internet. The endpoint information is populated.", "Initial state: packet originating from a Google service. The google_service information is populated.", "Initial state: packet originating from a VPC or on-premises network with internal source IP. If the source is a VPC network visible to the user, a NetworkInfo is populated with details of the network.", "Initial state: packet originating from a Google Kubernetes Engine cluster master. A GKEMasterInfo is populated with starting instance information.", "Initial state: packet originating from a Cloud SQL instance. A CloudSQLInstanceInfo is populated with starting instance information.", "Initial state: packet originating from a Redis instance. A RedisInstanceInfo is populated with starting instance information.", "Initial state: packet originating from a Redis Cluster. A RedisClusterInfo is populated with starting Cluster information.", "Initial state: packet originating from a Cloud Function. A CloudFunctionInfo is populated with starting function information.", "Initial state: packet originating from an App Engine service version. An AppEngineVersionInfo is populated with starting version information.", "Initial state: packet originating from a Cloud Run revision. A CloudRunRevisionInfo is populated with starting revision information.", "Initial state: packet originating from a Storage Bucket. Used only for return traces. The storage_bucket information is populated.", "Initial state: packet originating from a published service that uses Private Service Connect. Used only for return traces.", "Initial state: packet originating from a serverless network endpoint group backend. Used only for return traces. The serverless_neg information is populated.", "Config checking state: verify ingress firewall rule.", "Config checking state: verify egress firewall rule.", "Config checking state: verify route.", "Config checking state: match forwarding rule.", "Config checking state: verify load balancer backend configuration.", "Config checking state: packet sent or received under foreign IP address and allowed.", "Forwarding state: arriving at a Compute Engine instance.", "Forwarding state: arriving at a Compute Engine internal load balancer. Deprecated in favor of the `ANALYZE_LOAD_BALANCER_BACKEND` state, not used in new tests.", "Forwarding state: arriving at a Compute Engine external load balancer. Deprecated in favor of the `ANALYZE_LOAD_BALANCER_BACKEND` state, not used in new tests.", "Forwarding state: arriving at a Cloud VPN gateway.", "Forwarding state: arriving at a Cloud VPN tunnel.", "Forwarding state: arriving at a VPC connector.", "Forwarding state: for packets originating from a serverless endpoint forwarded through Direct VPC egress.", "Forwarding state: for packets originating from a serverless endpoint forwarded through public (external) connectivity.", "Transition state: packet header translated.", "Transition state: original connection is terminated and a new proxied connection is initiated.", "Final state: packet could be delivered.", "Final state: packet could be dropped.", "Final state: packet could be forwarded to a network with an unknown configuration.", "Final state: analysis is aborted.", "Special state: viewer of the test result does not have permission to see the configuration in this step."], "type": "string"}, "storageBucket": {"$ref": "StorageBucketInfo", "description": "Display information of a Storage Bucket. Used only for return traces."}, "vpcConnector": {"$ref": "VpcConnectorInfo", "description": "Display information of a VPC connector."}, "vpnGateway": {"$ref": "VpnGatewayInfo", "description": "Display information of a Compute Engine VPN gateway."}, "vpnTunnel": {"$ref": "VpnTunnelInfo", "description": "Display information of a Compute Engine VPN tunnel."}}, "type": "object"}, "StorageBucketInfo": {"description": "For display only. Metadata associated with Storage Bucket.", "id": "StorageBucketInfo", "properties": {"bucket": {"description": "Cloud Storage Bucket name.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Trace": {"description": "Trace represents one simulated packet forwarding path. * Each trace contains multiple ordered steps. * Each step is in a particular state with associated configuration. * State is categorized as final or non-final states. * Each final state has a reason associated. * Each trace must end with a final state (the last step). ``` |---------------------Trace----------------------| Step1(State) Step2(State) --- StepN(State(final)) ```", "id": "Trace", "properties": {"endpointInfo": {"$ref": "EndpointInfo", "description": "Derived from the source and destination endpoints definition specified by user request, and validated by the data plane model. If there are multiple traces starting from different source locations, then the endpoint_info may be different between traces."}, "forwardTraceId": {"description": "ID of trace. For forward traces, this ID is unique for each trace. For return traces, it matches ID of associated forward trace. A single forward trace can be associated with none, one or more than one return trace.", "format": "int32", "type": "integer"}, "steps": {"description": "A trace of a test contains multiple steps from the initial state to the final state (delivered, dropped, forwarded, or aborted). The steps are ordered by the processing sequence within the simulated network state machine. It is critical to preserve the order of the steps and avoid reordering or sorting them.", "items": {"$ref": "Step"}, "type": "array"}}, "type": "object"}, "VpcConnectorInfo": {"description": "For display only. Metadata associated with a VPC connector.", "id": "VpcConnectorInfo", "properties": {"displayName": {"description": "Name of a VPC connector.", "type": "string"}, "location": {"description": "Location in which the VPC connector is deployed.", "type": "string"}, "uri": {"description": "URI of a VPC connector.", "type": "string"}}, "type": "object"}, "VpcFlowLogsConfig": {"description": "A configuration to generate VPC Flow Logs.", "id": "VpcFlowLogsConfig", "properties": {"aggregationInterval": {"description": "Optional. The aggregation interval for the logs. Default value is INTERVAL_5_SEC.", "enum": ["AGGREGATION_INTERVAL_UNSPECIFIED", "INTERVAL_5_SEC", "INTERVAL_30_SEC", "INTERVAL_1_MIN", "INTERVAL_5_MIN", "INTERVAL_10_MIN", "INTERVAL_15_MIN"], "enumDescriptions": ["If not specified, will default to INTERVAL_5_SEC.", "Aggregate logs in 5s intervals.", "Aggregate logs in 30s intervals.", "Aggregate logs in 1m intervals.", "Aggregate logs in 5m intervals.", "Aggregate logs in 10m intervals.", "Aggregate logs in 15m intervals."], "type": "string"}, "createTime": {"description": "Output only. The time the config was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "crossProjectMetadata": {"description": "Optional. Determines whether to include cross project annotations in the logs. This field is available only for organization configurations. If not specified in org configs will be set to CROSS_PROJECT_METADATA_ENABLED.", "enum": ["CROSS_PROJECT_METADATA_UNSPECIFIED", "CROSS_PROJECT_METADATA_ENABLED", "CROSS_PROJECT_METADATA_DISABLED"], "enumDescriptions": ["If not specified, the default is CROSS_PROJECT_METADATA_ENABLED.", "When CROSS_PROJECT_METADATA_ENABLED, metadata from other projects will be included in the logs.", "When CROSS_PROJECT_METADATA_DISABLED, metadata from other projects will not be included in the logs."], "type": "string"}, "description": {"description": "Optional. The user-supplied description of the VPC Flow Logs configuration. Maximum of 512 characters.", "type": "string"}, "filterExpr": {"description": "Optional. Export filter used to define which VPC Flow Logs should be logged.", "type": "string"}, "flowSampling": {"description": "Optional. The value of the field must be in (0, 1]. The sampling rate of VPC Flow Logs where 1.0 means all collected logs are reported. Setting the sampling rate to 0.0 is not allowed. If you want to disable VPC Flow Logs, use the state field instead. Default value is 1.0.", "format": "float", "type": "number"}, "interconnectAttachment": {"description": "Traffic will be logged from the Interconnect Attachment. Format: projects/{project_id}/regions/{region}/interconnectAttachments/{name}", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user-provided metadata.", "type": "object"}, "metadata": {"description": "Optional. Configures whether all, none or a subset of metadata fields should be added to the reported VPC flow logs. Default value is INCLUDE_ALL_METADATA.", "enum": ["METADATA_UNSPECIFIED", "INCLUDE_ALL_METADATA", "EXCLUDE_ALL_METADATA", "CUSTOM_METADATA"], "enumDescriptions": ["If not specified, will default to INCLUDE_ALL_METADATA.", "Include all metadata fields.", "Exclude all metadata fields.", "Include only custom fields (specified in metadata_fields)."], "type": "string"}, "metadataFields": {"description": "Optional. Custom metadata fields to include in the reported VPC flow logs. Can only be specified if \"metadata\" was set to CUSTOM_METADATA.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Identifier. Unique name of the configuration using one of the forms: `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config_id}` `organizations/{organization_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config_id}`", "type": "string"}, "network": {"description": "Traffic will be logged from VMs, VPN tunnels and Interconnect Attachments within the network. Format: projects/{project_id}/global/networks/{name}", "type": "string"}, "state": {"description": "Optional. The state of the VPC Flow Log configuration. Default value is ENABLED. When creating a new configuration, it must be enabled. Setting state=DISABLED will pause the log generation for this config.", "enum": ["STATE_UNSPECIFIED", "ENABLED", "DISABLED"], "enumDescriptions": ["If not specified, will default to ENABLED.", "When ENABLED, this configuration will generate logs.", "When DISABLED, this configuration will not generate logs."], "type": "string"}, "subnet": {"description": "Traffic will be logged from VMs within the subnetwork. Format: projects/{project_id}/regions/{region}/subnetworks/{name}", "type": "string"}, "targetResourceState": {"description": "Output only. A diagnostic bit - describes the state of the configured target resource for diagnostic purposes.", "enum": ["TARGET_RESOURCE_STATE_UNSPECIFIED", "TARGET_RESOURCE_EXISTS", "TARGET_RESOURCE_DOES_NOT_EXIST"], "enumDescriptions": ["Unspecified target resource state.", "Indicates that the target resource exists.", "Indicates that the target resource does not exist."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time the config was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vpnTunnel": {"description": "Traffic will be logged from the VPN Tunnel. Format: projects/{project_id}/regions/{region}/vpnTunnels/{name}", "type": "string"}}, "type": "object"}, "VpnGatewayInfo": {"description": "For display only. Metadata associated with a Compute Engine VPN gateway.", "id": "VpnGatewayInfo", "properties": {"displayName": {"description": "Name of a VPN gateway.", "type": "string"}, "ipAddress": {"description": "IP address of the VPN gateway.", "type": "string"}, "networkUri": {"description": "URI of a Compute Engine network where the VPN gateway is configured.", "type": "string"}, "region": {"description": "Name of a Google Cloud region where this VPN gateway is configured.", "type": "string"}, "uri": {"description": "URI of a VPN gateway.", "type": "string"}, "vpnTunnelUri": {"description": "A VPN tunnel that is associated with this VPN gateway. There may be multiple VPN tunnels configured on a VPN gateway, and only the one relevant to the test is displayed.", "type": "string"}}, "type": "object"}, "VpnTunnelInfo": {"description": "For display only. Metadata associated with a Compute Engine VPN tunnel.", "id": "VpnTunnelInfo", "properties": {"displayName": {"description": "Name of a VPN tunnel.", "type": "string"}, "networkUri": {"description": "URI of a Compute Engine network where the VPN tunnel is configured.", "type": "string"}, "region": {"description": "Name of a Google Cloud region where this VPN tunnel is configured.", "type": "string"}, "remoteGateway": {"description": "URI of a VPN gateway at remote end of the tunnel.", "type": "string"}, "remoteGatewayIp": {"description": "Remote VPN gateway's IP address.", "type": "string"}, "routingType": {"description": "Type of the routing policy.", "enum": ["ROUTING_TYPE_UNSPECIFIED", "ROUTE_BASED", "POLICY_BASED", "DYNAMIC"], "enumDescriptions": ["Unspecified type. Default value.", "Route based VPN.", "Policy based routing.", "Dynamic (BGP) routing."], "type": "string"}, "sourceGateway": {"description": "URI of the VPN gateway at local end of the tunnel.", "type": "string"}, "sourceGatewayIp": {"description": "Local VPN gateway's IP address.", "type": "string"}, "uri": {"description": "URI of a VPN tunnel.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Network Management API", "version": "v1beta1", "version_module": true}