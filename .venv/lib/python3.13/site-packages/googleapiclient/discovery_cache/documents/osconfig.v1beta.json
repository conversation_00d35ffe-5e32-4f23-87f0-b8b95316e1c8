{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://osconfig.googleapis.com/", "batchPath": "batch", "canonicalName": "OS Config", "description": "OS management tools that can be used for patch management, patch compliance, and configuration management on VM instances.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/compute/docs/osconfig/rest", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "osconfig:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://osconfig.mtls.googleapis.com/", "name": "osconfig", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"guestPolicies": {"methods": {"create": {"description": "Create an OS Config guest policy.", "flatPath": "v1beta/projects/{projectsId}/guestPolicies", "httpMethod": "POST", "id": "osconfig.projects.guestPolicies.create", "parameterOrder": ["parent"], "parameters": {"guestPolicyId": {"description": "Required. The logical name of the guest policy in the project with the following restrictions: * Must contain only lowercase letters, numbers, and hyphens. * Must start with a letter. * Must be between 1-63 characters. * Must end with a number or a letter. * Must be unique within the project.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent using one of the following forms: `projects/{project_number}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/guestPolicies", "request": {"$ref": "GuestP<PERSON>y"}, "response": {"$ref": "GuestP<PERSON>y"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an OS Config guest policy.", "flatPath": "v1beta/projects/{projectsId}/guestPolicies/{guestPoliciesId}", "httpMethod": "DELETE", "id": "osconfig.projects.guestPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the guest policy using one of the following forms: `projects/{project_number}/guestPolicies/{guest_policy_id}`.", "location": "path", "pattern": "^projects/[^/]+/guestPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get an OS Config guest policy.", "flatPath": "v1beta/projects/{projectsId}/guestPolicies/{guestPoliciesId}", "httpMethod": "GET", "id": "osconfig.projects.guestPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the guest policy using one of the following forms: `projects/{project_number}/guestPolicies/{guest_policy_id}`.", "location": "path", "pattern": "^projects/[^/]+/guestPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GuestP<PERSON>y"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Get a page of OS Config guest policies.", "flatPath": "v1beta/projects/{projectsId}/guestPolicies", "httpMethod": "GET", "id": "osconfig.projects.guestPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of guest policies to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A pagination token returned from a previous call to `ListGuestPolicies` that indicates where this listing should continue from.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent using one of the following forms: `projects/{project_number}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/guestPolicies", "response": {"$ref": "ListGuestPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update an OS Config guest policy.", "flatPath": "v1beta/projects/{projectsId}/guestPolicies/{guestPoliciesId}", "httpMethod": "PATCH", "id": "osconfig.projects.guestPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Unique name of the resource in this project using one of the following forms: `projects/{project_number}/guestPolicies/{guest_policy_id}`.", "location": "path", "pattern": "^projects/[^/]+/guestPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Field mask that controls which fields of the guest policy should be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GuestP<PERSON>y"}, "response": {"$ref": "GuestP<PERSON>y"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "patchDeployments": {"methods": {"create": {"description": "Create an OS Config patch deployment.", "flatPath": "v1beta/projects/{projectsId}/patchDeployments", "httpMethod": "POST", "id": "osconfig.projects.patchDeployments.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The project to apply this patch deployment to in the form `projects/*`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "patchDeploymentId": {"description": "Required. A name for the patch deployment in the project. When creating a name the following rules apply: * Must contain only lowercase letters, numbers, and hyphens. * Must start with a letter. * Must be between 1-63 characters. * Must end with a number or a letter. * Must be unique within the project.", "location": "query", "type": "string"}}, "path": "v1beta/{+parent}/patchDeployments", "request": {"$ref": "PatchDeployment"}, "response": {"$ref": "PatchDeployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an OS Config patch deployment.", "flatPath": "v1beta/projects/{projectsId}/patchDeployments/{patchDeploymentsId}", "httpMethod": "DELETE", "id": "osconfig.projects.patchDeployments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the patch deployment in the form `projects/*/patchDeployments/*`.", "location": "path", "pattern": "^projects/[^/]+/patchDeployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get an OS Config patch deployment.", "flatPath": "v1beta/projects/{projectsId}/patchDeployments/{patchDeploymentsId}", "httpMethod": "GET", "id": "osconfig.projects.patchDeployments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the patch deployment in the form `projects/*/patchDeployments/*`.", "location": "path", "pattern": "^projects/[^/]+/patchDeployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "PatchDeployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Get a page of OS Config patch deployments.", "flatPath": "v1beta/projects/{projectsId}/patchDeployments", "httpMethod": "GET", "id": "osconfig.projects.patchDeployments.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of patch deployments to return. Default is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A pagination token returned from a previous call to ListPatchDeployments that indicates where this listing should continue from.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent in the form `projects/*`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/patchDeployments", "response": {"$ref": "ListPatchDeploymentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update an OS Config patch deployment.", "flatPath": "v1beta/projects/{projectsId}/patchDeployments/{patchDeploymentsId}", "httpMethod": "PATCH", "id": "osconfig.projects.patchDeployments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Unique name for the patch deployment resource in a project. The patch deployment name is in the form: `projects/{project_id}/patchDeployments/{patch_deployment_id}`. This field is ignored when you create a new patch deployment.", "location": "path", "pattern": "^projects/[^/]+/patchDeployments/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask that controls which fields of the patch deployment should be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "PatchDeployment"}, "response": {"$ref": "PatchDeployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "pause": {"description": "Change state of patch deployment to \"PAUSED\". Patch deployment in paused state doesn't generate patch jobs.", "flatPath": "v1beta/projects/{projectsId}/patchDeployments/{patchDeploymentsId}:pause", "httpMethod": "POST", "id": "osconfig.projects.patchDeployments.pause", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the patch deployment in the form `projects/*/patchDeployments/*`.", "location": "path", "pattern": "^projects/[^/]+/patchDeployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:pause", "request": {"$ref": "PausePatchDeploymentRequest"}, "response": {"$ref": "PatchDeployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resume": {"description": "Change state of patch deployment back to \"ACTIVE\". Patch deployment in active state continues to generate patch jobs.", "flatPath": "v1beta/projects/{projectsId}/patchDeployments/{patchDeploymentsId}:resume", "httpMethod": "POST", "id": "osconfig.projects.patchDeployments.resume", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the patch deployment in the form `projects/*/patchDeployments/*`.", "location": "path", "pattern": "^projects/[^/]+/patchDeployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:resume", "request": {"$ref": "ResumePatchDeploymentRequest"}, "response": {"$ref": "PatchDeployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "patchJobs": {"methods": {"cancel": {"description": "Cancel a patch job. The patch job must be active. Canceled patch jobs cannot be restarted.", "flatPath": "v1beta/projects/{projectsId}/patchJobs/{patchJobsId}:cancel", "httpMethod": "POST", "id": "osconfig.projects.patchJobs.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the patch in the form `projects/*/patchJobs/*`", "location": "path", "pattern": "^projects/[^/]+/patchJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:cancel", "request": {"$ref": "CancelPatchJobRequest"}, "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "execute": {"description": "Patch VM instances by creating and running a patch job.", "flatPath": "v1beta/projects/{projectsId}/patchJobs:execute", "httpMethod": "POST", "id": "osconfig.projects.patchJobs.execute", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The project in which to run this patch in the form `projects/*`", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/patchJobs:execute", "request": {"$ref": "ExecutePatchJobRequest"}, "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get the patch job. This can be used to track the progress of an ongoing patch job or review the details of completed jobs.", "flatPath": "v1beta/projects/{projectsId}/patchJobs/{patchJobsId}", "httpMethod": "GET", "id": "osconfig.projects.patchJobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the patch in the form `projects/*/patchJobs/*`", "location": "path", "pattern": "^projects/[^/]+/patchJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Get a list of patch jobs.", "flatPath": "v1beta/projects/{projectsId}/patchJobs", "httpMethod": "GET", "id": "osconfig.projects.patchJobs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "If provided, this field specifies the criteria that must be met by patch jobs to be included in the response. Currently, filtering is only available on the patch_deployment field.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of instance status to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A pagination token returned from a previous call that indicates where this listing should continue from.", "location": "query", "type": "string"}, "parent": {"description": "Required. In the form of `projects/*`", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/patchJobs", "response": {"$ref": "ListPatchJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"instanceDetails": {"methods": {"list": {"description": "Get a list of instance details for a given patch job.", "flatPath": "v1beta/projects/{projectsId}/patchJobs/{patchJobsId}/instanceDetails", "httpMethod": "GET", "id": "osconfig.projects.patchJobs.instanceDetails.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters results listed in the response. This field supports filtering results by instance zone, name, state, or `failure_reason`.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of instance details records to return. Default is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A pagination token returned from a previous call that indicates where this listing should continue from.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent for the instances are in the form of `projects/*/patchJobs/*`.", "location": "path", "pattern": "^projects/[^/]+/patchJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/instanceDetails", "response": {"$ref": "ListPatchJobInstanceDetailsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "zones": {"resources": {"instances": {"methods": {"lookupEffectiveGuestPolicy": {"description": "Lookup the effective guest policy that applies to a VM instance. This lookup merges all policies that are assigned to the instance ancestry.", "flatPath": "v1beta/projects/{projectsId}/zones/{zonesId}/instances/{instancesId}:lookupEffectiveGuestPolicy", "httpMethod": "POST", "id": "osconfig.projects.zones.instances.lookupEffectiveGuestPolicy", "parameterOrder": ["instance"], "parameters": {"instance": {"description": "Required. The VM instance whose policies are being looked up.", "location": "path", "pattern": "^projects/[^/]+/zones/[^/]+/instances/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+instance}:lookupEffectiveGuestPolicy", "request": {"$ref": "LookupEffectiveGuestPolicyRequest"}, "response": {"$ref": "EffectiveGuestPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250511", "rootUrl": "https://osconfig.googleapis.com/", "schemas": {"AptRepository": {"description": "Represents a single Apt package repository. This repository is added to a repo file that is stored at `/etc/apt/sources.list.d/google_osconfig.list`.", "id": "AptRepository", "properties": {"archiveType": {"description": "Type of archive files in this repository. The default behavior is DEB.", "enum": ["ARCHIVE_TYPE_UNSPECIFIED", "DEB", "DEB_SRC"], "enumDescriptions": ["Unspecified.", "DEB indicates that the archive contains binary files.", "DEB_SRC indicates that the archive contains source files."], "type": "string"}, "components": {"description": "Required. List of components for this repository. Must contain at least one item.", "items": {"type": "string"}, "type": "array"}, "distribution": {"description": "Required. Distribution of this repository.", "type": "string"}, "gpgKey": {"description": "URI of the key file for this repository. The agent maintains a keyring at `/etc/apt/trusted.gpg.d/osconfig_agent_managed.gpg` containing all the keys in any applied guest policy.", "type": "string"}, "uri": {"description": "Required. URI for this repository.", "type": "string"}}, "type": "object"}, "AptSettings": {"description": "Apt patching is completed by executing `apt-get update && apt-get upgrade`. Additional options can be set to control how this is executed.", "id": "AptSettings", "properties": {"excludes": {"description": "List of packages to exclude from update. These packages will be excluded", "items": {"type": "string"}, "type": "array"}, "exclusivePackages": {"description": "An exclusive list of packages to be updated. These are the only packages that will be updated. If these packages are not installed, they will be ignored. This field cannot be specified with any other patch configuration fields.", "items": {"type": "string"}, "type": "array"}, "type": {"description": "By changing the type to DIST, the patching is performed using `apt-get dist-upgrade` instead.", "enum": ["TYPE_UNSPECIFIED", "DIST", "UPGRADE"], "enumDescriptions": ["By default, upgrade will be performed.", "Runs `apt-get dist-upgrade`.", "Runs `apt-get upgrade`."], "type": "string"}}, "type": "object"}, "Assignment": {"description": "An assignment represents the group or groups of VM instances that the policy applies to. If an assignment is empty, it applies to all VM instances. Otherwise, the targeted VM instances must meet all the criteria specified. So if both labels and zones are specified, the policy applies to VM instances with those labels and in those zones.", "id": "Assignment", "properties": {"groupLabels": {"description": "Targets instances matching at least one of these label sets. This allows an assignment to target disparate groups, for example \"env=prod or env=staging\".", "items": {"$ref": "AssignmentGroupLabel"}, "type": "array"}, "instanceNamePrefixes": {"description": "Targets VM instances whose name starts with one of these prefixes. Like labels, this is another way to group VM instances when targeting configs, for example prefix=\"prod-\". Only supported for project-level policies.", "items": {"type": "string"}, "type": "array"}, "instances": {"description": "Targets any of the instances specified. Instances are specified by their URI in the form `zones/[ZONE]/instances/[INSTANCE_NAME]`. Instance targeting is uncommon and is supported to facilitate the management of changes by the instance or to target specific VM instances for development and testing. Only supported for project-level policies and must reference instances within this project.", "items": {"type": "string"}, "type": "array"}, "osTypes": {"description": "Targets VM instances matching at least one of the following OS types. VM instances must match all supplied criteria for a given OsType to be included.", "items": {"$ref": "AssignmentOsType"}, "type": "array"}, "zones": {"description": "Targets instances in any of these zones. Leave empty to target instances in any zone. Zonal targeting is uncommon and is supported to facilitate the management of changes by zone.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AssignmentGroupLabel": {"description": "Represents a group of VM intances that can be identified as having all these labels, for example \"env=prod and app=web\".", "id": "AssignmentGroupLabel", "properties": {"labels": {"additionalProperties": {"type": "string"}, "description": "Google Compute Engine instance labels that must be present for an instance to be included in this assignment group.", "type": "object"}}, "type": "object"}, "AssignmentOsType": {"description": "Defines the criteria for selecting VM Instances by OS type.", "id": "AssignmentOsType", "properties": {"osArchitecture": {"description": "Targets VM instances with OS Inventory enabled and having the following OS architecture.", "type": "string"}, "osShortName": {"description": "Targets VM instances with OS Inventory enabled and having the following OS short name, for example \"debian\" or \"windows\".", "type": "string"}, "osVersion": {"description": "Targets VM instances with OS Inventory enabled and having the following following OS version.", "type": "string"}}, "type": "object"}, "CancelPatchJobRequest": {"description": "Message for canceling a patch job.", "id": "CancelPatchJobRequest", "properties": {}, "type": "object"}, "EffectiveGuestPolicy": {"description": "The effective guest policy that applies to a VM instance.", "id": "EffectiveGuestPolicy", "properties": {"packageRepositories": {"description": "List of package repository configurations assigned to the VM instance.", "items": {"$ref": "EffectiveGuestPolicySourcedPackageRepository"}, "type": "array"}, "packages": {"description": "List of package configurations assigned to the VM instance.", "items": {"$ref": "EffectiveGuestPolicySourcedPackage"}, "type": "array"}, "softwareRecipes": {"description": "List of recipes assigned to the VM instance.", "items": {"$ref": "EffectiveGuestPolicySourcedSoftwareRecipe"}, "type": "array"}}, "type": "object"}, "EffectiveGuestPolicySourcedPackage": {"description": "A guest policy package including its source.", "id": "EffectiveGuestPolicySourcedPackage", "properties": {"package": {"$ref": "Package", "description": "A software package to configure on the VM instance."}, "source": {"description": "Name of the guest policy providing this config.", "type": "string"}}, "type": "object"}, "EffectiveGuestPolicySourcedPackageRepository": {"description": "A guest policy package repository including its source.", "id": "EffectiveGuestPolicySourcedPackageRepository", "properties": {"packageRepository": {"$ref": "PackageRepository", "description": "A software package repository to configure on the VM instance."}, "source": {"description": "Name of the guest policy providing this config.", "type": "string"}}, "type": "object"}, "EffectiveGuestPolicySourcedSoftwareRecipe": {"description": "A guest policy recipe including its source.", "id": "EffectiveGuestPolicySourcedSoftwareRecipe", "properties": {"softwareRecipe": {"$ref": "SoftwareRecipe", "description": "A software recipe to configure on the VM instance."}, "source": {"description": "Name of the guest policy providing this config.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ExecStep": {"description": "A step that runs an executable for a PatchJob.", "id": "ExecStep", "properties": {"linuxExecStepConfig": {"$ref": "ExecStepConfig", "description": "The ExecStepConfig for all Linux VMs targeted by the PatchJob."}, "windowsExecStepConfig": {"$ref": "ExecStepConfig", "description": "The ExecStepConfig for all Windows VMs targeted by the PatchJob."}}, "type": "object"}, "ExecStepConfig": {"description": "Common configurations for an ExecStep.", "id": "ExecStepConfig", "properties": {"allowedSuccessCodes": {"description": "Defaults to [0]. A list of possible return values that the execution can return to indicate a success.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "gcsObject": {"$ref": "GcsObject", "description": "A Google Cloud Storage object containing the executable."}, "interpreter": {"description": "The script interpreter to use to run the script. If no interpreter is specified the script will be executed directly, which will likely only succeed for scripts with [shebang lines] (https://en.wikipedia.org/wiki/She<PERSON>_\\(Unix\\)).", "enum": ["INTERPRETER_UNSPECIFIED", "NONE", "SHELL", "POWERSHELL"], "enumDescriptions": ["If the interpreter is not specified, the value defaults to `NONE`.", "Indicates that the file is run as follows on each operating system: + For Linux VMs, the file is ran as an executable and the interpreter might be parsed from the [shebang line](https://wikipedia.org/wiki/Shebang_(Unix)) of the file. + For Windows VM, this value is not supported.", "Indicates that the file is run with `/bin/sh` on Linux and `cmd` on Windows.", "Indicates that the file is run with PowerShell."], "type": "string"}, "localPath": {"description": "An absolute path to the executable on the VM.", "type": "string"}}, "type": "object"}, "ExecutePatchJobRequest": {"description": "A request message to initiate patching across Compute Engine instances.", "id": "ExecutePatchJobRequest", "properties": {"description": {"description": "Description of the patch job. Length of the description is limited to 1024 characters.", "type": "string"}, "displayName": {"description": "Display name for this patch job. This does not have to be unique.", "type": "string"}, "dryRun": {"description": "If this patch is a dry-run only, instances are contacted but will do nothing.", "type": "boolean"}, "duration": {"description": "Duration of the patch job. After the duration ends, the patch job times out.", "format": "google-duration", "type": "string"}, "instanceFilter": {"$ref": "PatchInstanceFilter", "description": "Required. Instances to patch, either explicitly or filtered by some criteria such as zone or labels."}, "patchConfig": {"$ref": "PatchConfig", "description": "Patch configuration being applied. If omitted, instances are patched using the default configurations."}, "rollout": {"$ref": "PatchRollout", "description": "Rollout strategy of the patch job."}}, "type": "object"}, "FixedOrPercent": {"description": "Message encapsulating a value that can be either absolute (\"fixed\") or relative (\"percent\") to a value.", "id": "FixedOrPercent", "properties": {"fixed": {"description": "Specifies a fixed value.", "format": "int32", "type": "integer"}, "percent": {"description": "Specifies the relative value defined as a percentage, which will be multiplied by a reference value.", "format": "int32", "type": "integer"}}, "type": "object"}, "GcsObject": {"description": "Google Cloud Storage object representation.", "id": "GcsObject", "properties": {"bucket": {"description": "Required. <PERSON><PERSON> of the Google Cloud Storage object.", "type": "string"}, "generationNumber": {"description": "Required. Generation number of the Google Cloud Storage object. This is used to ensure that the ExecStep specified by this PatchJob does not change.", "format": "int64", "type": "string"}, "object": {"description": "Required. Name of the Google Cloud Storage object.", "type": "string"}}, "type": "object"}, "GooRepository": {"description": "Represents a Goo package repository. These is added to a repo file that is stored at C:/ProgramData/GooGet/repos/google_osconfig.repo.", "id": "GooRepository", "properties": {"name": {"description": "Required. The name of the repository.", "type": "string"}, "url": {"description": "Required. The url of the repository.", "type": "string"}}, "type": "object"}, "GooSettings": {"description": "Googet patching is performed by running `googet update`.", "id": "GooSettings", "properties": {}, "type": "object"}, "GoogleCloudOsconfigV1__OSPolicyAssignmentOperationMetadata": {"description": "OS policy assignment operation metadata provided by OS policy assignment API methods that return long running operations.", "id": "GoogleCloudOsconfigV1__OSPolicyAssignmentOperationMetadata", "properties": {"apiMethod": {"description": "The OS policy assignment API method.", "enum": ["API_METHOD_UNSPECIFIED", "CREATE", "UPDATE", "DELETE"], "enumDescriptions": ["Invalid value", "Create OS policy assignment API method", "Update OS policy assignment API method", "Delete OS policy assignment API method"], "type": "string"}, "osPolicyAssignment": {"description": "Reference to the `OSPolicyAssignment` API resource. Format: `projects/{project_number}/locations/{location}/osPolicyAssignments/{os_policy_assignment_id@revision_id}`", "type": "string"}, "rolloutStartTime": {"description": "Rollout start time", "format": "google-datetime", "type": "string"}, "rolloutState": {"description": "State of the rollout", "enum": ["ROLLOUT_STATE_UNSPECIFIED", "IN_PROGRESS", "CANCELLING", "CANCELLED", "SUCCEEDED"], "enumDescriptions": ["Invalid value", "The rollout is in progress.", "The rollout is being cancelled.", "The rollout is cancelled.", "The rollout has completed successfully."], "type": "string"}, "rolloutUpdateTime": {"description": "Rollout update time", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudOsconfigV2__OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudOsconfigV2__OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudOsconfigV2beta__OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudOsconfigV2beta__OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GuestPolicy": {"description": "An OS Config resource representing a guest configuration policy. These policies represent the desired state for VM instance guest environments including packages to install or remove, package repository configurations, and software to install.", "id": "GuestP<PERSON>y", "properties": {"assignment": {"$ref": "Assignment", "description": "Required. Specifies the VM instances that are assigned to this policy. This allows you to target sets or groups of VM instances by different parameters such as labels, names, OS, or zones. If left empty, all VM instances underneath this policy are targeted. At the same level in the resource hierarchy (that is within a project), the service prevents the creation of multiple policies that conflict with each other. For more information, see how the service [handles assignment conflicts](/compute/docs/os-config-management/create-guest-policy#handle-conflicts)."}, "createTime": {"description": "Output only. Time this guest policy was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Description of the guest policy. Length of the description is limited to 1024 characters.", "type": "string"}, "etag": {"description": "The etag for this guest policy. If this is provided on update, it must match the server's etag.", "type": "string"}, "name": {"description": "Required. Unique name of the resource in this project using one of the following forms: `projects/{project_number}/guestPolicies/{guest_policy_id}`.", "type": "string"}, "packageRepositories": {"description": "A list of package repositories to configure on the VM instance. This is done before any other configs are applied so they can use these repos. Package repositories are only configured if the corresponding package manager(s) are available.", "items": {"$ref": "PackageRepository"}, "type": "array"}, "packages": {"description": "The software packages to be managed by this policy.", "items": {"$ref": "Package"}, "type": "array"}, "recipes": {"description": "A list of Recipes to install on the VM instance.", "items": {"$ref": "SoftwareRecipe"}, "type": "array"}, "updateTime": {"description": "Output only. Last time this guest policy was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ListGuestPoliciesResponse": {"description": "A response message for listing guest policies.", "id": "ListGuestPoliciesResponse", "properties": {"guestPolicies": {"description": "The list of GuestPolicies.", "items": {"$ref": "GuestP<PERSON>y"}, "type": "array"}, "nextPageToken": {"description": "A pagination token that can be used to get the next page of guest policies.", "type": "string"}}, "type": "object"}, "ListPatchDeploymentsResponse": {"description": "A response message for listing patch deployments.", "id": "ListPatchDeploymentsResponse", "properties": {"nextPageToken": {"description": "A pagination token that can be used to get the next page of patch deployments.", "type": "string"}, "patchDeployments": {"description": "The list of patch deployments.", "items": {"$ref": "PatchDeployment"}, "type": "array"}}, "type": "object"}, "ListPatchJobInstanceDetailsResponse": {"description": "A response message for listing the instances details for a patch job.", "id": "ListPatchJobInstanceDetailsResponse", "properties": {"nextPageToken": {"description": "A pagination token that can be used to get the next page of results.", "type": "string"}, "patchJobInstanceDetails": {"description": "A list of instance status.", "items": {"$ref": "PatchJobInstanceDetails"}, "type": "array"}}, "type": "object"}, "ListPatchJobsResponse": {"description": "A response message for listing patch jobs.", "id": "ListPatchJobsResponse", "properties": {"nextPageToken": {"description": "A pagination token that can be used to get the next page of results.", "type": "string"}, "patchJobs": {"description": "The list of patch jobs.", "items": {"$ref": "<PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "LookupEffectiveGuestPolicyRequest": {"description": "A request message for getting the effective guest policy assigned to the instance.", "id": "LookupEffectiveGuestPolicyRequest", "properties": {"osArchitecture": {"description": "Architecture of OS running on the instance. The OS Config agent only provides this field for targeting if OS Inventory is enabled for that instance.", "type": "string"}, "osShortName": {"description": "Short name of the OS running on the instance. The OS Config agent only provides this field for targeting if OS Inventory is enabled for that instance.", "type": "string"}, "osVersion": {"description": "Version of the OS running on the instance. The OS Config agent only provides this field for targeting if OS Inventory is enabled for that VM instance.", "type": "string"}}, "type": "object"}, "MessageSet": {"deprecated": true, "description": "This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW FIELDS. If you are using editions or proto2, please make your own extendable messages for your use case. If you are using proto3, please use `Any` instead. MessageSet was the implementation of extensions for proto1. When proto2 was introduced, extensions were implemented as a first-class feature. This schema for MessageSet was meant to be a \"bridge\" solution to migrate MessageSet-bearing messages from proto1 to proto2. This schema has been open-sourced only to facilitate the migration of Google products with MessageSet-bearing messages to open-source environments.", "id": "MessageSet", "properties": {}, "type": "object"}, "MonthlySchedule": {"description": "Represents a monthly schedule. An example of a valid monthly schedule is \"on the third Tuesday of the month\" or \"on the 15th of the month\".", "id": "MonthlySchedule", "properties": {"monthDay": {"description": "Required. One day of the month. 1-31 indicates the 1st to the 31st day. -1 indicates the last day of the month. Months without the target day will be skipped. For example, a schedule to run \"every month on the 31st\" will not run in February, April, June, etc.", "format": "int32", "type": "integer"}, "weekDayOfMonth": {"$ref": "WeekDayOfMonth", "description": "Required. Week day in a month."}}, "type": "object"}, "OSPolicyAssignmentOperationMetadata": {"description": "OS policy assignment operation metadata provided by OS policy assignment API methods that return long running operations.", "id": "OSPolicyAssignmentOperationMetadata", "properties": {"apiMethod": {"description": "The OS policy assignment API method.", "enum": ["API_METHOD_UNSPECIFIED", "CREATE", "UPDATE", "DELETE"], "enumDescriptions": ["Invalid value", "Create OS policy assignment API method", "Update OS policy assignment API method", "Delete OS policy assignment API method"], "type": "string"}, "osPolicyAssignment": {"description": "Reference to the `OSPolicyAssignment` API resource. Format: `projects/{project_number}/locations/{location}/osPolicyAssignments/{os_policy_assignment_id@revision_id}`", "type": "string"}, "rolloutStartTime": {"description": "Rollout start time", "format": "google-datetime", "type": "string"}, "rolloutState": {"description": "State of the rollout", "enum": ["ROLLOUT_STATE_UNSPECIFIED", "IN_PROGRESS", "CANCELLING", "CANCELLED", "SUCCEEDED"], "enumDescriptions": ["Invalid value", "The rollout is in progress.", "The rollout is being cancelled.", "The rollout is cancelled.", "The rollout has completed successfully."], "type": "string"}, "rolloutUpdateTime": {"description": "Rollout update time", "format": "google-datetime", "type": "string"}}, "type": "object"}, "OneTimeSchedule": {"description": "Sets the time for a one time patch deployment. Timestamp is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.", "id": "OneTimeSchedule", "properties": {"executeTime": {"description": "Required. The desired patch job execution time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Package": {"description": "Package is a reference to the software package to be installed or removed. The agent on the VM instance uses the system package manager to apply the config. These are the commands that the agent uses to install or remove packages. Apt install: `apt-get update && apt-get -y install package1 package2 package3` remove: `apt-get -y remove package1 package2 package3` Yum install: `yum -y install package1 package2 package3` remove: `yum -y remove package1 package2 package3` Zypper install: `zypper install package1 package2 package3` remove: `zypper rm package1 package2` Googet install: `googet -noconfirm install package1 package2 package3` remove: `googet -noconfirm remove package1 package2 package3`", "id": "Package", "properties": {"desiredState": {"description": "The desired_state the agent should maintain for this package. The default is to ensure the package is installed.", "enum": ["DESIRED_STATE_UNSPECIFIED", "INSTALLED", "UPDATED", "REMOVED"], "enumDescriptions": ["The default is to ensure the package is installed.", "The agent ensures that the package is installed.", "The agent ensures that the package is installed and periodically checks for and install any updates.", "The agent ensures that the package is not installed and uninstall it if detected."], "type": "string"}, "manager": {"description": "Type of package manager that can be used to install this package. If a system does not have the package manager, the package is not installed or removed no error message is returned. By default, or if you specify `ANY`, the agent attempts to install and remove this package using the default package manager. This is useful when creating a policy that applies to different types of systems. The default behavior is ANY.", "enum": ["MANAGER_UNSPECIFIED", "ANY", "APT", "YUM", "ZYPPER", "GOO"], "enumDescriptions": ["The default behavior is ANY.", "Apply this package config using the default system package manager.", "Apply this package config only if Apt is available on the system.", "Apply this package config only if Yum is available on the system.", "Apply this package config only if <PERSON><PERSON><PERSON> is available on the system.", "Apply this package config only if GooGet is available on the system."], "type": "string"}, "name": {"description": "Required. The name of the package. A package is uniquely identified for conflict validation by checking the package name and the manager(s) that the package targets.", "type": "string"}}, "type": "object"}, "PackageRepository": {"description": "A package repository.", "id": "PackageRepository", "properties": {"apt": {"$ref": "AptRepository", "description": "An Apt Repository."}, "goo": {"$ref": "GooRepository", "description": "A Goo Repository."}, "yum": {"$ref": "YumRepository", "description": "A Yum Repository."}, "zypper": {"$ref": "ZypperRepository", "description": "A Zypper Repository."}}, "type": "object"}, "PatchConfig": {"description": "Patch configuration specifications. Contains details on how to apply the patch(es) to a VM instance.", "id": "PatchConfig", "properties": {"apt": {"$ref": "AptSettings", "description": "Apt update settings. Use this setting to override the default `apt` patch rules."}, "goo": {"$ref": "GooSettings", "description": "Goo update settings. Use this setting to override the default `goo` patch rules."}, "migInstancesAllowed": {"description": "Allows the patch job to run on Managed instance groups (MIGs).", "type": "boolean"}, "postStep": {"$ref": "ExecStep", "description": "The `ExecStep` to run after the patch update."}, "preStep": {"$ref": "ExecStep", "description": "The `ExecStep` to run before the patch update."}, "rebootConfig": {"description": "Post-patch reboot settings.", "enum": ["REBOOT_CONFIG_UNSPECIFIED", "DEFAULT", "ALWAYS", "NEVER"], "enumDescriptions": ["The default behavior is DEFAULT.", "The agent decides if a reboot is necessary by checking signals such as registry keys on Windows or `/var/run/reboot-required` on APT based systems. On RPM based systems, a set of core system package install times are compared with system boot time.", "Always reboot the machine after the update completes.", "Never reboot the machine after the update completes."], "type": "string"}, "windowsUpdate": {"$ref": "WindowsUpdateSettings", "description": "Windows update settings. Use this override the default windows patch rules."}, "yum": {"$ref": "YumSettings", "description": "Yum update settings. Use this setting to override the default `yum` patch rules."}, "zypper": {"$ref": "ZypperSettings", "description": "Zypper update settings. Use this setting to override the default `zypper` patch rules."}}, "type": "object"}, "PatchDeployment": {"description": "Patch deployments are configurations that individual patch jobs use to complete a patch. These configurations include instance filter, package repository settings, and a schedule. For more information about creating and managing patch deployments, see [Scheduling patch jobs](https://cloud.google.com/compute/docs/os-patch-management/schedule-patch-jobs).", "id": "PatchDeployment", "properties": {"createTime": {"description": "Output only. Time the patch deployment was created. Timestamp is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Description of the patch deployment. Length of the description is limited to 1024 characters.", "type": "string"}, "duration": {"description": "Optional. Duration of the patch. After the duration ends, the patch times out.", "format": "google-duration", "type": "string"}, "instanceFilter": {"$ref": "PatchInstanceFilter", "description": "Required. VM instances to patch."}, "lastExecuteTime": {"description": "Output only. The last time a patch job was started by this deployment. Timestamp is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Unique name for the patch deployment resource in a project. The patch deployment name is in the form: `projects/{project_id}/patchDeployments/{patch_deployment_id}`. This field is ignored when you create a new patch deployment.", "type": "string"}, "oneTimeSchedule": {"$ref": "OneTimeSchedule", "description": "Required. Schedule a one-time execution."}, "patchConfig": {"$ref": "PatchConfig", "description": "Optional. Patch configuration that is applied."}, "recurringSchedule": {"$ref": "RecurringSchedule", "description": "Required. Schedule recurring executions."}, "rollout": {"$ref": "PatchRollout", "description": "Optional. Rollout strategy of the patch job."}, "state": {"description": "Output only. Current state of the patch deployment.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "PAUSED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "Active value means that patch deployment generates Patch Jobs.", "Paused value means that patch deployment does not generate Patch jobs. Requires user action to move in and out from this state."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time the patch deployment was last updated. Timestamp is in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "PatchInstanceFilter": {"description": "A filter to target VM instances for patching. The targeted VMs must meet all criteria specified. So if both labels and zones are specified, the patch job targets only VMs with those labels and in those zones.", "id": "PatchInstanceFilter", "properties": {"all": {"description": "Target all VM instances in the project. If true, no other criteria is permitted.", "type": "boolean"}, "groupLabels": {"description": "Targets VM instances matching at least one of these label sets. This allows targeting of disparate groups, for example \"env=prod or env=staging\".", "items": {"$ref": "PatchInstanceFilterGroupLabel"}, "type": "array"}, "instanceNamePrefixes": {"description": "Targets VMs whose name starts with one of these prefixes. Similar to labels, this is another way to group VMs when targeting configs, for example prefix=\"prod-\".", "items": {"type": "string"}, "type": "array"}, "instances": {"description": "Targets any of the VM instances specified. Instances are specified by their URI in the form `zones/[ZONE]/instances/[INSTANCE_NAME]`, `projects/[PROJECT_ID]/zones/[ZONE]/instances/[INSTANCE_NAME]`, or `https://www.googleapis.com/compute/v1/projects/[PROJECT_ID]/zones/[ZONE]/instances/[INSTANCE_NAME]`", "items": {"type": "string"}, "type": "array"}, "zones": {"description": "Targets VM instances in ANY of these zones. Leave empty to target VM instances in any zone.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PatchInstanceFilterGroupLabel": {"description": "Represents a group of VMs that can be identified as having all these labels, for example \"env=prod and app=web\".", "id": "PatchInstanceFilterGroupLabel", "properties": {"labels": {"additionalProperties": {"type": "string"}, "description": "Compute Engine instance labels that must be present for a VM instance to be targeted by this filter.", "type": "object"}}, "type": "object"}, "PatchJob": {"description": "A high level representation of a patch job that is either in progress or has completed. Instance details are not included in the job. To paginate through instance details, use `ListPatchJobInstanceDetails`. For more information about patch jobs, see [Creating patch jobs](https://cloud.google.com/compute/docs/os-patch-management/create-patch-job).", "id": "<PERSON><PERSON><PERSON>", "properties": {"createTime": {"description": "Time this patch job was created.", "format": "google-datetime", "type": "string"}, "description": {"description": "Description of the patch job. Length of the description is limited to 1024 characters.", "type": "string"}, "displayName": {"description": "Display name for this patch job. This is not a unique identifier.", "type": "string"}, "dryRun": {"description": "If this patch job is a dry run, the agent reports that it has finished without running any updates on the VM instance.", "type": "boolean"}, "duration": {"description": "Duration of the patch job. After the duration ends, the patch job times out.", "format": "google-duration", "type": "string"}, "errorMessage": {"description": "If this patch job failed, this message provides information about the failure.", "type": "string"}, "instanceDetailsSummary": {"$ref": "PatchJobInstanceDetailsSummary", "description": "Summary of instance details."}, "instanceFilter": {"$ref": "PatchInstanceFilter", "description": "Instances to patch."}, "name": {"description": "Unique identifier for this patch job in the form `projects/*/patchJobs/*`", "type": "string"}, "patchConfig": {"$ref": "PatchConfig", "description": "Patch configuration being applied."}, "patchDeployment": {"description": "Output only. Name of the patch deployment that created this patch job.", "readOnly": true, "type": "string"}, "percentComplete": {"description": "Reflects the overall progress of the patch job in the range of 0.0 being no progress to 100.0 being complete.", "format": "double", "type": "number"}, "rollout": {"$ref": "PatchRollout", "description": "Rollout strategy being applied."}, "state": {"description": "The current state of the PatchJob.", "enum": ["STATE_UNSPECIFIED", "STARTED", "INSTANCE_LOOKUP", "PATCHING", "SUCCEEDED", "COMPLETED_WITH_ERRORS", "CANCELED", "TIMED_OUT"], "enumDescriptions": ["State must be specified.", "The patch job was successfully initiated.", "The patch job is looking up instances to run the patch on.", "Instances are being patched.", "Patch job completed successfully.", "Patch job completed but there were errors.", "The patch job was canceled.", "The patch job timed out."], "type": "string"}, "updateTime": {"description": "Last time this patch job was updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "PatchJobInstanceDetails": {"description": "Patch details for a VM instance. For more information about reviewing VM instance details, see [Listing all VM instance details for a specific patch job](https://cloud.google.com/compute/docs/os-patch-management/manage-patch-jobs#list-instance-details).", "id": "PatchJobInstanceDetails", "properties": {"attemptCount": {"description": "The number of times the agent that the agent attempts to apply the patch.", "format": "int64", "type": "string"}, "failureReason": {"description": "If the patch fails, this field provides the reason.", "type": "string"}, "instanceSystemId": {"description": "The unique identifier for the instance. This identifier is defined by the server.", "type": "string"}, "name": {"description": "The instance name in the form `projects/*/zones/*/instances/*`", "type": "string"}, "state": {"description": "Current state of instance patch.", "enum": ["PATCH_STATE_UNSPECIFIED", "PENDING", "INACTIVE", "NOTIFIED", "STARTED", "DOWNLOADING_PATCHES", "APPLYING_PATCHES", "REBOOTING", "SUCCEEDED", "SUCCEEDED_REBOOT_REQUIRED", "FAILED", "ACKED", "TIMED_OUT", "RUNNING_PRE_PATCH_STEP", "RUNNING_POST_PATCH_STEP", "NO_AGENT_DETECTED"], "enumDescriptions": ["Unspecified.", "The instance is not yet notified.", "Instance is inactive and cannot be patched.", "The instance is notified that it should be patched.", "The instance has started the patching process.", "The instance is downloading patches.", "The instance is applying patches.", "The instance is rebooting.", "The instance has completed applying patches.", "The instance has completed applying patches but a reboot is required.", "The instance has failed to apply the patch.", "The instance acked the notification and will start shortly.", "The instance exceeded the time out while applying the patch.", "The instance is running the pre-patch step.", "The instance is running the post-patch step.", "The service could not detect the presence of the agent. Check to ensure that the agent is installed, running, and able to communicate with the service."], "type": "string"}}, "type": "object"}, "PatchJobInstanceDetailsSummary": {"description": "A summary of the current patch state across all instances that this patch job affects. Contains counts of instances in different states. These states map to `InstancePatchState`. List patch job instance details to see the specific states of each instance.", "id": "PatchJobInstanceDetailsSummary", "properties": {"ackedInstanceCount": {"description": "Number of instances that have acked and will start shortly.", "format": "int64", "type": "string"}, "applyingPatchesInstanceCount": {"description": "Number of instances that are applying patches.", "format": "int64", "type": "string"}, "downloadingPatchesInstanceCount": {"description": "Number of instances that are downloading patches.", "format": "int64", "type": "string"}, "failedInstanceCount": {"description": "Number of instances that failed.", "format": "int64", "type": "string"}, "inactiveInstanceCount": {"description": "Number of instances that are inactive.", "format": "int64", "type": "string"}, "noAgentDetectedInstanceCount": {"description": "Number of instances that do not appear to be running the agent. Check to ensure that the agent is installed, running, and able to communicate with the service.", "format": "int64", "type": "string"}, "notifiedInstanceCount": {"description": "Number of instances notified about patch job.", "format": "int64", "type": "string"}, "pendingInstanceCount": {"description": "Number of instances pending patch job.", "format": "int64", "type": "string"}, "postPatchStepInstanceCount": {"description": "Number of instances that are running the post-patch step.", "format": "int64", "type": "string"}, "prePatchStepInstanceCount": {"description": "Number of instances that are running the pre-patch step.", "format": "int64", "type": "string"}, "rebootingInstanceCount": {"description": "Number of instances rebooting.", "format": "int64", "type": "string"}, "startedInstanceCount": {"description": "Number of instances that have started.", "format": "int64", "type": "string"}, "succeededInstanceCount": {"description": "Number of instances that have completed successfully.", "format": "int64", "type": "string"}, "succeededRebootRequiredInstanceCount": {"description": "Number of instances that require reboot.", "format": "int64", "type": "string"}, "timedOutInstanceCount": {"description": "Number of instances that exceeded the time out while applying the patch.", "format": "int64", "type": "string"}}, "type": "object"}, "PatchRollout": {"description": "Patch rollout configuration specifications. Contains details on the concurrency control when applying patch(es) to all targeted VMs.", "id": "PatchRollout", "properties": {"disruptionBudget": {"$ref": "FixedOrPercent", "description": "The maximum number (or percentage) of VMs per zone to disrupt at any given moment. The number of VMs calculated from multiplying the percentage by the total number of VMs in a zone is rounded up. During patching, a VM is considered disrupted from the time the agent is notified to begin until patching has completed. This disruption time includes the time to complete reboot and any post-patch steps. A VM contributes to the disruption budget if its patching operation fails either when applying the patches, running pre or post patch steps, or if it fails to respond with a success notification before timing out. VMs that are not running or do not have an active agent do not count toward this disruption budget. For zone-by-zone rollouts, if the disruption budget in a zone is exceeded, the patch job stops, because continuing to the next zone requires completion of the patch process in the previous zone. For example, if the disruption budget has a fixed value of `10`, and 8 VMs fail to patch in the current zone, the patch job continues to patch 2 VMs at a time until the zone is completed. When that zone is completed successfully, patching begins with 10 VMs at a time in the next zone. If 10 VMs in the next zone fail to patch, the patch job stops."}, "mode": {"description": "Mode of the patch rollout.", "enum": ["MODE_UNSPECIFIED", "ZONE_BY_ZONE", "CONCURRENT_ZONES"], "enumDescriptions": ["Mode must be specified.", "Patches are applied one zone at a time. The patch job begins in the region with the lowest number of targeted VMs. Within the region, patching begins in the zone with the lowest number of targeted VMs. If multiple regions (or zones within a region) have the same number of targeted VMs, a tie-breaker is achieved by sorting the regions or zones in alphabetical order.", "Patches are applied to VMs in all zones at the same time."], "type": "string"}}, "type": "object"}, "PausePatchDeploymentRequest": {"description": "A request message for pausing a patch deployment.", "id": "PausePatchDeploymentRequest", "properties": {}, "type": "object"}, "RecurringSchedule": {"description": "Sets the time for recurring patch deployments.", "id": "RecurringSchedule", "properties": {"endTime": {"description": "Optional. The end time at which a recurring patch deployment schedule is no longer active.", "format": "google-datetime", "type": "string"}, "frequency": {"description": "Required. The frequency unit of this recurring schedule.", "enum": ["FREQUENCY_UNSPECIFIED", "WEEKLY", "MONTHLY", "DAILY"], "enumDescriptions": ["Invalid. A frequency must be specified.", "Indicates that the frequency of recurrence should be expressed in terms of weeks.", "Indicates that the frequency of recurrence should be expressed in terms of months.", "Indicates that the frequency of recurrence should be expressed in terms of days."], "type": "string"}, "lastExecuteTime": {"description": "Output only. The time the last patch job ran successfully.", "format": "google-datetime", "readOnly": true, "type": "string"}, "monthly": {"$ref": "MonthlySchedule", "description": "Required. Schedule with monthly executions."}, "nextExecuteTime": {"description": "Output only. The time the next patch job is scheduled to run.", "format": "google-datetime", "readOnly": true, "type": "string"}, "startTime": {"description": "Optional. The time that the recurring schedule becomes effective. Defaults to `create_time` of the patch deployment.", "format": "google-datetime", "type": "string"}, "timeOfDay": {"$ref": "TimeOfDay", "description": "Required. Time of the day to run a recurring deployment."}, "timeZone": {"$ref": "TimeZone", "description": "Required. Defines the time zone that `time_of_day` is relative to. The rules for daylight saving time are determined by the chosen time zone."}, "weekly": {"$ref": "WeeklySchedule", "description": "Required. Schedule with weekly executions."}}, "type": "object"}, "ResumePatchDeploymentRequest": {"description": "A request message for resuming a patch deployment.", "id": "ResumePatchDeploymentRequest", "properties": {}, "type": "object"}, "SoftwareRecipe": {"description": "A software recipe is a set of instructions for installing and configuring a piece of software. It consists of a set of artifacts that are downloaded, and a set of steps that install, configure, and/or update the software. Recipes support installing and updating software from artifacts in the following formats: Zip archive, Tar archive, Windows MSI, Debian package, and RPM package. Additionally, recipes support executing a script (either defined in a file or directly in this api) in bash, sh, cmd, and powershell. Updating a software recipe If a recipe is assigned to an instance and there is a recipe with the same name but a lower version already installed and the assigned state of the recipe is `UPDATED`, then the recipe is updated to the new version. Script Working Directories Each script or execution step is run in its own temporary directory which is deleted after completing the step.", "id": "SoftwareRecipe", "properties": {"artifacts": {"description": "Resources available to be used in the steps in the recipe.", "items": {"$ref": "SoftwareRecipeArtifact"}, "type": "array"}, "desiredState": {"description": "Default is INSTALLED. The desired state the agent should maintain for this recipe. INSTALLED: The software recipe is installed on the instance but won't be updated to new versions. UPDATED: The software recipe is installed on the instance. The recipe is updated to a higher version, if a higher version of the recipe is assigned to this instance. REMOVE: Remove is unsupported for software recipes and attempts to create or update a recipe to the REMOVE state is rejected.", "enum": ["DESIRED_STATE_UNSPECIFIED", "INSTALLED", "UPDATED", "REMOVED"], "enumDescriptions": ["The default is to ensure the package is installed.", "The agent ensures that the package is installed.", "The agent ensures that the package is installed and periodically checks for and install any updates.", "The agent ensures that the package is not installed and uninstall it if detected."], "type": "string"}, "installSteps": {"description": "Actions to be taken for installing this recipe. On failure it stops executing steps and does not attempt another installation. Any steps taken (including partially completed steps) are not rolled back.", "items": {"$ref": "SoftwareRecipeStep"}, "type": "array"}, "name": {"description": "Required. Unique identifier for the recipe. Only one recipe with a given name is installed on an instance. Names are also used to identify resources which helps to determine whether guest policies have conflicts. This means that requests to create multiple recipes with the same name and version are rejected since they could potentially have conflicting assignments.", "type": "string"}, "updateSteps": {"description": "Actions to be taken for updating this recipe. On failure it stops executing steps and does not attempt another update for this recipe. Any steps taken (including partially completed steps) are not rolled back.", "items": {"$ref": "SoftwareRecipeStep"}, "type": "array"}, "version": {"description": "The version of this software recipe. Version can be up to 4 period separated numbers (e.g. ***********).", "type": "string"}}, "type": "object"}, "SoftwareRecipeArtifact": {"description": "Specifies a resource to be used in the recipe.", "id": "SoftwareRecipeArtifact", "properties": {"allowInsecure": {"description": "Defaults to false. When false, recipes are subject to validations based on the artifact type: Remote: A checksum must be specified, and only protocols with transport-layer security are permitted. GCS: An object generation number must be specified.", "type": "boolean"}, "gcs": {"$ref": "SoftwareRecipeArtifactGcs", "description": "A Google Cloud Storage artifact."}, "id": {"description": "Required. Id of the artifact, which the installation and update steps of this recipe can reference. Artifacts in a recipe cannot have the same id.", "type": "string"}, "remote": {"$ref": "SoftwareRecipeArtifactRemote", "description": "A generic remote artifact."}}, "type": "object"}, "SoftwareRecipeArtifactGcs": {"description": "Specifies an artifact available as a Google Cloud Storage object.", "id": "SoftwareRecipeArtifactGcs", "properties": {"bucket": {"description": "Bucket of the Google Cloud Storage object. Given an example URL: `https://storage.googleapis.com/my-bucket/foo/bar#1234567` this value would be `my-bucket`.", "type": "string"}, "generation": {"description": "Must be provided if allow_insecure is false. Generation number of the Google Cloud Storage object. `https://storage.googleapis.com/my-bucket/foo/bar#1234567` this value would be `1234567`.", "format": "int64", "type": "string"}, "object": {"description": "Name of the Google Cloud Storage object. As specified [here] (https://cloud.google.com/storage/docs/naming#objectnames) Given an example URL: `https://storage.googleapis.com/my-bucket/foo/bar#1234567` this value would be `foo/bar`.", "type": "string"}}, "type": "object"}, "SoftwareRecipeArtifactRemote": {"description": "Specifies an artifact available via some URI.", "id": "SoftwareRecipeArtifactRemote", "properties": {"checksum": {"description": "Must be provided if `allow_insecure` is `false`. SHA256 checksum in hex format, to compare to the checksum of the artifact. If the checksum is not empty and it doesn't match the artifact then the recipe installation fails before running any of the steps.", "type": "string"}, "uri": {"description": "URI from which to fetch the object. It should contain both the protocol and path following the format {protocol}://{location}.", "type": "string"}}, "type": "object"}, "SoftwareRecipeStep": {"description": "An action that can be taken as part of installing or updating a recipe.", "id": "SoftwareRecipeStep", "properties": {"archiveExtraction": {"$ref": "SoftwareRecipeStepExtractArchive", "description": "Extracts an archive into the specified directory."}, "dpkgInstallation": {"$ref": "SoftwareRecipeStepInstallDpkg", "description": "Installs a deb file via dpkg."}, "fileCopy": {"$ref": "SoftwareRecipeStepCopyFile", "description": "Copies a file onto the instance."}, "fileExec": {"$ref": "SoftwareRecipeStepExecFile", "description": "Executes an artifact or local file."}, "msiInstallation": {"$ref": "SoftwareRecipeStepInstallMsi", "description": "Installs an MSI file."}, "rpmInstallation": {"$ref": "SoftwareRecipeStepInstallRpm", "description": "Installs an rpm file via the rpm utility."}, "scriptRun": {"$ref": "SoftwareRecipeStepRunScript", "description": "Runs commands in a shell."}}, "type": "object"}, "SoftwareRecipeStepCopyFile": {"description": "Copies the artifact to the specified path on the instance.", "id": "SoftwareRecipeStepCopyFile", "properties": {"artifactId": {"description": "Required. The id of the relevant artifact in the recipe.", "type": "string"}, "destination": {"description": "Required. The absolute path on the instance to put the file.", "type": "string"}, "overwrite": {"description": "Whether to allow this step to overwrite existing files. If this is false and the file already exists the file is not overwritten and the step is considered a success. Defaults to false.", "type": "boolean"}, "permissions": {"description": "Consists of three octal digits which represent, in order, the permissions of the owner, group, and other users for the file (similarly to the numeric mode used in the linux chmod utility). Each digit represents a three bit number with the 4 bit corresponding to the read permissions, the 2 bit corresponds to the write bit, and the one bit corresponds to the execute permission. Default behavior is 755. Below are some examples of permissions and their associated values: read, write, and execute: 7 read and execute: 5 read and write: 6 read only: 4", "type": "string"}}, "type": "object"}, "SoftwareRecipeStepExecFile": {"description": "Executes an artifact or local file.", "id": "SoftwareRecipeStepExecFile", "properties": {"allowedExitCodes": {"description": "Defaults to [0]. A list of possible return values that the program can return to indicate a success.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "args": {"description": "Arguments to be passed to the provided executable.", "items": {"type": "string"}, "type": "array"}, "artifactId": {"description": "The id of the relevant artifact in the recipe.", "type": "string"}, "localPath": {"description": "The absolute path of the file on the local filesystem.", "type": "string"}}, "type": "object"}, "SoftwareRecipeStepExtractArchive": {"description": "Extracts an archive of the type specified in the specified directory.", "id": "SoftwareRecipeStepExtractArchive", "properties": {"artifactId": {"description": "Required. The id of the relevant artifact in the recipe.", "type": "string"}, "destination": {"description": "Directory to extract archive to. Defaults to `/` on Linux or `C:\\` on Windows.", "type": "string"}, "type": {"description": "Required. The type of the archive to extract.", "enum": ["ARCHIVE_TYPE_UNSPECIFIED", "TAR", "TAR_GZIP", "TAR_BZIP", "TAR_LZMA", "TAR_XZ", "ZIP"], "enumDescriptions": ["Indicates that the archive type isn't specified.", "Indicates that the archive is a tar archive with no encryption.", "Indicates that the archive is a tar archive with gzip encryption.", "Indicates that the archive is a tar archive with bzip encryption.", "Indicates that the archive is a tar archive with lzma encryption.", "Indicates that the archive is a tar archive with xz encryption.", "Indicates that the archive is a zip archive."], "type": "string"}}, "type": "object"}, "SoftwareRecipeStepInstallDpkg": {"description": "Installs a deb via dpkg.", "id": "SoftwareRecipeStepInstallDpkg", "properties": {"artifactId": {"description": "Required. The id of the relevant artifact in the recipe.", "type": "string"}}, "type": "object"}, "SoftwareRecipeStepInstallMsi": {"description": "Installs an MSI file.", "id": "SoftwareRecipeStepInstallMsi", "properties": {"allowedExitCodes": {"description": "Return codes that indicate that the software installed or updated successfully. Behaviour defaults to [0]", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "artifactId": {"description": "Required. The id of the relevant artifact in the recipe.", "type": "string"}, "flags": {"description": "The flags to use when installing the MSI defaults to [\"/i\"] (i.e. the install flag).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SoftwareRecipeStepInstallRpm": {"description": "Installs an rpm file via the rpm utility.", "id": "SoftwareRecipeStepInstallRpm", "properties": {"artifactId": {"description": "Required. The id of the relevant artifact in the recipe.", "type": "string"}}, "type": "object"}, "SoftwareRecipeStepRunScript": {"description": "Runs a script through an interpreter.", "id": "SoftwareRecipeStepRunScript", "properties": {"allowedExitCodes": {"description": "Return codes that indicate that the software installed or updated successfully. Behaviour defaults to [0]", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "interpreter": {"description": "The script interpreter to use to run the script. If no interpreter is specified the script is executed directly, which likely only succeed for scripts with [shebang lines](https://en.wikipedia.org/wiki/Shebang_\\(Unix\\)).", "enum": ["INTERPRETER_UNSPECIFIED", "SHELL", "POWERSHELL"], "enumDescriptions": ["Default value for ScriptType.", "Indicates that the script is run with `/bin/sh` on Linux and `cmd` on windows.", "Indicates that the script is run with powershell."], "type": "string"}, "script": {"description": "Required. The shell script to be executed.", "type": "string"}}, "type": "object"}, "StatusProto": {"description": "Wire-format for a Status object", "id": "StatusProto", "properties": {"canonicalCode": {"description": "copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 canonical_code = 6;", "format": "int32", "type": "integer"}, "code": {"description": "Numeric code drawn from the space specified below. Often, this is the canonical error space, and code is drawn from google3/util/task/codes.proto copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional int32 code = 1;", "format": "int32", "type": "integer"}, "message": {"description": "Detail message copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional string message = 3;", "type": "string"}, "messageSet": {"$ref": "MessageSet", "description": "message_set associates an arbitrary proto message with the status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace optional proto2.bridge.MessageSet message_set = 5;"}, "space": {"description": "copybara:strip_begin(b/383363683) Space to which this status belongs copybara:strip_end_and_replace optional string space = 2; // Space to which this status belongs", "type": "string"}}, "type": "object"}, "TimeOfDay": {"description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.", "id": "TimeOfDay", "properties": {"hours": {"description": "Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.", "format": "int32", "type": "integer"}, "nanos": {"description": "Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.", "format": "int32", "type": "integer"}, "seconds": {"description": "Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}}, "type": "object"}, "TimeZone": {"description": "Represents a time zone from the [IANA Time Zone Database](https://www.iana.org/time-zones).", "id": "TimeZone", "properties": {"id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "type": "string"}, "version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "type": "string"}}, "type": "object"}, "WeekDayOfMonth": {"description": "Represents one week day in a month. An example is \"the 4th Sunday\".", "id": "WeekDayOfMonth", "properties": {"dayOfWeek": {"description": "Required. A day of the week.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "dayOffset": {"description": "Optional. Represents the number of days before or after the given week day of month that the patch deployment is scheduled for. For example if `week_ordinal` and `day_of_week` values point to the second day of the month and this `day_offset` value is set to `3`, the patch deployment takes place three days after the second Tuesday of the month. If this value is negative, for example -5, the patches are deployed five days before before the second Tuesday of the month. Allowed values are in range [-30, 30].", "format": "int32", "type": "integer"}, "weekOrdinal": {"description": "Required. Week number in a month. 1-4 indicates the 1st to 4th week of the month. -1 indicates the last week of the month.", "format": "int32", "type": "integer"}}, "type": "object"}, "WeeklySchedule": {"description": "Represents a weekly schedule.", "id": "WeeklySchedule", "properties": {"dayOfWeek": {"description": "Required. Day of the week.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}}, "type": "object"}, "WindowsUpdateSettings": {"description": "Windows patching is performed using the Windows Update Agent.", "id": "WindowsUpdateSettings", "properties": {"classifications": {"description": "Only apply updates of these windows update classifications. If empty, all updates are applied.", "items": {"enum": ["CLASSIFICATION_UNSPECIFIED", "CRITICAL", "SECURITY", "DEFINITION", "DRIVER", "FEATURE_PACK", "SERVICE_PACK", "TOOL", "UPDATE_ROLLUP", "UPDATE"], "enumDescriptions": ["Invalid. If classifications are included, they must be specified.", "\"A widely released fix for a specific problem that addresses a critical, non-security-related bug.\" [1]", "\"A widely released fix for a product-specific, security-related vulnerability. Security vulnerabilities are rated by their severity. The severity rating is indicated in the Microsoft security bulletin as critical, important, moderate, or low.\" [1]", "\"A widely released and frequent software update that contains additions to a product's definition database. Definition databases are often used to detect objects that have specific attributes, such as malicious code, phishing websites, or junk mail.\" [1]", "\"Software that controls the input and output of a device.\" [1]", "\"New product functionality that is first distributed outside the context of a product release and that is typically included in the next full product release.\" [1]", "\"A tested, cumulative set of all hotfixes, security updates, critical updates, and updates. Additionally, service packs may contain additional fixes for problems that are found internally since the release of the product. Service packs my also contain a limited number of customer-requested design changes or features.\" [1]", "\"A utility or feature that helps complete a task or set of tasks.\" [1]", "\"A tested, cumulative set of hotfixes, security updates, critical updates, and updates that are packaged together for easy deployment. A rollup generally targets a specific area, such as security, or a component of a product, such as Internet Information Services (IIS).\" [1]", "\"A widely released fix for a specific problem. An update addresses a noncritical, non-security-related bug.\" [1]"], "type": "string"}, "type": "array"}, "excludes": {"description": "List of KBs to exclude from update.", "items": {"type": "string"}, "type": "array"}, "exclusivePatches": {"description": "An exclusive list of kbs to be updated. These are the only patches that will be updated. This field must not be used with other patch configurations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "YumRepository": {"description": "Represents a single Yum package repository. This repository is added to a repo file that is stored at `/etc/yum.repos.d/google_osconfig.repo`.", "id": "YumRepository", "properties": {"baseUrl": {"description": "Required. The location of the repository directory.", "type": "string"}, "displayName": {"description": "The display name of the repository.", "type": "string"}, "gpgKeys": {"description": "URIs of GPG keys.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Required. A one word, unique name for this repository. This is the `repo id` in the Yum config file and also the `display_name` if `display_name` is omitted. This id is also used as the unique identifier when checking for guest policy conflicts.", "type": "string"}}, "type": "object"}, "YumSettings": {"description": "Yum patching is performed by executing `yum update`. Additional options can be set to control how this is executed. Note that not all settings are supported on all platforms.", "id": "YumSettings", "properties": {"excludes": {"description": "List of packages to exclude from update. These packages are excluded by using the yum `--exclude` flag.", "items": {"type": "string"}, "type": "array"}, "exclusivePackages": {"description": "An exclusive list of packages to be updated. These are the only packages that will be updated. If these packages are not installed, they will be ignored. This field must not be specified with any other patch configuration fields.", "items": {"type": "string"}, "type": "array"}, "minimal": {"description": "Will cause patch to run `yum update-minimal` instead.", "type": "boolean"}, "security": {"description": "Adds the `--security` flag to `yum update`. Not supported on all platforms.", "type": "boolean"}}, "type": "object"}, "ZypperRepository": {"description": "Represents a single Zypper package repository. This repository is added to a repo file that is stored at `/etc/zypp/repos.d/google_osconfig.repo`.", "id": "ZypperRepository", "properties": {"baseUrl": {"description": "Required. The location of the repository directory.", "type": "string"}, "displayName": {"description": "The display name of the repository.", "type": "string"}, "gpgKeys": {"description": "URIs of GPG keys.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Required. A one word, unique name for this repository. This is the `repo id` in the zypper config file and also the `display_name` if `display_name` is omitted. This id is also used as the unique identifier when checking for guest policy conflicts.", "type": "string"}}, "type": "object"}, "ZypperSettings": {"description": "Zypper patching is performed by running `zypper patch`. See also https://en.opensuse.org/SDB:Zypper_manual.", "id": "ZypperSettings", "properties": {"categories": {"description": "Install only patches with these categories. Common categories include security, recommended, and feature.", "items": {"type": "string"}, "type": "array"}, "excludes": {"description": "List of patches to exclude from update.", "items": {"type": "string"}, "type": "array"}, "exclusivePatches": {"description": "An exclusive list of patches to be updated. These are the only patches that will be installed using 'zypper patch patch:' command. This field must not be used with any other patch configuration fields.", "items": {"type": "string"}, "type": "array"}, "severities": {"description": "Install only patches with these severities. Common severities include critical, important, moderate, and low.", "items": {"type": "string"}, "type": "array"}, "withOptional": {"description": "Adds the `--with-optional` flag to `zypper patch`.", "type": "boolean"}, "withUpdate": {"description": "Adds the `--with-update` flag, to `zypper patch`.", "type": "boolean"}}, "type": "object"}}, "servicePath": "", "title": "OS Config API", "version": "v1beta", "version_module": true}