Metadata-Version: 2.1
Name: earthengine-api
Version: 1.0.0
Summary: Earth Engine Python API
Author-email: Google LLC <<EMAIL>>
License: Apache-2.0
Project-URL: Homepage, http://code.google.com/p/earthengine-api
Keywords: earth engine,image analysis
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python
Classifier: Topic :: Multimedia :: Graphics :: Viewers
Classifier: Topic :: Scientific/Engineering :: GIS
Classifier: Topic :: Scientific/Engineering :: Image Processing
Classifier: Topic :: Scientific/Engineering :: Visualization
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: google-cloud-storage
Requires-Dist: google-api-python-client>=1.12.1
Requires-Dist: google-auth>=1.4.1
Requires-Dist: google-auth-httplib2>=0.0.3
Requires-Dist: httplib2<1dev,>=0.9.2
Requires-Dist: requests
Provides-Extra: tests
Requires-Dist: absl-py; extra == "tests"
Requires-Dist: geopandas; extra == "tests"
Requires-Dist: numpy; extra == "tests"

Visit the [Google Earth Engine Python installation page](https://developers.google.com/earth-engine/python_install)
for set up instructions.
