"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/EventContracts.sol:EventContract
EVENT_CONTRACT_BYTECODE = "0x6080604052348015600e575f5ffd5b5061017a8061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c80635818fad71461002d575b5f5ffd5b610047600480360381019061004291906100f1565b610049565b005b7ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb1581604051610078919061012b565b60405180910390a17f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100af919061012b565b60405180910390a150565b5f5ffd5b5f819050919050565b6100d0816100be565b81146100da575f5ffd5b50565b5f813590506100eb816100c7565b92915050565b5f60208284031215610106576101056100ba565b5b5f610113848285016100dd565b91505092915050565b610125816100be565b82525050565b5f60208201905061013e5f83018461011c565b9291505056fea264697066735822122024dd83d4ed45a24e04cc3847bf220951b8e223a67484b966cda4cedca6223a0564736f6c634300081e0033"  # noqa: E501
EVENT_CONTRACT_RUNTIME = "0x608060405234801561000f575f5ffd5b5060043610610029575f3560e01c80635818fad71461002d575b5f5ffd5b610047600480360381019061004291906100f1565b610049565b005b7ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb1581604051610078919061012b565b60405180910390a17f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100af919061012b565b60405180910390a150565b5f5ffd5b5f819050919050565b6100d0816100be565b81146100da575f5ffd5b50565b5f813590506100eb816100c7565b92915050565b5f60208284031215610106576101056100ba565b5b5f610113848285016100dd565b91505092915050565b610125816100be565b82525050565b5f60208201905061013e5f83018461011c565b9291505056fea264697066735822122024dd83d4ed45a24e04cc3847bf220951b8e223a67484b966cda4cedca6223a0564736f6c634300081e0033"  # noqa: E501
EVENT_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleWithIndex",
        "type": "event",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "_arg0", "type": "uint256"}],
        "name": "logTwoEvents",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
EVENT_CONTRACT_DATA = {
    "bytecode": EVENT_CONTRACT_BYTECODE,
    "bytecode_runtime": EVENT_CONTRACT_RUNTIME,
    "abi": EVENT_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/EventContracts.sol:IndexedEventContract
INDEXED_EVENT_CONTRACT_BYTECODE = "0x6080604052348015600e575f5ffd5b506101708061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c80635818fad71461002d575b5f5ffd5b610047600480360381019061004291906100e7565b610049565b005b807ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb1560405160405180910390a27f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100a59190610121565b60405180910390a150565b5f5ffd5b5f819050919050565b6100c6816100b4565b81146100d0575f5ffd5b50565b5f813590506100e1816100bd565b92915050565b5f602082840312156100fc576100fb6100b0565b5b5f610109848285016100d3565b91505092915050565b61011b816100b4565b82525050565b5f6020820190506101345f830184610112565b9291505056fea26469706673582212203cd1266da088b06eb4010c3d410ac280b80cdf191b74b15b1cf76af93679dadb64736f6c634300081e0033"  # noqa: E501
INDEXED_EVENT_CONTRACT_RUNTIME = "0x608060405234801561000f575f5ffd5b5060043610610029575f3560e01c80635818fad71461002d575b5f5ffd5b610047600480360381019061004291906100e7565b610049565b005b807ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb1560405160405180910390a27f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100a59190610121565b60405180910390a150565b5f5ffd5b5f819050919050565b6100c6816100b4565b81146100d0575f5ffd5b50565b5f813590506100e1816100bd565b92915050565b5f602082840312156100fc576100fb6100b0565b5b5f610109848285016100d3565b91505092915050565b61011b816100b4565b82525050565b5f6020820190506101345f830184610112565b9291505056fea26469706673582212203cd1266da088b06eb4010c3d410ac280b80cdf191b74b15b1cf76af93679dadb64736f6c634300081e0033"  # noqa: E501
INDEXED_EVENT_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleWithIndex",
        "type": "event",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "_arg0", "type": "uint256"}],
        "name": "logTwoEvents",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
INDEXED_EVENT_CONTRACT_DATA = {
    "bytecode": INDEXED_EVENT_CONTRACT_BYTECODE,
    "bytecode_runtime": INDEXED_EVENT_CONTRACT_RUNTIME,
    "abi": INDEXED_EVENT_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/EventContracts.sol:AmbiguousEventNameContract
AMBIGUOUS_EVENT_NAME_CONTRACT_BYTECODE = "0x6080604052348015600e575f5ffd5b506102728061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c80635818fad71461002d575b5f5ffd5b61004760048036038101906100429190610119565b610049565b005b7f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100789190610153565b60405180910390a17fe466ad4edc182e32048f6e723b179ae20d1030f298fcfa1e9ad4a759b5a63112816040516020016100b29190610153565b6040516020818303038152906040526100ca906101ae565b6040516100d79190610223565b60405180910390a150565b5f5ffd5b5f819050919050565b6100f8816100e6565b8114610102575f5ffd5b50565b5f81359050610113816100ef565b92915050565b5f6020828403121561012e5761012d6100e2565b5b5f61013b84828501610105565b91505092915050565b61014d816100e6565b82525050565b5f6020820190506101665f830184610144565b92915050565b5f81519050919050565b5f819050602082019050919050565b5f819050919050565b5f6101998251610185565b80915050919050565b5f82821b905092915050565b5f6101b88261016c565b826101c284610176565b90506101cd8161018e565b9250602082101561020d576102087fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff836020036008026101a2565b831692505b5050919050565b61021d81610185565b82525050565b5f6020820190506102365f830184610214565b9291505056fea2646970667358221220606bc1ddfcd8b77dae3483714ddada7ba373cbf1798893d081138811a379ce7364736f6c634300081e0033"  # noqa: E501
AMBIGUOUS_EVENT_NAME_CONTRACT_RUNTIME = "0x608060405234801561000f575f5ffd5b5060043610610029575f3560e01c80635818fad71461002d575b5f5ffd5b61004760048036038101906100429190610119565b610049565b005b7f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100789190610153565b60405180910390a17fe466ad4edc182e32048f6e723b179ae20d1030f298fcfa1e9ad4a759b5a63112816040516020016100b29190610153565b6040516020818303038152906040526100ca906101ae565b6040516100d79190610223565b60405180910390a150565b5f5ffd5b5f819050919050565b6100f8816100e6565b8114610102575f5ffd5b50565b5f81359050610113816100ef565b92915050565b5f6020828403121561012e5761012d6100e2565b5b5f61013b84828501610105565b91505092915050565b61014d816100e6565b82525050565b5f6020820190506101665f830184610144565b92915050565b5f81519050919050565b5f819050602082019050919050565b5f819050919050565b5f6101998251610185565b80915050919050565b5f82821b905092915050565b5f6101b88261016c565b826101c284610176565b90506101cd8161018e565b9250602082101561020d576102087fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff836020036008026101a2565b831692505b5050919050565b61021d81610185565b82525050565b5f6020820190506102365f830184610214565b9291505056fea2646970667358221220606bc1ddfcd8b77dae3483714ddada7ba373cbf1798893d081138811a379ce7364736f6c634300081e0033"  # noqa: E501
AMBIGUOUS_EVENT_NAME_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "bytes32",
                "name": "arg0",
                "type": "bytes32",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "_arg0", "type": "uint256"}],
        "name": "logTwoEvents",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
AMBIGUOUS_EVENT_NAME_CONTRACT_DATA = {
    "bytecode": AMBIGUOUS_EVENT_NAME_CONTRACT_BYTECODE,
    "bytecode_runtime": AMBIGUOUS_EVENT_NAME_CONTRACT_RUNTIME,
    "abi": AMBIGUOUS_EVENT_NAME_CONTRACT_ABI,
}
