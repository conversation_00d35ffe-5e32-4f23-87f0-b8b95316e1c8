"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/OffchainLookup.sol:OffchainLookup
OFFCHAIN_LOOKUP_BYTECODE = "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"  # noqa: E501
OFFCHAIN_LOOKUP_RUNTIME = "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"  # noqa: E501
OFFCHAIN_LOOKUP_ABI = [
    {
        "inputs": [
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "string[]", "name": "urls", "type": "string[]"},
            {"internalType": "bytes", "name": "callData", "type": "bytes"},
            {"internalType": "bytes4", "name": "callbackFunction", "type": "bytes4"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "OffchainLookup",
        "type": "error",
    },
    {
        "inputs": [],
        "name": "continuousOffchainLookup",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "specifiedDataFromTest", "type": "bytes"}
        ],
        "name": "testOffchainLookup",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "result", "type": "bytes"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "testOffchainLookupWithProof",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
OFFCHAIN_LOOKUP_DATA = {
    "bytecode": OFFCHAIN_LOOKUP_BYTECODE,
    "bytecode_runtime": OFFCHAIN_LOOKUP_RUNTIME,
    "abi": OFFCHAIN_LOOKUP_ABI,
}
