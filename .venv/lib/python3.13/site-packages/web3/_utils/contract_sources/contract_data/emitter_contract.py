"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/EmitterContract.sol:EmitterContract
EMITTER_CONTRACT_BYTECODE = "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"  # noqa: E501
EMITTER_CONTRACT_RUNTIME = "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"  # noqa: E501
EMITTER_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "address",
                "name": "arg0",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "arg1",
                "type": "address",
            },
        ],
        "name": "LogAddressIndexed",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "address",
                "name": "arg0",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "arg1",
                "type": "address",
            },
        ],
        "name": "LogAddressNotIndexed",
        "type": "event",
    },
    {"anonymous": True, "inputs": [], "name": "LogAnonymous", "type": "event"},
    {
        "anonymous": False,
        "inputs": [
            {"indexed": False, "internalType": "bytes", "name": "v", "type": "bytes"}
        ],
        "name": "LogBytes",
        "type": "event",
    },
    {
        "anonymous": True,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
        ],
        "name": "LogDoubleAnonymous",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
        ],
        "name": "LogDoubleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
        ],
        "name": "LogDoubleWithIndex",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "string",
                "name": "arg0",
                "type": "string",
            },
            {
                "indexed": False,
                "internalType": "string",
                "name": "arg1",
                "type": "string",
            },
        ],
        "name": "LogDynamicArgs",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "address",
                "name": "indexedAddress",
                "type": "address",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "indexedUint256",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "nonIndexedAddress",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "nonIndexedUint256",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "string",
                "name": "nonIndexedString",
                "type": "string",
            },
        ],
        "name": "LogIndexedAndNotIndexed",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "bytes2[]",
                "name": "arg0",
                "type": "bytes2[]",
            },
            {
                "indexed": False,
                "internalType": "bytes2[]",
                "name": "arg1",
                "type": "bytes2[]",
            },
        ],
        "name": "LogListArgs",
        "type": "event",
    },
    {"anonymous": False, "inputs": [], "name": "LogNoArguments", "type": "event"},
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg3",
                "type": "uint256",
            },
        ],
        "name": "LogQuadrupleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg3",
                "type": "uint256",
            },
        ],
        "name": "LogQuadrupleWithIndex",
        "type": "event",
    },
    {
        "anonymous": True,
        "inputs": [
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleAnonymous",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleWithIndex",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {"indexed": False, "internalType": "string", "name": "v", "type": "string"}
        ],
        "name": "LogString",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256", "name": "b", "type": "uint256"},
                    {
                        "components": [
                            {"internalType": "uint256", "name": "c", "type": "uint256"}
                        ],
                        "internalType": "struct EmitterContract.NestedTestTuple",
                        "name": "nested",
                        "type": "tuple",
                    },
                ],
                "indexed": False,
                "internalType": "struct EmitterContract.TestTuple",
                "name": "arg1",
                "type": "tuple",
            },
        ],
        "name": "LogStructArgs",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
        ],
        "name": "LogTripleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
        ],
        "name": "LogTripleWithIndex",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "arg0", "type": "address"},
            {"internalType": "address", "name": "arg1", "type": "address"},
        ],
        "name": "logAddressIndexedArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "arg0", "type": "address"},
            {"internalType": "address", "name": "arg1", "type": "address"},
        ],
        "name": "logAddressNotIndexedArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes", "name": "v", "type": "bytes"}],
        "name": "logBytes",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {"internalType": "uint256", "name": "arg1", "type": "uint256"},
        ],
        "name": "logDouble",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "string", "name": "arg0", "type": "string"},
            {"internalType": "string", "name": "arg1", "type": "string"},
        ],
        "name": "logDynamicArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "indexedAddress", "type": "address"},
            {"internalType": "uint256", "name": "indexedUint256", "type": "uint256"},
            {"internalType": "address", "name": "nonIndexedAddress", "type": "address"},
            {"internalType": "uint256", "name": "nonIndexedUint256", "type": "uint256"},
            {"internalType": "string", "name": "nonIndexedString", "type": "string"},
        ],
        "name": "logIndexedAndNotIndexedArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes2[]", "name": "arg0", "type": "bytes2[]"},
            {"internalType": "bytes2[]", "name": "arg1", "type": "bytes2[]"},
        ],
        "name": "logListArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            }
        ],
        "name": "logNoArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {"internalType": "uint256", "name": "arg1", "type": "uint256"},
            {"internalType": "uint256", "name": "arg2", "type": "uint256"},
            {"internalType": "uint256", "name": "arg3", "type": "uint256"},
        ],
        "name": "logQuadruple",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
        ],
        "name": "logSingle",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "v", "type": "string"}],
        "name": "logString",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256", "name": "b", "type": "uint256"},
                    {
                        "components": [
                            {"internalType": "uint256", "name": "c", "type": "uint256"}
                        ],
                        "internalType": "struct EmitterContract.NestedTestTuple",
                        "name": "nested",
                        "type": "tuple",
                    },
                ],
                "internalType": "struct EmitterContract.TestTuple",
                "name": "arg1",
                "type": "tuple",
            },
        ],
        "name": "logStruct",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {"internalType": "uint256", "name": "arg1", "type": "uint256"},
            {"internalType": "uint256", "name": "arg2", "type": "uint256"},
        ],
        "name": "logTriple",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
EMITTER_CONTRACT_DATA = {
    "bytecode": EMITTER_CONTRACT_BYTECODE,
    "bytecode_runtime": EMITTER_CONTRACT_RUNTIME,
    "abi": EMITTER_CONTRACT_ABI,
}
