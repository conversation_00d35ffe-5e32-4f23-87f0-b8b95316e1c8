"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.30.
"""

# source: web3/_utils/contract_sources/OffchainResolver.sol:OffchainResolver
OFFCHAIN_RESOLVER_BYTECODE = "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"  # noqa: E501
OFFCHAIN_RESOLVER_RUNTIME = "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"  # noqa: E501
OFFCHAIN_RESOLVER_ABI = [
    {
        "inputs": [
            {"internalType": "string[]", "name": "_urls", "type": "string[]"},
            {"internalType": "address[]", "name": "_signers", "type": "address[]"},
        ],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "string[]", "name": "urls", "type": "string[]"},
            {"internalType": "bytes", "name": "callData", "type": "bytes"},
            {"internalType": "bytes4", "name": "callbackFunction", "type": "bytes4"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "OffchainLookup",
        "type": "error",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "address[]",
                "name": "signers",
                "type": "address[]",
            }
        ],
        "name": "NewSigners",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "target", "type": "address"},
            {"internalType": "uint64", "name": "expires", "type": "uint64"},
            {"internalType": "bytes", "name": "request", "type": "bytes"},
            {"internalType": "bytes", "name": "result", "type": "bytes"},
        ],
        "name": "makeSignatureHash",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "name", "type": "bytes"},
            {"internalType": "bytes", "name": "data", "type": "bytes"},
        ],
        "name": "resolve",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "response", "type": "bytes"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "resolveWithProof",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "address", "name": "", "type": "address"}],
        "name": "signers",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "urls",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
]
OFFCHAIN_RESOLVER_DATA = {
    "bytecode": OFFCHAIN_RESOLVER_BYTECODE,
    "bytecode_runtime": OFFCHAIN_RESOLVER_RUNTIME,
    "abi": OFFCHAIN_RESOLVER_ABI,
}
