from eth_typing import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    HexStr,
)

# Constants as Strings
ADDRESS_ZERO = HexAddress(HexStr("******************************************"))
CHECKSUM_ADDRESSS_ZERO = ChecksumAddress(ADDRESS_ZERO)
MAX_INT = HexStr("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff")
HASH_ZERO = HexStr("******************************************000000000000000000000000")

# Constants as Int
WEI_PER_ETHER = 1000000000000000000

# Grouped constants as <PERSON><PERSON>
DYNAMIC_FEE_TXN_PARAMS = ("maxFeePerGas", "maxPriorityFeePerGas")
