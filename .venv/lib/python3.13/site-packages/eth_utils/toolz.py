try:
    from cytoolz import (
        accumulate,
        assoc,
        assoc_in,
        comp,
        complement,
        compose,
        concat,
        concatv,
        cons,
        count,
        countby,
        curried,
        curry,
        dicttoolz,
        diff,
        dissoc,
        do,
        drop,
        excepts,
        filter,
        first,
        flip,
        frequencies,
        functoolz,
        get,
        get_in,
        groupby,
        identity,
        interleave,
        interpose,
        isdistinct,
        isiterable,
        itemfilter,
        itemmap,
        iterate,
        itertoolz,
        join,
        juxt,
        keyfilter,
        keymap,
        last,
        map,
        mapcat,
        memoize,
        merge,
        merge_sorted,
        merge_with,
        nth,
        partial,
        partition,
        partition_all,
        partitionby,
        peek,
        pipe,
        pluck,
        random_sample,
        recipes,
        reduce,
        reduceby,
        remove,
        second,
        sliding_window,
        sorted,
        tail,
        take,
        take_nth,
        thread_first,
        thread_last,
        topk,
        unique,
        update_in,
        utils,
        valfilter,
        valmap,
    )
except ImportError:
    from toolz import (  # noqa: F401
        accumulate,
        assoc,
        assoc_in,
        comp,
        complement,
        compose,
        concat,
        concatv,
        cons,
        count,
        countby,
        curried,
        curry,
        dicttoolz,
        diff,
        dissoc,
        do,
        drop,
        excepts,
        filter,
        first,
        flip,
        frequencies,
        functoolz,
        get,
        get_in,
        groupby,
        identity,
        interleave,
        interpose,
        isdistinct,
        isiterable,
        itemfilter,
        itemmap,
        iterate,
        itertoolz,
        join,
        juxt,
        keyfilter,
        keymap,
        last,
        map,
        mapcat,
        memoize,
        merge,
        merge_sorted,
        merge_with,
        nth,
        partial,
        partition,
        partition_all,
        partitionby,
        peek,
        pipe,
        pluck,
        random_sample,
        recipes,
        reduce,
        reduceby,
        remove,
        second,
        sliding_window,
        sorted,
        tail,
        take,
        take_nth,
        thread_first,
        thread_last,
        topk,
        unique,
        update_in,
        utils,
        valfilter,
        valmap,
    )
