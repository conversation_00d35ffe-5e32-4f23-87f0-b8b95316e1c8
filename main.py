"""
CarbonLedger API - Secure Production-Ready Implementation

This is the main FastAPI application with comprehensive security, monitoring,
and error handling. Built with enterprise-grade architecture principles.

Architectural Decisions:
- Dependency injection for testability and flexibility
- Comprehensive error handling and logging
- Security-first approach with rate limiting and validation
- Health monitoring and observability
- Configuration-driven setup for multiple environments
"""

from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAP<PERSON>, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from slowapi import Limiter
from slowapi.errors import RateLimitExceeded

# Core modules
from core.config import get_settings, Settings
from core.logging import setup_logging, get_logger
from core.db import DatabaseManager
from core.earth_engine import EarthEngineManager
from core.security import (
    setup_cors, setup_security_middleware, setup_rate_limiting
)
from core.exceptions import setup_exception_handlers
from core.health import HealthChecker

# API modules (to be created)
from api.verification import router as verification_router
from api.health import router as health_router


# Global instances
db_manager: DatabaseManager = None
ee_manager: EarthEngineManager = None
health_checker: HealthChecker = None
limiter: Limiter = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown operations.

    Handles initialization and cleanup of global resources with proper
    error handling and logging.
    """
    logger = get_logger(__name__, component="Application")

    try:
        # Startup
        logger.info("Starting CarbonLedger API application")

        settings = get_settings()

        # Setup logging
        setup_logging(settings.logging)
        logger.info("Logging system initialized")

        # Initialize database
        global db_manager
        db_manager = DatabaseManager(settings.database)
        db_manager.create_tables()
        logger.info("Database initialized successfully")

        # Initialize Earth Engine (skip if no credentials)
        global ee_manager
        try:
            ee_manager = EarthEngineManager(settings.gee)
            logger.info("Google Earth Engine initialized successfully")
        except Exception as e:
            logger.warning(
                "Google Earth Engine initialization failed - continuing without EE",
                error=str(e)
            )
            ee_manager = None

        # Initialize health checker
        global health_checker
        health_checker = HealthChecker(settings)
        health_checker.set_managers(db_manager, ee_manager)
        logger.info("Health checker initialized")

        # Setup rate limiting
        global limiter
        limiter = setup_rate_limiting(settings.security)

        # Setup rate limiting exception handlers now that limiter is available
        from core.security import create_security_exception_handlers
        create_security_exception_handlers(app, limiter)
        logger.info("Rate limiting configured")

        logger.info(
            "CarbonLedger API startup completed successfully",
            environment=settings.environment,
            version=settings.api.version
        )

        yield

    except Exception as e:
        logger.error(
            "Failed to start CarbonLedger API",
            error=str(e),
            error_type=type(e).__name__
        )
        raise

    finally:
        # Shutdown
        logger.info("Shutting down CarbonLedger API application")

        # Cleanup resources if needed
        # (Database connections are handled by connection pooling)

        logger.info("CarbonLedger API shutdown completed")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        Configured FastAPI application instance
    """
    settings = get_settings()

    # Create FastAPI app with metadata
    app = FastAPI(
        title=settings.api.title,
        description=settings.api.description,
        version=settings.api.version,
        lifespan=lifespan,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )

    # Setup CORS
    setup_cors(app, settings.security)

    # Setup security middleware
    setup_security_middleware(app, settings.security)

    # Setup exception handlers
    setup_exception_handlers(app)

    # Include routers
    app.include_router(
        health_router,
        prefix="/health",
        tags=["Health"],
        dependencies=[Depends(get_health_checker)]
    )

    app.include_router(
        verification_router,
        prefix="/api/v1",
        tags=["Verification"],
        dependencies=[Depends(get_db_manager), Depends(get_ee_manager)]
    )

    # Root endpoint
    @app.get("/", response_model=Dict[str, Any])
    async def root():
        """Root endpoint with API information."""
        return {
            "name": settings.api.title,
            "version": settings.api.version,
            "description": settings.api.description,
            "environment": settings.environment,
            "status": "operational",
            "docs_url": "/docs" if settings.debug else None,
            "health_url": "/health"
        }

    return app


# Dependency injection functions
def get_db_manager() -> DatabaseManager:
    """Get database manager instance."""
    if db_manager is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database not available"
        )
    return db_manager


def get_ee_manager() -> EarthEngineManager:
    """Get Earth Engine manager instance."""
    if ee_manager is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Earth Engine not available"
        )
    return ee_manager


def get_health_checker() -> HealthChecker:
    """Get health checker instance."""
    if health_checker is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Health checker not available"
        )
    return health_checker


# Create the application
app = create_application()


# Development server entry point
if __name__ == "__main__":
    import uvicorn

    settings = get_settings()

    uvicorn.run(
        "main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.reload,
        workers=settings.api.workers if not settings.api.reload else 1,
        log_level=settings.logging.level.lower(),
        access_log=settings.logging.enable_request_logging,
    )