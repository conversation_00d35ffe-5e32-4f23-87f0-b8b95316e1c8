# Step 1: Import the necessary libraries
from fastapi import FastAPI
from pydantic import BaseModel
import ee
import geemap

# --- API SETUP ---

# Initialize our FastAPI application
app = FastAPI()

# Define the data model for our incoming requests using Pydantic.
# This ensures that any request to our API must contain these four values.
class VerificationRequest(BaseModel):
    lon_min: float
    lat_min: float
    lon_max: float
    lat_max: float

# --- CORE LOGIC (Refactored from the previous script) ---

# This function contains the core satellite analysis logic.
# It now takes a bounding box (the Area of Interest) as an argument.
def analyze_deforestation(aoi: ee.Geometry.Rectangle):
    """
    Analyzes deforestation within a given Area of Interest (AOI).
    Returns True if deforestation is detected, False otherwise.
    """
    try:
        ee.Initialize()

        def calculate_ndvi(image):
            return image.normalizedDifference(['B8', 'B4']).rename('NDVI')

        image_collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')

        # Use the dates from the past two full years for a reliable comparison.
        # This makes the analysis robust regardless of when it's run.
        image_before = image_collection.filterBounds(aoi) \
                                       .filterDate('2023-01-01', '2023-12-31') \
                                       .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20)) \
                                       .median().clip(aoi)

        image_after = image_collection.filterBounds(aoi) \
                                      .filterDate('2024-01-01', '2024-12-31') \
                                      .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20)) \
                                      .median().clip(aoi)

        ndvi_before = calculate_ndvi(image_before)
        ndvi_after = calculate_ndvi(image_after)

        # Calculate the change in vegetation.
        ndvi_difference = ndvi_after.subtract(ndvi_before)

        # Define a threshold for what we consider "significant" deforestation.
        # We look for areas where the NDVI value decreased by more than 20% (0.2).
        deforestation_threshold = -0.2
        deforestation_areas = ndvi_difference.lt(deforestation_threshold)

        # Check if any such areas exist. We get statistics from the area,
        # specifically the sum of pixels that match our deforestation criteria.
        stats = deforestation_areas.reduceRegion(
            reducer=ee.Reducer.sum(),
            geometry=aoi,
            scale=30, # Resolution in meters
            maxPixels=1e9
        ).get('NDVI').getInfo()

        # If the sum of deforestation pixels is greater than zero, it means we found deforestation.
        return stats > 0

    except Exception as e:
        print(f"An error occurred during Earth Engine analysis: {e}")
        return False # Default to False if any error occurs

# --- API ENDPOINT ---

# This defines our main API endpoint. It listens for POST requests at the "/verify" URL.
@app.post("/verify")
def verify_project(request: VerificationRequest):
    """
    Receives project coordinates, runs the analysis, and returns the result.
    """
    print(f"Received verification request for coordinates: {request.dict()}")

    # Create an Earth Engine Geometry object from the incoming request data.
    area_to_check = ee.Geometry.Rectangle(
        [request.lon_min, request.lat_min, request.lon_max, request.lat_max]
    )

    # Run the analysis function.
    deforestation_found = analyze_deforestation(area_to_check)

    # Return the result as a JSON response.
    return {
        "verification_complete": True,
        "area_checked": request.dict(),
        "deforestation_detected": deforestation_found
    }