# Step 1: Import the necessary libraries
import ee
import geemap

# --- CONFIGURATION ---
# We will focus on a real area in the Brazilian Amazon (Rondônia) known for deforestation.
# You can change these coordinates later to analyze any project in the world.
AREA_OF_INTEREST = ee.Geometry.Rectangle([-63.0, -10.0, -62.0, -9.0])

# Define the time periods we want to compare. Let's compare the first half
# of last year (2024) to the first half of this year (2025).
START_DATE_BEFORE = '2024-01-01'
END_DATE_BEFORE = '2024-06-30'
START_DATE_AFTER = '2025-01-01'
END_DATE_AFTER = '2025-06-30'


# --- CORE LOGIC ---

print("Initializing Google Earth Engine...")
# This command connects to Google's servers.
ee.Initialize()

print("Defining the vegetation analysis function (NDVI)...")
# The Normalized Difference Vegetation Index (NDVI) is a standard way to measure plant health.
# Healthy vegetation reflects more near-infrared (NIR) light and less red light.
# This function calculates (NIR - Red) / (NIR + Red) for each pixel in an image.
# For the Sentinel-2 satellite, Band 8 is NIR and Band 4 is Red.
def calculate_ndvi(image):
    return image.normalizedDifference(['B8', 'B4']).rename('NDVI')

print("Fetching satellite imagery...")
# This is our connection to the satellite data. We are using the Copernicus Sentinel-2 dataset,
# which provides high-resolution (10-meter) images of the entire planet every few days.
image_collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')

# Get the "before" image: Filter the entire collection by our dates and area,
# remove cloudy images (less than 20% cloud cover), take the median pixel value
# to create one clean composite image, and then clip it to our exact area.
image_before = image_collection.filterBounds(AREA_OF_INTEREST) \
                               .filterDate(START_DATE_BEFORE, END_DATE_BEFORE) \
                               .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20)) \
                               .median() \
                               .clip(AREA_OF_INTEREST)

# Get the "after" image using the same process for the new dates.
image_after = image_collection.filterBounds(AREA_OF_INTEREST) \
                             .filterDate(START_DATE_AFTER, END_DATE_AFTER) \
                             .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20)) \
                             .median() \
                             .clip(AREA_OF_INTEREST)

print("Calculating vegetation changes...")
# Calculate the NDVI for both our "before" and "after" images.
ndvi_before = calculate_ndvi(image_before)
ndvi_after = calculate_ndvi(image_after)

# Calculate the difference in vegetation.
# Pixels where vegetation was lost will have negative values.
# Pixels where vegetation grew will have positive values.
ndvi_difference = ndvi_after.subtract(ndvi_before)

print("Generating the verification map...")
# Create an interactive map centered on our Area of Interest.
Map = geemap.Map()
Map.centerObject(AREA_OF_INTEREST, 9) # Zoom level 9

# Define how we want to color the map.
# We will show areas with significant vegetation loss in bright red.
# Areas with significant gain (reforestation) will be bright green.
palette = [
    'FF0000',  # Strong loss (red)
    '000000',  # No change (black)
    '00FF00'   # Strong gain (green)
]
vis_params = {'min': -0.5, 'max': 0.5, 'palette': palette}

# Add our calculated difference layer to the map.
Map.addLayer(ndvi_difference, vis_params, 'Vegetation Change (Red=Loss, Green=Gain)')
Map.addLayerControl() # Adds a layer control widget to the map.

# Display the map. This will either open in your browser or display inline
# if you are using a Jupyter Notebook.
Map

print("\nVerification map generated. Look for red areas indicating deforestation.")
print("This visual proof is the foundation of CarbonLedger's trust.")