// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

/**
 * @title CarbonCreditToken
 * @dev ERC-1155 based carbon credit utility tokens with retirement functionality
 * 
 * Architectural Decision: ERC-1155 for gas efficiency and metadata support
 * Regulatory Positioning: Utility tokens for carbon retirement services
 * 
 * Key Features:
 * - Satellite-verified carbon credits
 * - Built-in retirement mechanism
 * - Non-transferable after retirement
 * - Public retirement registry
 * - Compliance-focused design
 */
contract CarbonCreditToken is ERC1155, AccessControl, Pausable, ReentrancyGuard {
    using Counters for Counters.Counter;
    
    // Role definitions
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");
    bytes32 public constant VERIFIER_ROLE = keccak256("VERIFIER_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    
    // Token ID counter
    Counters.Counter private _tokenIdCounter;
    
    // Carbon credit metadata structure
    struct CarbonCredit {
        string projectName;
        string projectDescription;
        uint256 totalSupply;
        uint256 retiredAmount;
        uint256 verificationTimestamp;
        string verificationId;
        string satelliteData; // IPFS hash or verification details
        bool isActive;
        address projectDeveloper;
    }
    
    // Retirement record structure
    struct RetirementRecord {
        uint256 tokenId;
        uint256 amount;
        address retiree;
        uint256 timestamp;
        string reason; // Retirement purpose/reason
        string beneficiary; // On behalf of whom
    }
    
    // Storage mappings
    mapping(uint256 => CarbonCredit) public carbonCredits;
    mapping(uint256 => RetirementRecord[]) public retirementHistory;
    mapping(address => uint256[]) public userRetirements;
    
    // Retired token tracking (non-transferable)
    mapping(uint256 => mapping(address => uint256)) public retiredBalances;
    
    // Events
    event CarbonCreditMinted(
        uint256 indexed tokenId,
        address indexed projectDeveloper,
        uint256 amount,
        string projectName,
        string verificationId
    );
    
    event CarbonCreditRetired(
        uint256 indexed tokenId,
        address indexed retiree,
        uint256 amount,
        string reason,
        string beneficiary
    );
    
    event CarbonCreditVerified(
        uint256 indexed tokenId,
        string verificationId,
        uint256 timestamp
    );
    
    /**
     * @dev Constructor sets up roles and initial configuration
     */
    constructor(string memory uri) ERC1155(uri) {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(MINTER_ROLE, msg.sender);
        _grantRole(VERIFIER_ROLE, msg.sender);
        _grantRole(PAUSER_ROLE, msg.sender);
        
        // Start token IDs at 1
        _tokenIdCounter.increment();
    }
    
    /**
     * @dev Mint new carbon credits (only by authorized minters)
     * @param to Address to mint tokens to (project developer)
     * @param amount Amount of carbon credits to mint
     * @param projectName Name of the carbon project
     * @param projectDescription Description of the project
     * @param verificationId Unique verification ID from satellite analysis
     * @param satelliteData IPFS hash or verification details
     */
    function mintCarbonCredit(
        address to,
        uint256 amount,
        string memory projectName,
        string memory projectDescription,
        string memory verificationId,
        string memory satelliteData
    ) public onlyRole(MINTER_ROLE) whenNotPaused nonReentrant returns (uint256) {
        require(to != address(0), "Cannot mint to zero address");
        require(amount > 0, "Amount must be greater than zero");
        require(bytes(projectName).length > 0, "Project name required");
        require(bytes(verificationId).length > 0, "Verification ID required");
        
        uint256 tokenId = _tokenIdCounter.current();
        _tokenIdCounter.increment();
        
        // Store carbon credit metadata
        carbonCredits[tokenId] = CarbonCredit({
            projectName: projectName,
            projectDescription: projectDescription,
            totalSupply: amount,
            retiredAmount: 0,
            verificationTimestamp: block.timestamp,
            verificationId: verificationId,
            satelliteData: satelliteData,
            isActive: true,
            projectDeveloper: to
        });
        
        // Mint the tokens
        _mint(to, tokenId, amount, "");
        
        emit CarbonCreditMinted(tokenId, to, amount, projectName, verificationId);
        
        return tokenId;
    }
    
    /**
     * @dev Retire carbon credits (permanent, non-transferable)
     * @param tokenId Token ID to retire
     * @param amount Amount to retire
     * @param reason Reason for retirement
     * @param beneficiary On behalf of whom (optional)
     */
    function retireCarbonCredit(
        uint256 tokenId,
        uint256 amount,
        string memory reason,
        string memory beneficiary
    ) public whenNotPaused nonReentrant {
        require(amount > 0, "Amount must be greater than zero");
        require(balanceOf(msg.sender, tokenId) >= amount, "Insufficient balance");
        require(carbonCredits[tokenId].isActive, "Token not active");
        require(bytes(reason).length > 0, "Retirement reason required");
        
        // Burn the transferable tokens
        _burn(msg.sender, tokenId, amount);
        
        // Track retired amount
        retiredBalances[tokenId][msg.sender] += amount;
        carbonCredits[tokenId].retiredAmount += amount;
        
        // Record retirement
        RetirementRecord memory retirement = RetirementRecord({
            tokenId: tokenId,
            amount: amount,
            retiree: msg.sender,
            timestamp: block.timestamp,
            reason: reason,
            beneficiary: beneficiary
        });
        
        retirementHistory[tokenId].push(retirement);
        userRetirements[msg.sender].push(tokenId);
        
        emit CarbonCreditRetired(tokenId, msg.sender, amount, reason, beneficiary);
    }
    
    /**
     * @dev Update verification data (only by verifiers)
     */
    function updateVerification(
        uint256 tokenId,
        string memory verificationId,
        string memory satelliteData
    ) public onlyRole(VERIFIER_ROLE) {
        require(carbonCredits[tokenId].isActive, "Token not active");
        
        carbonCredits[tokenId].verificationId = verificationId;
        carbonCredits[tokenId].satelliteData = satelliteData;
        carbonCredits[tokenId].verificationTimestamp = block.timestamp;
        
        emit CarbonCreditVerified(tokenId, verificationId, block.timestamp);
    }
    
    /**
     * @dev Get carbon credit details
     */
    function getCarbonCredit(uint256 tokenId) public view returns (CarbonCredit memory) {
        return carbonCredits[tokenId];
    }
    
    /**
     * @dev Get retirement history for a token
     */
    function getRetirementHistory(uint256 tokenId) public view returns (RetirementRecord[] memory) {
        return retirementHistory[tokenId];
    }
    
    /**
     * @dev Get user's retired balance for a token
     */
    function getRetiredBalance(address user, uint256 tokenId) public view returns (uint256) {
        return retiredBalances[tokenId][user];
    }
    
    /**
     * @dev Get user's retirement token IDs
     */
    function getUserRetirements(address user) public view returns (uint256[] memory) {
        return userRetirements[user];
    }
    
    /**
     * @dev Pause contract (emergency)
     */
    function pause() public onlyRole(PAUSER_ROLE) {
        _pause();
    }
    
    /**
     * @dev Unpause contract
     */
    function unpause() public onlyRole(PAUSER_ROLE) {
        _unpause();
    }
    
    /**
     * @dev Override required by Solidity
     */
    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC1155, AccessControl)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }
    
    /**
     * @dev Override to prevent transfers of retired tokens
     */
    function _beforeTokenTransfer(
        address operator,
        address from,
        address to,
        uint256[] memory ids,
        uint256[] memory amounts,
        bytes memory data
    ) internal override whenNotPaused {
        super._beforeTokenTransfer(operator, from, to, ids, amounts, data);
    }
}
