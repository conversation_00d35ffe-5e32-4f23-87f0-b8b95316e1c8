export const tokens: Record<string, string> = {
  "1": "pragma",
  "2": ";",
  "3": "*",
  "4": "||",
  "5": "^",
  "6": "~",
  "7": ">=",
  "8": ">",
  "9": "<",
  "10": "<=",
  "11": "=",
  "12": "as",
  "13": "import",
  "14": "from",
  "15": "{",
  "16": ",",
  "17": "}",
  "18": "abstract",
  "19": "contract",
  "20": "interface",
  "21": "library",
  "22": "is",
  "23": "(",
  "24": ")",
  "25": "layout",
  "26": "at",
  "27": "error",
  "28": "using",
  "29": "for",
  "30": "|",
  "31": "&",
  "32": "+",
  "33": "-",
  "34": "/",
  "35": "%",
  "36": "==",
  "37": "!=",
  "38": "struct",
  "39": "modifier",
  "40": "function",
  "41": "returns",
  "42": "event",
  "43": "enum",
  "44": "[",
  "45": "]",
  "46": "address",
  "47": ".",
  "48": "mapping",
  "49": "=>",
  "50": "memory",
  "51": "storage",
  "52": "calldata",
  "53": "if",
  "54": "else",
  "55": "try",
  "56": "catch",
  "57": "while",
  "58": "unchecked",
  "59": "assembly",
  "60": "do",
  "61": "return",
  "62": "throw",
  "63": "emit",
  "64": "revert",
  "65": "var",
  "66": "bool",
  "67": "string",
  "68": "byte",
  "69": "++",
  "70": "--",
  "71": "new",
  "72": ":",
  "73": "delete",
  "74": "!",
  "75": "**",
  "76": "<<",
  "77": ">>",
  "78": "&&",
  "79": "?",
  "80": "|=",
  "81": "^=",
  "82": "&=",
  "83": "<<=",
  "84": ">>=",
  "85": "+=",
  "86": "-=",
  "87": "*=",
  "88": "/=",
  "89": "%=",
  "90": "let",
  "91": ":=",
  "92": "=:",
  "93": "switch",
  "94": "case",
  "95": "default",
  "96": "->",
  "97": "callback",
  "98": "override",
  "99": "Int",
  "100": "Uint",
  "101": "Byte",
  "102": "Fixed",
  "103": "Ufixed",
  "104": "BooleanLiteral",
  "105": "DecimalNumber",
  "106": "HexNumber",
  "107": "NumberUnit",
  "108": "HexLiteralFragment",
  "109": "ReservedKeyword",
  "110": "anonymous",
  "111": "break",
  "112": "constant",
  "113": "transient",
  "114": "immutable",
  "115": "continue",
  "116": "leave",
  "117": "external",
  "118": "indexed",
  "119": "internal",
  "120": "payable",
  "121": "private",
  "122": "public",
  "123": "virtual",
  "124": "pure",
  "125": "type",
  "126": "view",
  "127": "global",
  "128": "constructor",
  "129": "fallback",
  "130": "receive",
  "131": "Identifier",
  "132": "StringLiteralFragment",
  "133": "VersionLiteral",
  "134": "WS",
  "135": "COMMENT",
  "136": "LINE_COMMENT"
}