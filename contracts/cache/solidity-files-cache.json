{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/cl/contracts/contracts/CarbonCreditToken.sol": {"lastModificationDate": 1751558811883, "contentHash": "6ee56cfbb6cd3122cfde6051ac54b081", "sourceName": "contracts/CarbonCreditToken.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC1155/ERC1155.sol", "@openzeppelin/contracts/access/AccessControl.sol", "@openzeppelin/contracts/security/Pausable.sol", "@openzeppelin/contracts/security/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Counters.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["CarbonCreditToken"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/access/AccessControl.sol": {"lastModificationDate": 1751558758170, "contentHash": "a2b1ec38a8dad325a596f926890772b8", "sourceName": "@openzeppelin/contracts/access/AccessControl.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IAccessControl.sol", "../utils/Context.sol", "../utils/Strings.sol", "../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["AccessControl"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/Counters.sol": {"lastModificationDate": 1751558758206, "contentHash": "74654e3ae5d7f39555055dfe244dab7a", "sourceName": "@openzeppelin/contracts/utils/Counters.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Counters"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/security/ReentrancyGuard.sol": {"lastModificationDate": 1751558758358, "contentHash": "1535f8c0c68463f8c1b5239f7584e71f", "sourceName": "@openzeppelin/contracts/security/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/token/ERC1155/ERC1155.sol": {"lastModificationDate": 1751558758229, "contentHash": "65c9d9c87e2b4df70e723819774a0482", "sourceName": "@openzeppelin/contracts/token/ERC1155/ERC1155.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC1155.sol", "./IERC1155Receiver.sol", "./extensions/IERC1155MetadataURI.sol", "../../utils/Address.sol", "../../utils/Context.sol", "../../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC1155"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/security/Pausable.sol": {"lastModificationDate": 1751558758356, "contentHash": "25c8108f36fdd472bc78d4c4af240c11", "sourceName": "@openzeppelin/contracts/security/Pausable.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Pausable"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/access/IAccessControl.sol": {"lastModificationDate": 1751558758297, "contentHash": "57c84298234411cea19c7c284d86be8b", "sourceName": "@openzeppelin/contracts/access/IAccessControl.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IAccessControl"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/Strings.sol": {"lastModificationDate": 1751558758367, "contentHash": "48686fc32a22a3754b8e63321857dd2a", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Strings"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1751558758206, "contentHash": "f07feb4a44b1a4872370da5aa70e8e46", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Context"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol": {"lastModificationDate": 1751558758239, "contentHash": "0e7db055ce108f9da7bb6686a00287c0", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC165"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"lastModificationDate": 1751558758353, "contentHash": "fe63409d8a06818b926cf89e0ea88b1b", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Math"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"lastModificationDate": 1751558758365, "contentHash": "9488ebd4daacfee8ad04811600d7d061", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["SignedMath"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"lastModificationDate": 1751558758328, "contentHash": "03e6768535ac4da0e9756f1d8a4a018a", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC165"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/utils/Address.sol": {"lastModificationDate": 1751558758198, "contentHash": "211ffd288c1588ba8c10eae668ca3c66", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.1"], "artifacts": ["Address"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/token/ERC1155/IERC1155.sol": {"lastModificationDate": 1751558758315, "contentHash": "93aa9af4f5d2d2095a71ccde917038bb", "sourceName": "@openzeppelin/contracts/token/ERC1155/IERC1155.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1155"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol": {"lastModificationDate": 1751558758322, "contentHash": "9f8822b72fe2702979e40160cb6d9636", "sourceName": "@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1155Receiver"]}, "/Users/<USER>/cl/contracts/node_modules/@openzeppelin/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol": {"lastModificationDate": 1751558758320, "contentHash": "9148c2e10c4efb12c71a7f080da5559b", "sourceName": "@openzeppelin/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "solcConfig": {"version": "0.8.19", "settings": {"optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC1155.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1155MetadataURI"]}}}